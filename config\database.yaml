# 电信数据源配置
# 专门用于电信数据导入和处理的配置文件

# 电信数据源规则
telecom_data_sources:
  # EP (工程参数) 配置
  ep:
    schema_name: ep_to2
    file_extensions: ['.xlsx', '.xls']
    skip_rows: 1
    header_row: 1
    table_name_pattern: 'ep_{cell_type}_{year}_cw{week}'
    coordinate_validation:
      latitude_range: [-90, 90]
      longitude_range: [-180, 180]
    batch_processing:
      batch_size: 5000
      parallel_workers: 6

  # CDR (通话详单记录) 配置
  cdr:
    file_extensions: ['.xlsx', '.xls']
    skip_rows: 2
    header_row: 2
    table_name_pattern: 'cdr_{year}{quarter}_{service_type}'
    # 运营商特定模式映射
    operator_schema_mapping:
      telefonica: cdr_to2
      vodafone: cdr_vdf
      telekom: cdr_tdg

    batch_processing:
      batch_size: 10000
      parallel_workers: 8
      # Memory management settings
      memory_threshold_mb: 2048  # Increased from 1024 to prevent unnecessary warnings
      memory_per_worker_mb: 300  # Estimated memory usage per worker
      # Dynamic scaling settings
      enable_dynamic_scaling: true
      min_workers: 2
      max_workers: 12
    geospatial:
      enabled: true
      coordinate_system: 'EPSG:4326'

  # NLG (网络位置地理) 配置
  nlg:
    schema_name: nlg_to2
    file_extensions: ['.xlsb']
    sheet_name: 'Techno_2G_4G_5G'
    skip_rows: 4
    header_row: 4
    table_name_pattern: 'nlg_cube_aktuell_{date}'

    technologies: ['2G', '3G', '4G', '5G']
    batch_processing:
      batch_size: 1000  # 较小批次用于地理空间数据
      parallel_workers: 6
    geospatial:
      enabled: true
      geometry_types: ['POINT', 'POLYGON']
      coordinate_system: 'EPSG:4326'
      create_spatial_index: true

  # KPI (关键性能指标) 配置
  kpi:
    schema_name: kpi_to2
    file_extensions: ['.xlsx', '.xls', '.csv']
    table_name_pattern: 'kpi_{metric_type}_{year}'
    batch_processing:
      batch_size: 15000
      parallel_workers: 8
    validation:
      metric_value_range: [-1000000, 1000000]
      timestamp_format: 'ISO8601'

  # CFG (配置数据) 配置
  cfg:
    schema_name: cfg_to2
    file_extensions: ['.xml', '.tar.gz']
    table_name_pattern: 'cfg_{year}_{week}'
    compressed_format: 'tar.gz'
    extract_to_format: 'xml'
    batch_processing:
      batch_size: 2000
      parallel_workers: 4

  # Score (评分数据) 配置
  score:
    schema_name: score_to2
    file_extensions: ['.xlsx', '.xls', '.csv']
    skip_rows: 2
    header_row: 2
    table_name_pattern: 'score_{year}{quarter}'
    batch_processing:
      batch_size: 8000
      parallel_workers: 6

# 列处理规则
column_processing:
  normalize_names: true
  lowercase: true
  replace_special_chars: true
  max_length: 63
  reserved_words_handling: 'prefix_underscore'

# 表设计标准
table_schema:
  primary_key:
    name: id
    type: BIGSERIAL
  timestamps:
    created_at:
      type: TIMESTAMP WITH TIME ZONE
      default: CURRENT_TIMESTAMP
    updated_at:
      type: TIMESTAMP WITH TIME ZONE
      default: CURRENT_TIMESTAMP
  default_column_type: TEXT
  indexes:
    auto_create_timestamp_index: true
    auto_create_foreign_key_index: true

# 表命名约定
naming_convention:
  table: "snake_case"
  column: "snake_case"
  index: "idx_{table}_{column}"
  constraint: "chk_{table}_{column}"
  foreign_key: "fk_{table}_{column}"
