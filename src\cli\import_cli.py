#!/usr/bin/env python3
"""
Enhanced Connect Telecommunications Data Import CLI

Comprehensive command-line interface for importing telecommunications data with
advanced features, performance optimization, and unified architecture.

Features:
- Unified import interface for all telecom data types
- Intelligent batch processing with memory optimization
- Real-time progress tracking and performance monitoring
- Automatic schema detection and table creation
- Multi-operator support (Telefonica, Vodafone, Telekom)
- Advanced data validation and error reporting
- Web API integration ready
- Comprehensive logging and audit trails

Author: Vincent.Li
Email: <EMAIL>
Version: 2.0.0
"""

import asyncio
import logging
import sys
import time
import traceback
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

import click
from rich.console import Console
from rich.progress import (
    BarColumn,
    MofNCompleteColumn,
    Progress,
    SpinnerColumn,
    TaskProgressColumn,
    TextColumn,
    TimeElapsedColumn,
    TimeRemainingColumn,
)
from rich.table import Table
from rich.panel import Panel
from rich.text import Text
from rich.layout import Layout
from rich.live import Live

# Import our modules
sys.path.append(str(Path(__file__).parent.parent.parent))

# Import unified importer architecture
from src.importers import (
    create_importer,
    ALL_IMPORTERS,
    ALL_CONFIGS,
    TELECOM_DATA_TYPES,
    TelecomImportError
)

# Import enhanced logging and performance optimization
from .enhanced_logging import setup_enhanced_logging, log_import_metrics, log_performance_summary
from .performance_optimizer import PerformanceOptimizer
from src.importers.base import ImportResult
from src.importers.base import ImportStatus as BaseImportStatus

# Import database and configuration
from src.database.operations.database_manager import DatabaseManager
from src.database.schema.manager import SchemaManager
from src.database.connection.pool import get_pool_manager, initialize_global_pool
from src.config import get_config
from src.core.utils.logging import get_logger, setup_logging

# Initialize console and logger
console = Console()
logger = get_logger(__name__)

# Supported data types and their configurations
SUPPORTED_DATA_TYPES = {
    'cdr': {
        'description': 'Call Detail Records',
        'schemas': ['cdr_to2', 'cdr_vdf', 'cdr_tdg'],
        'operators': ['telefonica', 'vodafone', 'telekom'],
        'extensions': ['.csv', '.xlsx', '.xls'],
        'batch_size': 10000
    },
    'ep': {
        'description': 'Engineering Parameters',
        'schemas': ['ep_to2'],
        'operators': ['telefonica', 'qgis'],
        'extensions': ['.xlsx', '.xls', '.csv'],
        'batch_size': 5000
    },
    'nlg': {
        'description': 'Network Location Geography',
        'schemas': ['nlg_to2'],
        'operators': ['telefonica'],
        'extensions': ['.csv', '.xlsx', '.xls', '.xlsb'],
        'batch_size': 3000
    },
    'kpi': {
        'description': 'Key Performance Indicators',
        'schemas': ['kpi_to2'],
        'operators': ['telefonica'],
        'extensions': ['.csv', '.xlsx', '.xls'],
        'batch_size': 8000
    },
    'cfg': {
        'description': 'Configuration Data',
        'schemas': ['cfg_to2'],
        'operators': ['telefonica'],
        'extensions': ['.xlsx', '.xls'],
        'batch_size': 2000
    },
    'score': {
        'description': 'Scoring/Benchmark Data',
        'schemas': ['score_to2'],
        'operators': ['telefonica'],
        'extensions': ['.xlsx', '.xls'],
        'batch_size': 5000
    }
}

# Default schemas mapping
DEFAULT_SCHEMAS = {
    'cdr': 'cdr_to2',
    'ep': 'ep_to2',
    'nlg': 'nlg_to2',
    'kpi': 'kpi_to2',
    'cfg': 'cfg_to2',
    'score': 'score_to2'
}


class ImportContext:
    """Context for import operations with shared resources."""

    def __init__(self):
        self.config = None
        self.db_manager = None
        self.schema_manager = None
        self.pool_manager = None
        self.import_manager = None
        self.performance_stats = {
            'total_files': 0,
            'successful_imports': 0,
            'failed_imports': 0,
            'total_records': 0,
            'start_time': None,
            'end_time': None
        }

    async def initialize(self):
        """Initialize the import context."""
        try:
            # Load configuration
            self.config = get_config()

            # Initialize database connection pool
            self.pool_manager = get_pool_manager(self.config)
            await self.pool_manager.initialize_pool()

            # Initialize database manager
            self.db_manager = DatabaseManager(self.pool_manager)

            # Initialize schema manager
            self.schema_manager = SchemaManager(self.pool_manager)

            # Initialize unified import manager
            from src.importers.import_manager import ImportManager
            self.import_manager = ImportManager()
            await self.import_manager.initialize()

            # Ensure required schemas exist
            await self._ensure_schemas_exist()

            logger.info("Import context initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize import context: {e}")
            raise

    async def _ensure_schemas_exist(self):
        """Ensure all required schemas exist."""
        required_schemas = set()
        for data_type_config in SUPPORTED_DATA_TYPES.values():
            required_schemas.update(data_type_config['schemas'])

        for schema_name in required_schemas:
            try:
                if not await self.schema_manager.schema_exists(schema_name):
                    await self.schema_manager.create_schema(schema_name)
                    logger.info(f"Created schema: {schema_name}")
            except Exception as e:
                logger.warning(f"Failed to create schema {schema_name}: {e}")

    async def cleanup(self):
        """Cleanup resources."""
        if self.import_manager:
            await self.import_manager.cleanup()
        if self.pool_manager:
            await self.pool_manager.close_pool()


def auto_detect_data_type(file_path: Path) -> Optional[str]:
    """Auto-detect data type based on file path and name."""
    filename_lower = file_path.name.lower()
    parent_path_lower = str(file_path.parent).lower()
    
    # Check by parent directory patterns
    for data_type in SUPPORTED_DATA_TYPES.keys():
        if data_type in parent_path_lower:
            return data_type
    
    # Check by filename patterns
    patterns = {
        'cdr': ['cdr', 'call', 'voice', 'conversational'],
        'ep': ['gsmcell', 'ltecell', 'nrcell', 'tef_site', 'engineering', 'ep'],
        'nlg': ['nlg_cube', 'neighbor', 'location', 'nlg'],
        'kpi': ['kpi', 'performance', 'indicator'],
        'cfg': ['bkpfileof', 'config', 'cfg'],
        'score': ['benchmark', 'score', 'quality', 'flattable']
    }
    
    for data_type, type_patterns in patterns.items():
        if any(pattern in filename_lower for pattern in type_patterns):
            return data_type
    
    return None


def detect_operator(file_path: Path, skip_excel_analysis: bool = False) -> Optional[str]:
    """Detect operator based on file path and Excel sheet analysis using OperatorDetector.

    Args:
        file_path: Path to the file
        skip_excel_analysis: Skip Excel sheet analysis for performance (used in preview mode)
    """
    try:
        from src.database.utils.table_naming import OperatorDetector
        
        # Create detector with empty config as fallback
        detector = OperatorDetector({})
        
        # Try to detect from filename first
        filename = file_path.name
        operator = detector.detect_operator_from_sheet(filename)
        
        if operator:
            return operator
        
        # For CDR Excel files, try sheet-based detection (only if not skipping)
        if not skip_excel_analysis and file_path.suffix.lower() in ['.xlsx', '.xls'] and 'cdr' in str(file_path).lower():
            detected_operator = detect_operator_from_excel_sheets(file_path)
            if detected_operator:
                return detected_operator
        
        return 'telefonica'  # Default
        
    except Exception as e:
        # Fallback to original logic if OperatorDetector fails
        path_str = str(file_path).lower()
        
        if any(pattern in path_str for pattern in ['vdf', 'vodafone']):
            return 'vodafone'
        elif any(pattern in path_str for pattern in ['tdg', 'telekom']):
            return 'telekom'
        elif any(pattern in path_str for pattern in ['to2', 'telefonica']):
            return 'telefonica'
        elif any(pattern in path_str for pattern in ['qgis', 'gsmcell', 'ltecell', 'nrcell']):
            return 'qgis'
        
        return 'telefonica'  # Default


def detect_operator_from_excel_sheets(file_path: Path) -> Optional[str]:
    """Detect operator by analyzing Excel sheet names using OperatorDetector."""
    try:
        import pandas as pd
        from src.database.utils.table_naming import OperatorDetector

        # Read Excel file to get sheet names
        excel_file = pd.ExcelFile(file_path)
        sheet_names = excel_file.sheet_names
        
        # Create detector with empty config as fallback
        detector = OperatorDetector({})

        # Check each sheet name for operator patterns using OperatorDetector
        for sheet_name in sheet_names:
            operator = detector.detect_operator_from_sheet(sheet_name)
            if operator:
                return operator

        return None

    except Exception as e:
        # If Excel reading or OperatorDetector fails, fall back to original logic
        try:
            import pandas as pd
            
            excel_file = pd.ExcelFile(file_path)
            sheet_names = [name.lower() for name in excel_file.sheet_names]

            # Define operator patterns for sheet name matching
            operator_patterns = {
                'telefonica': ['telefonica', 'tef', 'o2'],
                'vodafone': ['vodafone', 'vdf', 'vf'],
                'telekom': ['telekom', 'tdg', 'dt', 'deutsche']
            }

            # Check each sheet name for operator patterns
            for operator, patterns in operator_patterns.items():
                for sheet_name in sheet_names:
                    for pattern in patterns:
                        if pattern in sheet_name:
                            return operator
            return None
        except:
            return None


def get_schema_for_data_type(data_type: str, operator: Optional[str] = None) -> str:
    """Get appropriate schema for data type and operator using OperatorDetector."""
    if data_type == 'cdr' and operator:
        try:
            from src.database.utils.table_naming import OperatorDetector
            
            # Create detector with empty config as fallback
            detector = OperatorDetector({})
            return detector.get_schema_for_operator(operator, data_type)
        except Exception as e:
            # Fallback to hardcoded mapping
            operator_schemas = {
                'telefonica': 'cdr_to2',
                'vodafone': 'cdr_vdf',
                'telekom': 'cdr_tdg'
            }
            return operator_schemas.get(operator, 'cdr_to2')
    
    return DEFAULT_SCHEMAS.get(data_type, 'public')


async def import_single_file(
    ctx: ImportContext,
    file_path: Path,
    data_type: Optional[str] = None,
    schema: Optional[str] = None,
    operator: Optional[str] = None,
    validate_only: bool = False,
    batch_size: Optional[int] = None
) -> 'ImportResult':
    """Import a single file using the unified import manager."""

    # Auto-detect data type if not provided
    if not data_type:
        data_type = auto_detect_data_type(file_path)
        if not data_type:
            raise ValueError(f"Could not auto-detect data type for {file_path}")

    # Auto-detect operator if not provided
    if not operator:
        operator = detect_operator(file_path)

    # Determine schema
    if not schema:
        schema = get_schema_for_data_type(data_type, operator)

    # Get batch size
    if not batch_size:
        batch_size = SUPPORTED_DATA_TYPES.get(data_type, {}).get('batch_size', 5000)

    try:
        # Use unified import manager
        from src.importers.import_manager import ImportManager, ImportJobConfig

        # Create import job configuration
        job_config = ImportJobConfig(
            source_path=str(file_path),
            data_type=data_type,
            operator=operator,
            target_schema=schema,
            batch_size=batch_size,
            validate_only=validate_only
        )

        # Use existing import manager from context (already initialized)
        if not ctx.import_manager:
            raise RuntimeError("Import manager not initialized in context")

        # Create and execute import job
        job_id = await ctx.import_manager.create_import_job(job_config)
        result = await ctx.import_manager.execute_import_job(job_id)

        # Update performance stats
        ctx.performance_stats['total_files'] += 1

        # Handle different result formats with proper status handling
        status_value = None
        if hasattr(result, 'status'):
            # Handle ImportStatus enum properly
            if hasattr(result.status, 'value'):
                status_value = result.status.value
            elif hasattr(result.status, 'name'):
                status_value = result.status.name
            else:
                status_value = str(result.status)
        elif hasattr(result, 'success'):
            status_value = 'COMPLETED' if result.success else 'FAILED'

        # Normalize status values
        is_success = status_value in ['COMPLETED', 'completed', 'SUCCESS', 'success']
        
        if is_success:
            ctx.performance_stats['successful_imports'] += 1
            # Get records processed from metrics if available
            records_processed = 0
            if hasattr(result, 'metrics') and result.metrics:
                records_processed = getattr(result.metrics, 'records_processed', 0)
            else:
                records_processed = getattr(result, 'records_processed', 0)
            ctx.performance_stats['total_records'] += records_processed
        else:
            ctx.performance_stats['failed_imports'] += 1

        # Convert to expected CLI format
        class CLIImportResult:
            def __init__(self, status, message, records_processed, errors=None):
                self.status = status
                self.message = message
                self.records_processed = records_processed
                self.errors = errors or []
                self.processing_time_seconds = getattr(result, 'processing_time_seconds', 0.0)

        class CLIImportStatus:
            SUCCESS = 'success'
            FAILED = 'failed'

        # Extract records processed properly
        records_processed = 0
        if hasattr(result, 'metrics') and result.metrics:
            records_processed = getattr(result.metrics, 'records_processed', 0)
        else:
            records_processed = getattr(result, 'records_processed', 0)

        return CLIImportResult(
            status=CLIImportStatus.SUCCESS if is_success else CLIImportStatus.FAILED,
            message=getattr(result, 'message', getattr(result, 'error_message', 'Import completed')),
            records_processed=records_processed,
            errors=getattr(result, 'validation_errors', [])
        )

    except Exception as e:
        # Enhanced error logging with context
        error_context = {
            'file_path': str(file_path),
            'data_type': data_type,
            'schema': schema,
            'operator': operator,
            'batch_size': batch_size,
            'error_type': type(e).__name__
        }

        logger.error(f"Failed to import {file_path}: {e}", exc_info=True, extra=error_context)
        ctx.performance_stats['failed_imports'] += 1

        # Categorize error for better user understanding
        error_category = _categorize_error(e)
        user_friendly_message = _get_user_friendly_error_message(e, error_category)

        # Return enhanced error result
        return CLIImportResult(
            status=CLIImportStatus.FAILED,
            message=user_friendly_message,
            records_processed=0,
            errors=[f"{error_category}: {str(e)}"]
        )


def _categorize_error(error: Exception) -> str:
    """Categorize error for better user understanding."""
    error_type = type(error).__name__
    error_message = str(error).lower()

    if 'connection' in error_message or 'database' in error_message:
        return "DATABASE_CONNECTION"
    elif 'permission' in error_message or 'access' in error_message:
        return "PERMISSION_DENIED"
    elif 'file not found' in error_message or 'no such file' in error_message:
        return "FILE_NOT_FOUND"
    elif 'memory' in error_message or 'out of memory' in error_message:
        return "MEMORY_ERROR"
    elif 'timeout' in error_message:
        return "TIMEOUT_ERROR"
    elif 'validation' in error_message or 'invalid' in error_message:
        return "VALIDATION_ERROR"
    elif 'csv' in error_message or 'excel' in error_message or 'parsing' in error_message:
        return "FILE_FORMAT_ERROR"
    elif error_type in ['KeyError', 'AttributeError', 'IndexError']:
        return "DATA_STRUCTURE_ERROR"
    else:
        return "UNKNOWN_ERROR"


def _get_user_friendly_error_message(error: Exception, category: str) -> str:
    """Generate user-friendly error message based on error category."""
    base_message = str(error)

    friendly_messages = {
        "DATABASE_CONNECTION": "数据库连接失败。请检查数据库服务是否运行，连接配置是否正确。",
        "PERMISSION_DENIED": "权限不足。请检查文件访问权限或数据库用户权限。",
        "FILE_NOT_FOUND": "文件未找到。请确认文件路径正确且文件存在。",
        "MEMORY_ERROR": "内存不足。请尝试减少批处理大小或关闭其他应用程序。",
        "TIMEOUT_ERROR": "操作超时。请检查网络连接或增加超时设置。",
        "VALIDATION_ERROR": "数据验证失败。请检查数据格式是否符合要求。",
        "FILE_FORMAT_ERROR": "文件格式错误。请确认文件格式正确且未损坏。",
        "DATA_STRUCTURE_ERROR": "数据结构错误。文件中可能缺少必需的列或数据格式不正确。",
        "UNKNOWN_ERROR": f"未知错误: {base_message}"
    }

    return friendly_messages.get(category, f"导入失败: {base_message}")


def _is_recoverable_error(error: Exception) -> bool:
    """Check if an error is recoverable and worth retrying."""
    error_message = str(error).lower()
    recoverable_patterns = [
        'timeout', 'connection', 'temporary', 'busy', 'lock', 'deadlock'
    ]
    return any(pattern in error_message for pattern in recoverable_patterns)


async def _retry_import_with_backoff(import_func, max_retries: int = 3, base_delay: float = 1.0):
    """Retry import operation with exponential backoff."""
    for attempt in range(max_retries + 1):
        try:
            return await import_func()
        except Exception as e:
            if attempt == max_retries or not _is_recoverable_error(e):
                raise e

            delay = base_delay * (2 ** attempt)
            logger.warning(f"Import attempt {attempt + 1} failed, retrying in {delay}s: {e}")
            await asyncio.sleep(delay)


# CLI Commands
@click.group()
@click.pass_context
def cli(ctx):
    """Connect Telecommunications Data Import CLI."""

    # Setup enhanced logging
    loggers = setup_enhanced_logging(
        log_level="INFO",
        log_dir="logs",
        enable_console=True,
        enable_structured=True,
        enable_progress=True
    )

    # Initialize context with loggers
    ctx.ensure_object(dict)
    ctx.obj['loggers'] = loggers

    # Display banner
    console.print(Panel.fit(
        "[bold blue]Connect Telecommunications Data Import CLI[/bold blue]\n"
        "[dim]Advanced telecom data processing and import system[/dim]",
        border_style="blue"
    ))


@cli.command(name='import')
@click.argument('path', type=click.Path(exists=True, path_type=Path))
@click.option('--preview', is_flag=True, help='Preview files and data before importing')
@click.pass_context
def import_data(ctx, path, preview):
    """Smart import for telecommunications data - auto-detect files or directories and import

    Fully adaptive import command:
    - Auto-detect data type (based on directory name: ep/cdr/nlg/kpi/cfg/score)
    - Auto-detect operator (based on file content: telefonica/vodafone/telekom)
    - Auto-select database schema (based on configuration)
    - Auto-recursive directory scanning
    - Auto-parallel processing
    - Auto-batch sizing
    """

    async def _import_async():
        import_ctx = ImportContext()
        active_tasks = set()

        try:
            # Initialize context
            await import_ctx.initialize()
            logger.info("Import context initialized successfully")

            # 智能路径检测和处理
            if path.is_file():
                console.print(f"\n[bold blue]📄 Single File Import Mode[/bold blue]")
                await _handle_smart_file_import(import_ctx, path, preview)
            elif path.is_dir():
                console.print(f"\n[bold green]📁 Directory Import Mode[/bold green]")
                await _handle_smart_directory_import(import_ctx, path, preview)
            else:
                console.print(f"[bold red]✗ Error:[/bold red] Path {path} is neither a file nor a directory")
                return

        except GeneratorExit:
            logger.info("Import process interrupted by GeneratorExit")
        except KeyboardInterrupt:
            logger.info("Import process interrupted by user")
        except Exception as e:
            console.print(f"\n[bold red]✗ Import failed:[/bold red]")
            console.print(f"Error: {str(e)}")
            console.print(f"Detailed error: {traceback.format_exc()}")

        finally:
            # Cancel all pending tasks before cleanup (excluding current task)
            try:
                current_task = asyncio.current_task()
                all_tasks = asyncio.all_tasks()
                pending_tasks = [task for task in all_tasks if not task.done() and task != current_task]

                if pending_tasks:
                    logger.info(f"Cancelling {len(pending_tasks)} pending tasks")
                    for task in pending_tasks:
                        if not task.cancelled():
                            task.cancel()

                    # Wait for tasks to be cancelled with timeout
                    if pending_tasks:
                        try:
                            await asyncio.wait_for(
                                asyncio.gather(*pending_tasks, return_exceptions=True),
                                timeout=3.0
                            )
                        except asyncio.TimeoutError:
                            logger.warning("Some tasks did not cancel within timeout")
                        except Exception as e:
                            logger.warning(f"Error during task cancellation: {e}")
            except Exception as e:
                logger.warning(f"Error during task cleanup: {e}")

            # Cleanup context
            try:
                await import_ctx.cleanup()
            except Exception as e:
                logger.warning(f"Error during context cleanup: {e}")

    async def _handle_smart_file_import(import_ctx, file_path, preview):
        """Smart single file import handling"""

        try:
            # Check if it's a QGIS file (auto-skip)
            path_str = str(file_path).lower()
            if 'ep' in path_str and 'qgis' in path_str:
                console.print(f"\n[bold yellow]⚠ Auto-skipping QGIS file:[/bold yellow] {file_path.name}")
                console.print(f"[dim]QGIS files are not included in import process[/dim]")
                return

            # Smart detection of all parameters
            data_type = auto_detect_data_type(file_path)
            operator = detect_operator(file_path)
            schema = get_schema_for_data_type(data_type, operator)

            # Get batch size
            batch_size = 1000

            console.print(f"\n[bold]Importing file:[/bold] {file_path.name}")
            console.print(f"📊 Data type: [cyan]{data_type.upper()}[/cyan]")
            console.print(f"🏢 Operator: [cyan]{operator}[/cyan]")
            console.print(f"🗄️ Target schema: [cyan]{schema}[/cyan]")

            # For CDR Excel files, show sheet detection details and update operator/schema
            updated_operator = operator
            updated_schema = schema
            if data_type == 'cdr' and file_path.suffix.lower() in ['.xlsx', '.xls']:
                try:
                    import pandas as pd
                    excel_file = pd.ExcelFile(file_path)
                    sheet_names = excel_file.sheet_names

                    console.print(f"📋 Excel Sheets: {sheet_names}")

                    # Show which sheet would be selected
                    from src.importers.cdr_importer import CDRImporter
                    temp_importer = CDRImporter(
                        source_path=file_path,
                        target_schema=schema,
                        operator=operator
                    )
                    selected_sheet, detected_operator = temp_importer._detect_operator_sheet(sheet_names, operator)

                    if detected_operator != operator:
                        console.print(f"🎯 Operator Updated: [yellow]{operator}[/yellow] → [green]{detected_operator}[/green]")
                        updated_operator = detected_operator
                        updated_schema = get_schema_for_data_type(data_type, detected_operator)
                        console.print(f"🗄️ Updated Schema: [cyan]{updated_schema}[/cyan]")

                    console.print(f"🎯 Selected Sheet: '[green]{selected_sheet}[/green]' (operator match)")

                except Exception as e:
                    console.print(f"⚠️ Could not analyze Excel sheets: {e}")

            # Use updated values for the actual import
            operator = updated_operator
            schema = updated_schema

            # Preview mode - show file content preview
            if preview:
                console.print(f"\n[bold blue]🔍 Preview Mode - File details:[/bold blue]")
                console.print(f"    📊 {data_type} | 🏢 {operator} | 🗄️ {schema}")
                try:
                    import pandas as pd
                    import yaml
                    from pathlib import Path

                    # Get configuration for proper file reading from database.yaml
                    skip_rows = 0
                    header_row = 0

                    try:
                        config_path = Path("config/database.yaml")
                        if config_path.exists():
                            with open(config_path, 'r', encoding='utf-8') as f:
                                yaml_config = yaml.safe_load(f)

                            # Get telecom data sources configuration
                            if 'telecom_data_sources' in yaml_config and data_type in yaml_config['telecom_data_sources']:
                                data_type_config = yaml_config['telecom_data_sources'][data_type]
                                config_skip_rows = data_type_config.get('skip_rows', 0)
                                config_header_row = data_type_config.get('header_row', 0)

                                # Convert config values to pandas parameters
                                # If skip_rows=1 and header_row=1, we skip row 0 and use row 1 as header
                                # In pandas: skiprows=1, header=0 (because after skipping, row 1 becomes row 0)
                                skip_rows = config_skip_rows
                                header_row = 0 if config_skip_rows > 0 else config_header_row

                                console.print(f"[dim]Config loaded: skip_rows={config_skip_rows}, header_row={config_header_row} → pandas: skiprows={skip_rows}, header={header_row}[/dim]")
                            else:
                                console.print(f"[yellow]No config found for {data_type} in telecom_data_sources[/yellow]")
                        else:
                            console.print(f"[yellow]Config file not found: {config_path}[/yellow]")
                    except Exception as e:
                        console.print(f"[yellow]Warning: Could not load config, using defaults: {e}[/yellow]")

                    # Read first few rows for preview
                    if file_path.suffix.lower() == '.csv':
                        # Try multiple encodings for CSV files
                        encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
                        df_preview = None
                        for encoding in encodings:
                            try:
                                df_preview = pd.read_csv(
                                    file_path,
                                    nrows=5,
                                    encoding=encoding,
                                    skiprows=skip_rows,
                                    header=header_row
                                )
                                break
                            except UnicodeDecodeError:
                                continue
                        if df_preview is None:
                            console.print(f"[yellow]Cannot preview file: encoding issues[/yellow]")
                            return
                    else:
                        df_preview = pd.read_excel(
                            file_path,
                            nrows=5,
                            skiprows=skip_rows,
                            header=header_row
                        )

                    console.print(f"Columns: {list(df_preview.columns)}")
                    console.print(f"Sample data (first 5 rows):")
                    console.print(df_preview.to_string(index=False))

                    # Ask user to continue
                    import click
                    if not click.confirm("\nContinue with import?"):
                        console.print("[yellow]User cancelled import[/yellow]")
                        return

                except Exception as e:
                    console.print(f"[yellow]Cannot preview file: {e}[/yellow]")

            # Start import with progress display
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TaskProgressColumn(),
                TimeElapsedColumn(),
                console=console
            ) as progress:

                task = progress.add_task(f"Importing {data_type.upper()} data...", total=100)

                # Execute import
                result = await import_single_file(
                    import_ctx, file_path, data_type, schema, operator,
                    False, batch_size  # validate_only=False
                )

                progress.update(task, completed=100)

                # Display results
                if result.status == 'success':
                    console.print(f"\n[bold green]✅ Import successful![/bold green]")
                    console.print(f"📈 Records processed: [green]{result.records_processed:,}[/green]")
                    if result.processing_time_seconds:
                        console.print(f"⏱️ Processing time: [green]{result.processing_time_seconds:.2f} seconds[/green]")
                else:
                    console.print(f"\n[bold red]❌ Import failed![/bold red]")
                    console.print(f"Error: {result.message}")
                    if result.errors:
                        console.print("Detailed errors:")
                        for error in result.errors[:3]:  # Show first 3 errors
                            console.print(f"  • {error}")

        except Exception as e:
            console.print(f"\n[bold red]❌ Import exception:[/bold red]")
            console.print(f"Error: {str(e)}")
            logger.error(f"Smart file import error: {str(e)}", exc_info=True)

        finally:
            await import_ctx.cleanup()

    async def _handle_smart_directory_import(import_ctx, directory_path, preview):
        """Smart directory import handling - fully adaptive"""

        # Get default settings
        parallel_workers = 8
        batch_size = 1000

        # Auto-recursive scan of all files
        console.print(f"🔍 Recursively scanning directory: [cyan]{directory_path}[/cyan]")

        # Supported file extensions
        file_extensions = ['.csv', '.xlsx', '.xls', '.xlsb']

        # Scan files
        files_to_import = []
        skipped_files = []
        total_scanned = 0

        for file_path in directory_path.rglob("*"):  # Recursive scan
            total_scanned += 1
            if file_path.is_file() and file_path.suffix.lower() in file_extensions:
                # Auto-skip QGIS files
                path_str = str(file_path).lower()
                if 'ep' in path_str and 'qgis' in path_str:
                    skipped_files.append(file_path)
                    continue

                files_to_import.append(file_path)

        console.print(f"📊 Scan results: Total {total_scanned} files, found {len(files_to_import)} importable files")
        if skipped_files:
            console.print(f"⚠️ Auto-skipped {len(skipped_files)} QGIS files")

        if not files_to_import:
            console.print("[yellow]❌ No importable files found[/yellow]")
            return

        # Advanced performance optimization
        optimizer = PerformanceOptimizer()
        processing_plan = optimizer.optimize_batch_processing(files_to_import)

        console.print(f"[blue]📊 Processing Plan:[/blue]")
        console.print(f"  Total files: {processing_plan['total_files']}")
        console.print(f"  Total size: {processing_plan['total_size_mb']:.1f} MB")
        console.print(f"  Estimated time: {processing_plan['estimated_time_minutes']:.1f} minutes")
        console.print(f"  Memory requirement: {processing_plan['memory_requirement_mb']:.0f} MB")

        # Use optimized parallel workers with improved memory threshold
        memory_threshold = 2048  # Updated threshold to prevent unnecessary warnings
        if processing_plan['memory_requirement_mb'] > memory_threshold:
            # More intelligent worker scaling based on memory requirements
            memory_per_worker = 300  # MB per worker
            max_workers_by_memory = int(memory_threshold / memory_per_worker)
            adjusted_workers = min(parallel_workers, max_workers_by_memory, 4)  # Cap at 4 for very large datasets

            if adjusted_workers < parallel_workers:
                parallel_workers = adjusted_workers
                console.print(f"[blue]ℹ️ Optimized parallel workers to {parallel_workers} for memory efficiency[/blue]")
            # No warning for normal operations under the new threshold

        # Smart file grouping (by data type and operator) - optimized for preview
        file_groups = {}
        for file_path in files_to_import:
            data_type = auto_detect_data_type(file_path)
            operator = detect_operator(file_path, skip_excel_analysis=True)  # Skip Excel analysis for performance

            key = f"{data_type}_{operator}"
            if key not in file_groups:
                file_groups[key] = []
            file_groups[key].append(file_path)

        # Display file distribution
        console.print(f"\n[bold]📋 File Distribution Statistics:[/bold]")
        for key, files in file_groups.items():
            data_type_part, operator_part = key.split('_', 1)
            console.print(f"  📊 {data_type_part.upper()} ({operator_part}): [cyan]{len(files)}[/cyan] files")

        # Preview mode - show files to be processed
        if preview:
            console.print(f"\n[bold blue]🔍 Preview Mode - Files to be processed:[/bold blue]")
            for i, file_path in enumerate(files_to_import[:20], 1):  # Only show first 20
                data_type = auto_detect_data_type(file_path)
                operator = detect_operator(file_path, skip_excel_analysis=True)  # Skip Excel analysis for performance
                schema = get_schema_for_data_type(data_type, operator)
                console.print(f"  {i:3d}. [cyan]{file_path.name}[/cyan]")
                console.print(f"       📊 {data_type} | 🏢 {operator} | 🗄️ {schema}")

            if len(files_to_import) > 20:
                console.print(f"       ... and {len(files_to_import) - 20} more files")

            console.print(f"\n[bold]📈 Import Configuration:[/bold]")
            console.print(f"  📁 Total files: [cyan]{len(files_to_import)}[/cyan]")
            console.print(f"  🔄 Parallel workers: [cyan]{parallel_workers}[/cyan]")
            console.print(f"  📦 Batch size: [cyan]{batch_size}[/cyan]")

            # Ask user to continue
            import click
            if not click.confirm("\nStart import?"):
                console.print("[yellow]User cancelled import[/yellow]")
                return

        # Actual import processing
        import asyncio
        semaphore = asyncio.Semaphore(parallel_workers)

        # Statistics
        stats = {
            'total_files': len(files_to_import),
            'successful_imports': 0,
            'failed_imports': 0,
            'total_records': 0,
            'start_time': time.time()
        }

        console.print(f"\n[bold green]🚀 Starting batch import...[/bold green]")

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            TimeElapsedColumn(),
            console=console
        ) as progress:

            main_task = progress.add_task(f"Processing {len(files_to_import)} files...", total=len(files_to_import))

            async def process_file(file_path):
                async with semaphore:
                    try:
                        # 智能检测参数
                        data_type = auto_detect_data_type(file_path)
                        operator = detect_operator(file_path)

                        # Skip files with undetectable type
                        if not data_type:
                            console.print(f"[yellow]⚠ Skipping:[/yellow] {file_path.name} - cannot detect data type")
                            progress.advance(main_task)
                            return None

                        schema = get_schema_for_data_type(data_type, operator)

                        console.print(f"[dim]Processing:[/dim] {file_path.name} → {data_type} ({operator})")

                        # Import file with error handling
                        try:
                            result = await import_single_file(
                                import_ctx, file_path, data_type, schema,
                                operator, False, batch_size  # validate_only=False
                            )
                        except Exception as import_error:
                            # Enhanced error handling for batch imports
                            error_category = _categorize_error(import_error)
                            friendly_message = _get_user_friendly_error_message(import_error, error_category)

                            console.print(f"[red]❌ Import error {file_path.name}:[/red]")
                            console.print(f"  [dim]{friendly_message}[/dim]")

                            # Log detailed error for debugging
                            logger.error(f"Batch import error for {file_path.name}: {import_error}",
                                       exc_info=True,
                                       extra={'file_path': str(file_path), 'error_category': error_category})

                            stats['failed_imports'] += 1
                            progress.advance(main_task)
                            return None

                        # Update statistics
                        if result.status == 'success':
                            stats['successful_imports'] += 1
                            if hasattr(result, 'records_processed'):
                                stats['total_records'] += result.records_processed
                        else:
                            stats['failed_imports'] += 1
                            console.print(f"[red]❌ Failed:[/red] {file_path.name}")
                            console.print(f"  Error: {result.message}")

                        progress.advance(main_task)
                        return result

                    except Exception as e:
                        console.print(f"[red]❌ Processing error {file_path.name}:[/red] {str(e)}")
                        stats['failed_imports'] += 1
                        progress.advance(main_task)
                        return None

            # Process all files concurrently with performance monitoring
            async def monitor_performance():
                """Monitor system performance during import."""
                while True:
                    perf_status = optimizer.monitor_performance_during_import()
                    if perf_status['status'] != 'normal':
                        console.print(f"[yellow]⚠️ Performance Alert: {perf_status['status']}[/yellow]")
                        for rec in perf_status['recommendations']:
                            console.print(f"  💡 {rec}")
                    await asyncio.sleep(10)  # Check every 10 seconds

            # Start performance monitoring
            monitor_task = asyncio.create_task(monitor_performance())

            try:
                tasks = [process_file(file_path) for file_path in files_to_import]
                results = await asyncio.gather(*tasks, return_exceptions=True)
            finally:
                monitor_task.cancel()
                try:
                    await monitor_task
                except asyncio.CancelledError:
                    pass

        # Display final statistics
        end_time = time.time()
        total_time = end_time - stats['start_time']

        console.print(f"\n[bold]📊 Import Completion Statistics:[/bold]")
        console.print(f"  📁 Total files: [cyan]{stats['total_files']}[/cyan]")
        console.print(f"  ✅ Successful imports: [green]{stats['successful_imports']}[/green]")
        console.print(f"  ❌ Failed imports: [red]{stats['failed_imports']}[/red]")
        console.print(f"  📈 Total records: [green]{stats['total_records']:,}[/green]")
        console.print(f"  ⏱️ Total time: [cyan]{total_time:.2f} seconds[/cyan]")

        if stats['total_files'] > 0:
            success_rate = (stats['successful_imports'] / stats['total_files']) * 100
            console.print(f"  🎯 Success rate: [green]{success_rate:.1f}%[/green]")

            if success_rate == 100.0:
                console.print(f"\n[bold green]🎉 Perfect! All files imported successfully![/bold green]")
            elif success_rate >= 90.0:
                console.print(f"\n[bold green]✨ Excellent! Very high import success rate![/bold green]")
            elif success_rate >= 70.0:
                console.print(f"\n[bold yellow]⚠️ Notice: Some files failed to import, please check error messages[/bold yellow]")
            else:
                console.print(f"\n[bold red]🚨 Warning: Low import success rate, recommend checking data format[/bold red]")

        # Performance cleanup and logging
        console.print(f"[dim]🧹 Cleaning up memory...[/dim]")
        optimizer.cleanup_memory()

        # Log performance summary if loggers are available
        if 'loggers' in ctx.obj and 'performance' in ctx.obj['loggers']:
            log_performance_summary(
                ctx.obj['loggers']['performance'],
                stats['total_files'],
                stats['successful_imports'],
                stats['failed_imports'],
                stats['total_records'],
                total_time
            )

    # Run the async function
    import asyncio
    asyncio.run(_import_async())





# Preview command removed as per user requirements
# Only import and info commands are retained


@cli.command()
@click.option('--data-type', '-t', type=click.Choice(list(SUPPORTED_DATA_TYPES.keys())),
              help='Show information for specific data type')
def info(data_type):
    """Show information about supported data types and configurations."""

    console.print(f"\n[bold]Connect Import System Information[/bold]")

    if data_type:
        # Show specific data type information
        if data_type in SUPPORTED_DATA_TYPES:
            config = SUPPORTED_DATA_TYPES[data_type]

            console.print(f"\n[bold cyan]{data_type.upper()} Data Type Information:[/bold cyan]")
            console.print(f"Description: {config.get('description', 'No description available')}")
            console.print(f"Supported schemas: {', '.join(config.get('schemas', []))}")
            console.print(f"Default batch size: {config.get('batch_size', 'Not specified')}")
            console.print(f"Supported extensions: {', '.join(config.get('extensions', []))}")

            if 'operators' in config:
                console.print(f"Supported operators: {', '.join(config['operators'])}")
        else:
            console.print(f"[red]Unknown data type: {data_type}[/red]")
    else:
        # Show all supported data types
        console.print(f"\n[bold]Supported Data Types:[/bold]")

        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Type", style="cyan")
        table.add_column("Description", style="white")
        table.add_column("Schemas", style="green")
        table.add_column("Extensions", style="yellow")

        for dtype, config in SUPPORTED_DATA_TYPES.items():
            description = config.get('description', 'No description')
            schemas = ', '.join(config.get('schemas', []))
            extensions = ', '.join(config.get('extensions', []))

            table.add_row(dtype.upper(), description, schemas, extensions)

        console.print(table)

        # Show operator information
        console.print(f"\n[bold]Supported Operators:[/bold]")
        operators_table = Table(show_header=True, header_style="bold magenta")
        operators_table.add_column("Operator", style="cyan")
        operators_table.add_column("CDR Schema", style="green")
        operators_table.add_column("Description", style="white")

        operators_table.add_row("telefonica", "cdr_to2", "Telefonica/O2 operator data")
        operators_table.add_row("vodafone", "cdr_vdf", "Vodafone operator data")
        operators_table.add_row("telekom", "cdr_tdg", "Deutsche Telekom operator data")
        operators_table.add_row("qgis", "ep_to2", "QGIS engineering parameters data")

        console.print(operators_table)


# Handle async CLI commands - this needs to run regardless of how the module is invoked
import asyncio
import functools

def async_command(f):
    @functools.wraps(f)
    def wrapper(*args, **kwargs):
        return asyncio.run(f(*args, **kwargs))
    return wrapper

# Note: file and directory commands already handle async internally
# No need to apply async wrappers to them

if __name__ == '__main__':
    cli()
