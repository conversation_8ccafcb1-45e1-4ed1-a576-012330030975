#!/usr/bin/env python3
"""
SCORE data importer.

This module provides functionality for importing and processing SCORE data files.
SCORE files typically contain scoring and rating data for telecommunications services.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""

import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

import pandas as pd
from datetime import datetime

from .base import AbstractImporter, TelecomImportError, ImportResult, ImportStatus, ImportMetrics

# Configure logging
logger = logging.getLogger(__name__)


class ScoreImporter(AbstractImporter):
    """SCORE data importer."""

    def __init__(
        self,
        config: Optional[Dict[str, Any]] = None,
        db_session=None,
        performance_logger=None,
        **kwargs,
    ):
        """Initialize SCORE importer.

        Args:
            config: Configuration dictionary
            db_session: Database session for direct database operations
            performance_logger: Logger for performance metrics
            **kwargs: Additional configuration options
        """
        self.name = "SCOREImporter"
        self.supported_formats = ["csv", "xlsx", "xls"]
        self.original_config = config or {}  # Keep original config
        self.db_session = db_session
        self.performance_logger = performance_logger

        # Initialize database components if session provided
        if db_session:
            from src.database.operations import BulkOperations
            from src.database.schema import SchemaManager

            self.schema_manager = SchemaManager(db_session)
            self.bulk_operations = BulkOperations(db_session)

        # Prepare config for AbstractImporter
        importer_config = {
            'name': self.name,
            'data_type': 'score',
            'supported_formats': self.supported_formats,
            'batch_size': self.original_config.get('batch_size', 5000),
            **kwargs
        }
        super().__init__(config=importer_config)

    def set_database_context(self, pool=None, db_manager=None, db_ops=None, schema_manager=None):
        """Set database context for the importer."""
        if pool:
            self.pool_manager = pool
        if db_manager:
            self.db_manager = db_manager
        if db_ops:
            self.db_ops = db_ops
        if schema_manager:
            self.schema_manager = schema_manager

        # Initialize bulk operations if we have a schema manager with pool
        if schema_manager and hasattr(schema_manager, 'pool'):
            from src.database.operations.bulk_operations import BulkOperations
            self.bulk_operations = BulkOperations(schema_manager.pool)
            # Also set db_session for backward compatibility
            self.db_session = schema_manager

    def validate_file(self, file_path: Union[str, Path]) -> bool:
        """Validate SCORE data file.

        Args:
            file_path: Path to the file

        Returns:
            bool: True if file is valid

        Raises:
            ImportError: If file is invalid
        """
        path = Path(file_path)

        if not path.exists():
            raise ImportError(f"File does not exist: {path}")

        if path.suffix.lower() not in [".csv", ".xlsx", ".xls"]:
            raise ImportError(f"Unsupported file format: {path.suffix}")

        return True

    def validate_data_structure(self, data: pd.DataFrame) -> Tuple[bool, List[str]]:
        """Validate SCORE data structure.

        Args:
            data: DataFrame to validate

        Returns:
            Tuple[bool, List[str]]: Validation result and error messages
        """
        errors = []

        # Accept any column structure - no required columns validation

        # Check for empty DataFrame
        if data.empty:
            errors.append("Data file is empty")

        return len(errors) == 0, errors

    def validate_data_values(self, data: pd.DataFrame) -> Tuple[bool, List[str]]:
        """Validate SCORE data values.

        Args:
            data: DataFrame to validate

        Returns:
            Tuple[bool, List[str]]: Validation result and error messages
        """
        errors = []

        # Skip required columns validation - accept any data structure

        # Validate SCORE_VALUE is numeric
        if "SCORE_VALUE" in data.columns:
            try:
                pd.to_numeric(data["SCORE_VALUE"], errors="coerce")
            except Exception as e:
                errors.append(f"SCORE_VALUE column contains non-numeric values: {e}")

        # Validate MEASUREMENT_TIME format
        if "MEASUREMENT_TIME" in data.columns:
            try:
                pd.to_datetime(data["MEASUREMENT_TIME"], errors="coerce")
            except Exception as e:
                errors.append(f"MEASUREMENT_TIME column has invalid datetime format: {e}")

        return len(errors) == 0, errors

    def preprocess_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Preprocess SCORE data before import.

        Args:
            data: Raw DataFrame

        Returns:
            pd.DataFrame: Preprocessed DataFrame
        """
        processed_data = data.copy()

        # Remove duplicate columns and standardize column names
        from src.utils.column_deduplicator import ColumnDeduplicator
        
        # Remove duplicate columns, keeping the most complete one
        processed_data, dedup_report = ColumnDeduplicator.remove_duplicate_columns(
            processed_data, keep_strategy='best'
        )
        
        if dedup_report['total_removed'] > 0:
            logger.info(f"Removed {dedup_report['total_removed']} duplicate columns in Score data")
        
        # Standardize column names to uppercase (keeping original format)
        clean_columns = []
        for col in processed_data.columns:
            clean_name = str(col).upper().strip()
            clean_columns.append(clean_name)
        
        processed_data.columns = clean_columns

        # Convert SCORE_VALUE to numeric
        if "SCORE_VALUE" in processed_data.columns:
            processed_data["SCORE_VALUE"] = pd.to_numeric(
                processed_data["SCORE_VALUE"], errors="coerce"
            )

        # Convert MEASUREMENT_TIME to datetime
        if "MEASUREMENT_TIME" in processed_data.columns:
            processed_data["MEASUREMENT_TIME"] = pd.to_datetime(
                processed_data["MEASUREMENT_TIME"], errors="coerce"
            )

        # Add metadata columns
        processed_data["IMPORT_TIMESTAMP"] = datetime.now()
        processed_data["SOURCE_FILE"] = self.source_path.name

        # Remove rows with all null values only
        processed_data = processed_data.dropna(how='all')

        return processed_data

    def get_table_name(self, filename: str) -> str:
        """Generate table name for SCORE data using enhanced naming conventions.

        Args:
            filename: Source filename or full path

        Returns:
            str: Table name following pattern score_{year}Q{quarter}
        """
        try:
            from src.database.utils.table_naming import TableNamingManager

            # Get configuration
            config = getattr(self, 'config', {})

            naming_manager = TableNamingManager(config)

            # Convert filename to Path object (handle both filename and full path)
            file_path = Path(filename)

            # Generate table name using standardized logic
            # Pattern: score_{year}Q{quarter}
            table_name = naming_manager.generate_table_name(
                data_type="score",
                file_path=file_path
            )

            self.logger.info(f"Generated SCORE table name '{table_name}' for file '{filename}'")
            return table_name

        except Exception as e:
            self.logger.warning(f"Failed to generate table name using pattern, falling back to simple naming: {e}")
            return self._generate_score_table_name_fallback(filename)

    def _generate_score_table_name_fallback(self, filename: str) -> str:
        """Fallback SCORE table name generation with enhanced logic."""
        import re
        from pathlib import Path
        from datetime import datetime

        file_path = Path(filename)
        filename_only = file_path.stem

        # Extract year and quarter from filename
        year_quarter = self._extract_year_quarter_from_score_filename(filename_only)

        table_name = f"score_{year_quarter}"
        self.logger.info(f"Using fallback SCORE table name: '{table_name}'")
        return table_name

    def _extract_year_quarter_from_score_filename(self, filename: str) -> str:
        """Extract year and quarter from SCORE filename."""
        import re
        from datetime import datetime

        # Look for year-quarter pattern
        quarter_match = re.search(r'(\d{4})Q([1-4])', filename, re.IGNORECASE)
        if quarter_match:
            year, quarter = quarter_match.groups()
            return f"{year}Q{quarter}"

        # Fallback to current year and quarter
        current_year = datetime.now().year
        current_quarter = (datetime.now().month - 1) // 3 + 1
        return f"{current_year}Q{current_quarter}"

    def _get_legacy_table_name(self, filename: str) -> str:
        """Legacy table name generation for backward compatibility."""
        import re
        from src.database.utils.validators import InputValidator

        # Use pattern from config or default
        pattern = self.original_config.get('table_name_pattern', 'score_{filename}')

        # Clean filename (remove extension and special characters)
        clean_filename = Path(filename).stem.lower()
        clean_filename = "".join(c if c.isalnum() or c == "_" else "_" for c in clean_filename)

        # Prepare format parameters with defaults
        format_params = {
            'filename': clean_filename,
            'algorithm': 'default',  # Default algorithm
            'date': '20240101'       # Default date
        }

        # Try to extract algorithm from filename (common algorithms)
        filename_lower = clean_filename.lower()
        algorithms = ['decision_tree', 'gradient_boost', 'naive_bayes', 'neural', 'rf', 'svm', 'knn']  # Order by length to match longer names first
        for algo in algorithms:
            if algo in filename_lower:
                format_params['algorithm'] = algo
                break

        # Try to extract date from filename (YYYYMMDD format)
        date_match = re.search(r'(20\d{6})', clean_filename)
        if date_match:
            format_params['date'] = date_match.group(1)

        try:
            raw_table_name = pattern.format(**format_params)
        except KeyError as e:
            # If pattern has unknown placeholders, fall back to simple filename
            raw_table_name = f"score_{clean_filename}"

        # Validate and fix table name to meet PostgreSQL requirements
        return InputValidator.validate_and_fix_table_name(raw_table_name)

    def get_schema_name(self) -> str:
        """Get schema name for SCORE data.

        Returns:
            str: Schema name
        """
        return self.original_config.get('schema_name', 'score_to2')

    async def import_data(self, source_path: Union[str, Path] = None, **kwargs) -> ImportResult:
        """Import SCORE data from source file.

        Args:
            source_path: Path to the source file
            **kwargs: Additional import options

        Returns:
            ImportResult: Result of the import operation

        Raises:
            ImportError: If import fails
        """
        # Set source_path from parameter or use existing
        if source_path:
            from pathlib import Path
            self.source_path = Path(source_path)
        elif not hasattr(self, 'source_path') or not self.source_path:
            raise TelecomImportError("No source path provided")

        start_time = datetime.now()

        try:
            logger.info(f"Starting SCORE import from {self.source_path}")

            # Validate source file
            self.validate_file(self.source_path)

            # Read data based on file format with configuration-driven approach
            if self.source_path.suffix.lower() == ".csv":
                # Use configuration-driven CSV structure detection
                structure = self._detect_csv_structure_with_config(self.source_path, 'score')
                self.logger.info(f"Using SCORE CSV structure: {structure}")

                # Enhanced CSV reading with configuration-based structure detection
                import csv
                data = pd.read_csv(
                    self.source_path,
                    encoding=structure['encoding'],
                    delimiter=structure['delimiter'],
                    skiprows=structure['skip_rows'],
                    header=structure['header_row'],  # Use configured header row
                    engine='python',  # More flexible parser
                    on_bad_lines='skip',  # Skip problematic lines
                    quoting=csv.QUOTE_MINIMAL,
                    skipinitialspace=True,
                    comment='#'  # Skip lines starting with #
                )
            else:
                # Excel files - handle complex header structure
                # First read to understand the structure
                raw_data = pd.read_excel(self.source_path, header=None)

                # For SCORE files, we need to handle the complex header structure
                # Row 0: Operator names (Salt, Sunrise, Swisscom, etc.)
                # Row 1: Column names (module, aggregation_infrastructure, etc.)

                # Create proper column names by combining row 0 and row 1
                header_row_0 = raw_data.iloc[0].fillna('')
                header_row_1 = raw_data.iloc[1].fillna('')

                # Create meaningful column names
                column_names = []
                for i, (op_name, col_name) in enumerate(zip(header_row_0, header_row_1)):
                    if col_name and col_name != '':
                        if op_name and op_name != '':
                            column_names.append(f"{op_name}_{col_name}")
                        else:
                            column_names.append(str(col_name))
                    else:
                        column_names.append(f"col_{i}")

                # Read the actual data starting from row 2
                data = pd.read_excel(self.source_path, skiprows=2, header=None)
                data.columns = column_names[:len(data.columns)]

                # Filter out empty rows
                data = data.dropna(how='all')

                logger.info(f"SCORE Excel file processed with columns: {list(data.columns)}")

            logger.info(f"Read {len(data)} records from {self.source_path}")

            # Validate data structure
            is_valid, structure_errors = self.validate_data_structure(data)
            if not is_valid:
                raise ImportError(f"Data structure validation failed: {structure_errors}")

            # Validate data values
            is_valid, value_errors = self.validate_data_values(data)
            if not is_valid:
                logger.warning(f"Data value validation warnings: {value_errors}")

            # Preprocess data
            processed_data = self.preprocess_data(data)
            logger.info(f"Preprocessed data: {len(processed_data)} valid records")

            if processed_data.empty:
                raise ImportError("No valid records after preprocessing")

            # Import to database if session available
            records_imported = 0
            if self.db_session and self.bulk_operations:
                table_name = self.get_table_name(self.source_path.name)
                schema_name = self.get_schema_name()

                # Use bulk_insert_dataframe method
                success = await self.bulk_operations.bulk_insert_dataframe_async(
                    table_name=table_name,
                    df=processed_data,
                    schema=schema_name,
                    if_exists="append",
                    chunk_size=kwargs.get("batch_size", 1000)
                )

                if success:
                    records_imported = len(processed_data)
                    logger.info(f"Imported {records_imported} SCORE records to {schema_name}.{table_name}")
                else:
                    logger.error("Failed to import SCORE records to database")
                    records_imported = 0
            else:
                records_imported = len(processed_data)
                logger.info("Database session not available, skipping database import")

            # Create result
            processing_time = (datetime.now() - start_time).total_seconds()

            # Create metrics
            metrics = ImportMetrics()
            metrics.records_processed = records_imported
            metrics.processing_time_seconds = processing_time

            import_result = ImportResult(
                status=ImportStatus.COMPLETED,
                metrics=metrics,
                source_info={
                    "source_path": str(self.source_path),
                    "file_size_bytes": self.source_path.stat().st_size,
                    "data_type": "SCORE",
                    "original_records": len(data),
                    "processed_records": len(processed_data),
                    "table_name": self.get_table_name(self.source_path.name) if self.db_session else None,
                    "schema_name": self.get_schema_name() if self.db_session else None,
                }
            )

            logger.info(f"SCORE import completed successfully in {import_result.metrics.processing_time_seconds:.2f} seconds")
            return import_result

        except Exception as e:
            error_msg = f"SCORE import failed: {str(e)}"
            logger.error(error_msg)
            
            # Create metrics for failed import
            processing_time = (datetime.now() - start_time).total_seconds()
            metrics = ImportMetrics()
            metrics.records_processed = 0
            metrics.processing_time_seconds = processing_time

            return ImportResult(
                status=ImportStatus.FAILED,
                metrics=metrics,
                error_message=error_msg,
                source_info={
                    "source_path": str(self.source_path),
                    "file_size_bytes": self.source_path.stat().st_size if self.source_path.exists() else 0,
                    "data_type": "SCORE",
                }
            )

    def get_sample_data(self, num_rows: int = 5) -> Optional[pd.DataFrame]:
        """Get sample data from the source file.

        Args:
            num_rows: Number of rows to sample

        Returns:
            Optional[pd.DataFrame]: Sample data or None if error
        """
        try:
            if self.source_path.suffix.lower() == ".csv":
                return pd.read_csv(self.source_path, nrows=num_rows)
            else:
                return pd.read_excel(self.source_path, nrows=num_rows)
        except Exception as e:
            logger.error(f"Failed to get sample data: {e}")
            return None

    def get_file_info(self) -> Dict[str, Any]:
        """Get information about the source file.

        Returns:
            Dict[str, Any]: File information
        """
        try:
            stat = self.source_path.stat()
            return {
                "filename": self.source_path.name,
                "file_size_bytes": stat.st_size,
                "file_size_mb": round(stat.st_size / (1024 * 1024), 2),
                "modified_time": datetime.fromtimestamp(stat.st_mtime),
                "file_format": self.source_path.suffix.lower(),
                "supported": self.source_path.suffix.lower() in [".csv", ".xlsx", ".xls"],
            }
        except Exception as e:
            logger.error(f"Failed to get file info: {e}")
            return {"error": str(e)}

    def get_source_info(self) -> Dict[str, Any]:
        """Get source information for the importer."""
        return {
            'source_path': str(self.source_path) if self.source_path else None,
            'data_type': 'score',
            'supported_formats': self.supported_formats
        }

    async def validate_source(self, source_path: str = None) -> bool:
        """Validate the source file."""
        try:
            from pathlib import Path

            path = Path(source_path or self.source_path)

            # Check if file exists
            if not path.exists():
                return False

            # Check file extension
            if path.suffix.lower() not in ['.xlsx', '.xls']:
                return False

            return True

        except Exception:
            return False