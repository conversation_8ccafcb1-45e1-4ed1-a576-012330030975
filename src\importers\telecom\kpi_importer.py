"""KPI (Key Performance Indicators) importer with telecommunications optimization.

This module provides specialized KPI data import functionality with support for
multiple operator formats, advanced validation, and performance optimization.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""
import asyncio
import logging
from typing import Any, Dict, List, Optional, Union
from pathlib import Path
from datetime import datetime, timedelta

import pandas as pd
import numpy as np
from pydantic import BaseModel, Field, ConfigDict

from ..base import (
    AbstractImporter,
    ValidationMixin,
    ProcessingMixin,
    PerformanceMixin,
    ImporterConfig,
    ImportResult,
)


class KPIConfig(BaseModel):
    """Configuration specific to KPI data import."""
    # Operator-specific settings
    operator: str = Field(default="generic", description="Telecom operator (telefonica, vodafone, telekom, etc.)")
    network_type: str = Field(default="4G", description="Network type (2G, 3G, 4G, 5G)")
    kpi_category: str = Field(default="all", description="KPI category (radio, core, service, quality)")
    
    # Time-based settings
    timezone: str = Field(default="Europe/Madrid", description="Timezone for timestamp processing")
    aggregation_level: str = Field(default="hourly", description="Time aggregation level (hourly, daily, weekly)")
    
    # KPI-specific validation
    enable_threshold_validation: bool = Field(default=True, description="Enable KPI threshold validation")
    enable_trend_analysis: bool = Field(default=True, description="Enable trend analysis")
    enable_anomaly_detection: bool = Field(default=True, description="Enable anomaly detection")
    
    # Quality thresholds
    min_availability_threshold: float = Field(default=95.0, description="Minimum availability threshold (%)")
    max_error_rate_threshold: float = Field(default=5.0, description="Maximum error rate threshold (%)")
    min_throughput_threshold: float = Field(default=1.0, description="Minimum throughput threshold (Mbps)")
    
    # Data processing
    enable_geolocation: bool = Field(default=True, description="Enable geolocation processing")
    enable_cell_aggregation: bool = Field(default=True, description="Enable cell-level aggregation")
    enable_sector_analysis: bool = Field(default=True, description="Enable sector analysis")
    
    # Performance optimization
    batch_size: int = Field(default=25000, description="Batch size for KPI processing")
    enable_time_partitioning: bool = Field(default=True, description="Enable time-based partitioning")
    partition_column: str = Field(default="measurement_time", description="Column for partitioning")
    
    # Quality control
    max_error_rate: float = Field(default=0.01, description="Maximum acceptable error rate")
    min_data_quality_score: float = Field(default=0.98, description="Minimum data quality score")
    
    model_config = ConfigDict(
        extra="allow"
    )
class KPIImporter(AbstractImporter, ValidationMixin, ProcessingMixin, PerformanceMixin):
    """Enhanced KPI importer with telecommunications-specific capabilities.
    
    This importer provides comprehensive KPI data processing with support for:
    - Multiple operator formats and network types
    - Advanced validation and quality checks
    - Geolocation and spatial analysis
    - Performance optimization for large datasets
    - Trend analysis and anomaly detection
    """
    
    # Standard KPI column mappings for different operators
    OPERATOR_SCHEMAS = {
        'telefonica': {
            'cell_id': 'celda_id',
            'measurement_time': 'fecha_hora_medicion',
            'availability': 'disponibilidad',
            'throughput_dl': 'velocidad_descarga',
            'throughput_ul': 'velocidad_subida',
            'latency': 'latencia',
            'packet_loss': 'perdida_paquetes',
            'signal_strength': 'intensidad_senal',
            'latitude': 'latitud',
            'longitude': 'longitud',
            'azimuth': 'azimut',
            'sector_id': 'sector_id'
        },
        'vodafone': {
            'cell_id': 'cell_id',
            'measurement_time': 'timestamp',
            'availability': 'availability_pct',
            'throughput_dl': 'dl_throughput_mbps',
            'throughput_ul': 'ul_throughput_mbps',
            'latency': 'latency_ms',
            'packet_loss': 'packet_loss_pct',
            'signal_strength': 'rsrp_dbm',
            'latitude': 'lat',
            'longitude': 'lon',
            'azimuth': 'azimuth_deg',
            'sector_id': 'sector'
        },
        'telekom': {
            'cell_id': 'zellen_id',
            'measurement_time': 'messzeit',
            'availability': 'verfuegbarkeit',
            'throughput_dl': 'download_mbps',
            'throughput_ul': 'upload_mbps',
            'latency': 'latenz_ms',
            'packet_loss': 'paketverlust',
            'signal_strength': 'signalstaerke',
            'latitude': 'breitengrad',
            'longitude': 'laengengrad',
            'azimuth': 'azimut_grad',
            'sector_id': 'sektor_id'
        },
        'generic': {
            'cell_id': 'cell_id',
            'measurement_time': 'measurement_time',
            'availability': 'availability',
            'throughput_dl': 'throughput_dl',
            'throughput_ul': 'throughput_ul',
            'latency': 'latency',
            'packet_loss': 'packet_loss',
            'signal_strength': 'signal_strength',
            'latitude': 'latitude',
            'longitude': 'longitude',
            'azimuth': 'azimuth',
            'sector_id': 'sector_id'
        }
    }
    
    # KPI thresholds by network type
    NETWORK_THRESHOLDS = {
        '2G': {
            'min_availability': 95.0,
            'max_latency': 1000.0,
            'min_signal_strength': -110.0,
            'max_packet_loss': 10.0
        },
        '3G': {
            'min_availability': 97.0,
            'max_latency': 500.0,
            'min_signal_strength': -105.0,
            'max_packet_loss': 5.0
        },
        '4G': {
            'min_availability': 99.0,
            'max_latency': 100.0,
            'min_signal_strength': -100.0,
            'max_packet_loss': 2.0
        },
        '5G': {
            'min_availability': 99.5,
            'max_latency': 50.0,
            'min_signal_strength': -95.0,
            'max_packet_loss': 1.0
        }
    }
    
    def __init__(self, config: Optional[Union[ImporterConfig, Dict[str, Any]]] = None, **kwargs):
        """Initialize KPI importer.
        
        Args:
            config: Importer configuration
            **kwargs: Additional configuration parameters
        """
        super().__init__(config=config, **kwargs)
        
        # KPI-specific configuration
        kpi_config_dict = kwargs.get('kpi_config', {})
        if isinstance(kpi_config_dict, KPIConfig):
            self.kpi_config = kpi_config_dict
        else:
            self.kpi_config = KPIConfig(**kpi_config_dict)
            
        # Set data type
        self.data_type = 'KPI'
        self.name = 'KPI Importer'
        self.supported_formats = ['.csv', '.xlsx', '.xls', '.json']
        
        # Required columns (will be mapped based on operator)
        self.required_columns = [
            'cell_id',
            'measurement_time'
        ]
        
        # Configure processing for KPI data
        self.configure_processing({
            'batch_size': self.kpi_config.batch_size,
            'enable_parallel': True,
            'memory_limit_mb': 1024,
            'use_polars': False  # Pandas for better telecom data handling
        })
        
        # Configure validation for KPI data
        self.configure_validation({
            'enable_strict_validation': True,
            'max_error_rate': self.kpi_config.max_error_rate,
            'enable_threshold_validation': self.kpi_config.enable_threshold_validation,
            'network_type': self.kpi_config.network_type
        })
        
        # Configure performance monitoring
        self.configure_performance({
            'enable_monitoring': True,
            'enable_telecom_optimizations': True,
            'kpi_batch_size': self.kpi_config.batch_size
        })
        
        self.logger.info(f"KPI Importer initialized for operator: {self.kpi_config.operator}, network: {self.kpi_config.network_type}")

    def get_table_name(self, filename: str) -> str:
        """Generate table name for KPI data using enhanced naming conventions.

        Args:
            filename: Source filename or full path

        Returns:
            str: Table name following pattern kpi_{metric_type}_{year}
        """
        try:
            from src.database.utils.table_naming import TableNamingManager

            # Get configuration
            config = getattr(self, 'config', {})

            naming_manager = TableNamingManager(config)

            # Convert filename to Path object (handle both filename and full path)
            file_path = Path(filename)

            # Generate table name using standardized logic
            # Pattern: kpi_{metric_type}_{year}
            table_name = naming_manager.generate_table_name(
                data_type="kpi",
                file_path=file_path
            )

            self.logger.info(f"Generated KPI table name '{table_name}' for file '{filename}'")
            return table_name

        except Exception as e:
            self.logger.warning(f"Failed to generate table name using pattern, falling back to simple naming: {e}")
            return self._generate_kpi_table_name_fallback(filename)

    def _generate_kpi_table_name_fallback(self, filename: str) -> str:
        """Fallback KPI table name generation with enhanced logic."""
        import re
        from pathlib import Path
        from datetime import datetime

        file_path = Path(filename)
        filename_only = file_path.stem

        # Extract year from filename or path
        year = str(datetime.now().year)  # Default
        year_match = re.search(r'\b(20\d{2})\b', filename_only)
        if year_match:
            year = year_match.group(1)

        # Extract metric type from filename
        metric_type = self._extract_metric_type_from_filename(filename_only)

        table_name = f"kpi_{metric_type}_{year}"
        self.logger.info(f"Using fallback KPI table name: '{table_name}'")
        return table_name

    def _extract_metric_type_from_filename(self, filename: str) -> str:
        """Extract metric type from KPI filename."""
        import re

        # Remove common suffixes and date patterns
        clean_name = re.sub(r'\s*\(\d{8}-\d{8}\)', '', filename)  # Remove date ranges
        clean_name = re.sub(r'_KPIs?$', '', clean_name, flags=re.IGNORECASE)  # Remove _KPI suffix
        clean_name = re.sub(r'\s*KPIs?\s*', '', clean_name, flags=re.IGNORECASE)  # Remove KPI word

        # Convert to lowercase and replace spaces/special chars with underscores
        metric_type = re.sub(r'[^\w]', '_', clean_name.lower())
        metric_type = re.sub(r'_+', '_', metric_type).strip('_')

        # Default if empty
        if not metric_type:
            metric_type = 'general'

        return metric_type

    async def import_data(self, source: Union[str, Path], **kwargs) -> ImportResult:
        """Import KPI data with comprehensive processing.
        
        Args:
            source: Data source (file path or connection string)
            **kwargs: Additional import parameters
            
        Returns:
            Import result with metrics and status
        """
        start_time = datetime.now()
        
        try:
            # Start performance monitoring
            await self.start_performance_monitoring()
            
            with self.track_operation("kpi_import", source=str(source)) as op_metrics:
                # Validate source
                if not await self.validate_source(source):
                    return ImportResult(
                        success=False,
                        records_imported=0,
                        records_failed=0,
                        source_path=str(source),
                        error_message="Source validation failed",
                        processing_time=0,
                        file_size_bytes=0,
                        metadata={"errors": ["Invalid or inaccessible data source"]}
                    )
                    
                # Load and process data
                data = await self.process_data_async(
                    source,
                    self._process_kpi_batch,
                    **kwargs
                )
                
                if data is None or len(data) == 0:
                    return ImportResult(
                        success=False,
                        records_imported=0,
                        records_failed=0,
                        source_path=str(source),
                        error_message="No data loaded or processed",
                        processing_time=0,
                        file_size_bytes=0,
                        metadata={}
                    )
                    
                # Update operation metrics
                op_metrics.records_processed = len(data)
                op_metrics.bytes_processed = data.memory_usage(deep=True).sum()
                
                # Validate processed data
                validation_result = await self.validate_telecom_data(data, 'KPI')
                
                if not validation_result.get('valid', True):
                    error_count = validation_result.get('summary', {}).get('errors', 0)
                    error_rate = error_count / len(data) if len(data) > 0 else 0
                    if error_rate > self.kpi_config.max_error_rate:
                        return ImportResult(
                            success=False,
                            records_imported=0,
                            records_failed=len(data),
                            source_path=str(source_path) if 'source_path' in locals() else '',
                            error_message=f"Data quality below threshold: {error_rate:.2%}",
                            processing_time=0,
                            file_size_bytes=0,
                            metadata={
                                "errors": [e.get('message', '') for e in validation_result.get('results', {}).get('errors', [])][:10]
                            }
                        )
                    else:
                        self.logger.warning(f"Data quality issues detected: {error_rate:.2%}")
                        
                # Perform KPI-specific analysis
                analysis_results = await self._perform_kpi_analysis(data)
                
                # Store data if database operations are available
                if hasattr(self, 'db_ops') and self.db_ops:
                    try:
                        await self._store_kpi_data(data)
                    except Exception as e:
                        self.logger.error(f"Database storage failed: {e}")
                        return ImportResult(
                            success=False,
                            records_imported=0,
                            records_failed=len(data),
                            source_path=str(source_path) if 'source_path' in locals() else '',
                            error_message=f"Database storage failed: {e}",
                            processing_time=(datetime.now() - start_time).total_seconds(),
                            file_size_bytes=0,
                            metadata={"errors": [str(e)]}
                        )
                        
                # Calculate processing time
                processing_time = (datetime.now() - start_time).total_seconds()
                
                # Calculate data quality score
                error_count = validation_result.get('summary', {}).get('errors', 0)
                data_quality_score = 1.0 - (error_count / len(data)) if len(data) > 0 else 1.0
                
                return ImportResult(
                    success=True,
                    records_imported=len(data),
                    records_failed=0,
                    source_path=str(source_path) if 'source_path' in locals() else '',
                    error_message='',
                    processing_time=processing_time,
                    file_size_bytes=0,
                    metadata={
                        "data_quality_score": data_quality_score,
                        "warnings": [w.get('message', '') for w in validation_result.get('results', {}).get('warnings', [])][:5],
                        "analysis_results": analysis_results
                    }
                )
                
        except Exception as e:
            self.logger.error(f"KPI import failed: {e}")
            return ImportResult(
                success=False,
                records_imported=0,
                records_failed=0,
                source_path=str(source_path) if 'source_path' in locals() else '',
                error_message=f"Import failed: {e}",
                processing_time=0,
                file_size_bytes=0,
                metadata={"errors": [str(e)]}
            )
            
        finally:
            await self.stop_performance_monitoring()
            
    async def _process_kpi_batch(self, batch: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """Process a batch of KPI data.
        
        Args:
            batch: Batch of KPI data
            **kwargs: Additional processing parameters
            
        Returns:
            Processed batch
        """
        try:
            # Apply operator-specific column mapping
            batch = self._map_operator_columns(batch)
            
            # Standardize data types
            batch = self._standardize_data_types(batch)
            
            # Process timestamps
            batch = self._process_timestamps(batch)
            
            # Calculate derived KPI metrics
            batch = self._calculate_derived_kpis(batch)
            
            # Process geolocation if enabled
            if self.kpi_config.enable_geolocation:
                batch = self._process_geolocation(batch)
                
            # Perform cell aggregation if enabled
            if self.kpi_config.enable_cell_aggregation:
                batch = self._aggregate_cell_kpis(batch)
                
            # Analyze sectors if enabled
            if self.kpi_config.enable_sector_analysis:
                batch = self._analyze_sectors(batch)
                
            # Detect anomalies if enabled
            if self.kpi_config.enable_anomaly_detection:
                batch = self._detect_kpi_anomalies(batch)
                
            # Add metadata
            batch['import_timestamp'] = datetime.now()
            batch['operator'] = self.kpi_config.operator
            batch['network_type'] = self.kpi_config.network_type
            batch['data_source'] = 'kpi_import'
            
            return batch
            
        except Exception as e:
            self.logger.error(f"KPI batch processing failed: {e}")
            raise
            
    def _map_operator_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """Map operator-specific column names to standard names."""
        schema = self.OPERATOR_SCHEMAS.get(self.kpi_config.operator, self.OPERATOR_SCHEMAS['generic'])
        
        # Create reverse mapping (operator columns to standard columns)
        column_mapping = {v: k for k, v in schema.items() if v in df.columns}
        
        if column_mapping:
            df = df.rename(columns=column_mapping)
            self.logger.info(f"Mapped {len(column_mapping)} columns for operator {self.kpi_config.operator}")
            
        # Remove duplicate columns and standardize column names
        from src.utils.column_deduplicator import ColumnDeduplicator
        
        # Remove duplicate columns, keeping the most complete one
        df, dedup_report = ColumnDeduplicator.remove_duplicate_columns(
            df, keep_strategy='best'
        )
        
        if dedup_report['total_removed'] > 0:
            self.logger.info(f"Removed {dedup_report['total_removed']} duplicate columns in KPI data")
        
        # Standardize column names to lowercase
        clean_columns = []
        for col in df.columns:
            clean_name = str(col).strip().lower()
            clean_columns.append(clean_name)
        
        df.columns = clean_columns
        
        return df
        
    def _standardize_data_types(self, df: pd.DataFrame) -> pd.DataFrame:
        """Standardize data types for KPI fields."""
        # Cell ID as string
        if 'cell_id' in df.columns:
            df['cell_id'] = df['cell_id'].astype(str).str.strip()
            
        # Numeric KPI fields
        numeric_fields = [
            'availability', 'throughput_dl', 'throughput_ul', 'latency',
            'packet_loss', 'signal_strength', 'latitude', 'longitude', 'azimuth'
        ]
        
        for field in numeric_fields:
            if field in df.columns:
                df[field] = pd.to_numeric(df[field], errors='coerce')
                
        return df
        
    def _process_timestamps(self, df: pd.DataFrame) -> pd.DataFrame:
        """Process and validate timestamps."""
        if 'measurement_time' in df.columns:
            try:
                df['measurement_time'] = pd.to_datetime(df['measurement_time'], errors='coerce')
                
                # Localize to configured timezone if needed
                if df['measurement_time'].dt.tz is None:
                    try:
                        df['measurement_time'] = df['measurement_time'].dt.tz_localize(
                            self.kpi_config.timezone, ambiguous='infer', nonexistent='shift_forward'
                        )
                    except TypeError:
                        # Fallback for older pandas versions
                        df['measurement_time'] = df['measurement_time'].dt.tz_localize(
                            self.kpi_config.timezone
                        )
                    
                # Create time-based columns for analysis
                df['measurement_date'] = df['measurement_time'].dt.date
                df['measurement_hour'] = df['measurement_time'].dt.hour
                df['measurement_day_of_week'] = df['measurement_time'].dt.dayofweek
                
            except Exception as e:
                self.logger.warning(f"Timestamp processing warning: {e}")
                
        return df
        
    def _calculate_derived_kpis(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate derived KPI metrics."""
        # Calculate total throughput
        if 'throughput_dl' in df.columns and 'throughput_ul' in df.columns:
            df['throughput_total'] = df['throughput_dl'].fillna(0) + df['throughput_ul'].fillna(0)
            
        # Calculate quality score based on multiple KPIs
        quality_components = []
        
        if 'availability' in df.columns:
            # Normalize availability (0-100 to 0-1)
            availability_norm = df['availability'].fillna(0) / 100.0
            quality_components.append(availability_norm * 0.3)  # 30% weight
            
        if 'latency' in df.columns:
            # Invert latency (lower is better) and normalize
            max_latency = self.NETWORK_THRESHOLDS[self.kpi_config.network_type]['max_latency']
            latency_norm = 1 - (df['latency'].fillna(max_latency) / max_latency).clip(0, 1)
            quality_components.append(latency_norm * 0.25)  # 25% weight
            
        if 'packet_loss' in df.columns:
            # Invert packet loss (lower is better) and normalize
            max_packet_loss = self.NETWORK_THRESHOLDS[self.kpi_config.network_type]['max_packet_loss']
            packet_loss_norm = 1 - (df['packet_loss'].fillna(max_packet_loss) / max_packet_loss).clip(0, 1)
            quality_components.append(packet_loss_norm * 0.25)  # 25% weight
            
        if 'signal_strength' in df.columns:
            # Normalize signal strength
            min_signal = self.NETWORK_THRESHOLDS[self.kpi_config.network_type]['min_signal_strength']
            signal_norm = (df['signal_strength'].fillna(min_signal) - min_signal) / abs(min_signal)
            signal_norm = signal_norm.clip(0, 1)
            quality_components.append(signal_norm * 0.2)  # 20% weight
            
        # Calculate overall quality score
        if quality_components:
            df['quality_score'] = sum(quality_components)
            df['quality_score'] = df['quality_score'].clip(0, 1) * 100  # Convert to percentage
            
        return df
        
    def _process_geolocation(self, df: pd.DataFrame) -> pd.DataFrame:
        """Process geolocation data and create geometry column."""
        if 'latitude' in df.columns and 'longitude' in df.columns:
            # Filter valid coordinates
            valid_coords = (
                df['latitude'].notna() & 
                df['longitude'].notna() &
                (df['latitude'].between(-90, 90)) &
                (df['longitude'].between(-180, 180))
            )
            
            # Create geometry column for valid coordinates
            if valid_coords.any():
                try:
                    from shapely.geometry import Point
                    df.loc[valid_coords, 'geometry'] = df.loc[valid_coords].apply(
                        lambda row: Point(row['longitude'], row['latitude']), axis=1
                    )
                except ImportError:
                    self.logger.warning("Shapely not available, skipping geometry creation")
                    
            self.logger.info(f"Processed geolocation for {valid_coords.sum()} records")
            
        return df
        
    def _aggregate_cell_kpis(self, df: pd.DataFrame) -> pd.DataFrame:
        """Aggregate KPIs at cell level if multiple measurements exist."""
        if 'cell_id' not in df.columns or 'measurement_time' not in df.columns:
            return df
            
        # Group by cell and time period based on aggregation level
        if self.kpi_config.aggregation_level == 'hourly':
            df['time_group'] = df['measurement_time'].dt.floor('H')
        elif self.kpi_config.aggregation_level == 'daily':
            df['time_group'] = df['measurement_time'].dt.floor('D')
        elif self.kpi_config.aggregation_level == 'weekly':
            df['time_group'] = df['measurement_time'].dt.to_period('W').dt.start_time
        else:
            return df  # No aggregation
            
        # Define aggregation functions for different KPI types
        agg_functions = {
            'availability': 'mean',
            'throughput_dl': 'mean',
            'throughput_ul': 'mean',
            'latency': 'mean',
            'packet_loss': 'mean',
            'signal_strength': 'mean',
            'quality_score': 'mean',
            'latitude': 'first',  # Assuming cell location doesn't change
            'longitude': 'first',
            'azimuth': 'first',
            'sector_id': 'first'
        }
        
        # Perform aggregation
        available_agg = {col: func for col, func in agg_functions.items() if col in df.columns}
        
        if available_agg:
            aggregated = df.groupby(['cell_id', 'time_group']).agg(available_agg).reset_index()
            aggregated['measurement_time'] = aggregated['time_group']
            aggregated = aggregated.drop('time_group', axis=1)
            
            self.logger.info(f"Aggregated {len(df)} records to {len(aggregated)} at {self.kpi_config.aggregation_level} level")
            return aggregated
            
        return df
        
    def _analyze_sectors(self, df: pd.DataFrame) -> pd.DataFrame:
        """Analyze sector-level performance."""
        if 'sector_id' not in df.columns or 'azimuth' not in df.columns:
            return df
            
        # Calculate sector statistics
        sector_stats = df.groupby('sector_id').agg({
            'availability': ['mean', 'std'],
            'throughput_dl': ['mean', 'std'],
            'quality_score': ['mean', 'std']
        }).round(2)
        
        # Flatten column names
        sector_stats.columns = ['_'.join(col).strip() for col in sector_stats.columns]
        sector_stats = sector_stats.reset_index()
        
        # Merge back to main dataframe
        df = df.merge(sector_stats, on='sector_id', how='left', suffixes=('', '_sector_avg'))
        
        return df
        
    def _detect_kpi_anomalies(self, df: pd.DataFrame) -> pd.DataFrame:
        """Detect anomalies in KPI data."""
        df['anomaly_flags'] = ''
        
        # Get network-specific thresholds
        thresholds = self.NETWORK_THRESHOLDS.get(self.kpi_config.network_type, self.NETWORK_THRESHOLDS['4G'])
        
        # Check availability anomalies
        if 'availability' in df.columns:
            low_availability = df['availability'] < thresholds['min_availability']
            df.loc[low_availability, 'anomaly_flags'] += 'LOW_AVAILABILITY;'
            
        # Check latency anomalies
        if 'latency' in df.columns:
            high_latency = df['latency'] > thresholds['max_latency']
            df.loc[high_latency, 'anomaly_flags'] += 'HIGH_LATENCY;'
            
        # Check signal strength anomalies
        if 'signal_strength' in df.columns:
            weak_signal = df['signal_strength'] < thresholds['min_signal_strength']
            df.loc[weak_signal, 'anomaly_flags'] += 'WEAK_SIGNAL;'
            
        # Check packet loss anomalies
        if 'packet_loss' in df.columns:
            high_packet_loss = df['packet_loss'] > thresholds['max_packet_loss']
            df.loc[high_packet_loss, 'anomaly_flags'] += 'HIGH_PACKET_LOSS;'
            
        # Clean up anomaly flags
        df['anomaly_flags'] = df['anomaly_flags'].str.rstrip(';')
        df['has_anomaly'] = df['anomaly_flags'] != ''
        
        anomaly_count = df['has_anomaly'].sum()
        if anomaly_count > 0:
            self.logger.warning(f"Detected {anomaly_count} KPI anomalies")
            
        return df
        
    async def _perform_kpi_analysis(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Perform comprehensive KPI analysis."""
        analysis = {
            'total_records': len(data),
            'unique_cells': data['cell_id'].nunique() if 'cell_id' in data.columns else 0,
            'time_range': {},
            'kpi_summary': {},
            'quality_metrics': {},
            'anomalies': {}
        }
        
        # Time range analysis
        if 'measurement_time' in data.columns:
            analysis['time_range'] = {
                'start': data['measurement_time'].min().isoformat() if data['measurement_time'].notna().any() else None,
                'end': data['measurement_time'].max().isoformat() if data['measurement_time'].notna().any() else None,
                'duration_hours': (data['measurement_time'].max() - data['measurement_time'].min()).total_seconds() / 3600 if data['measurement_time'].notna().any() else 0
            }
            
        # KPI summary statistics
        kpi_columns = ['availability', 'throughput_dl', 'throughput_ul', 'latency', 'packet_loss', 'signal_strength']
        for col in kpi_columns:
            if col in data.columns:
                analysis['kpi_summary'][col] = {
                    'mean': float(data[col].mean()) if data[col].notna().any() else None,
                    'median': float(data[col].median()) if data[col].notna().any() else None,
                    'std': float(data[col].std()) if data[col].notna().any() else None,
                    'min': float(data[col].min()) if data[col].notna().any() else None,
                    'max': float(data[col].max()) if data[col].notna().any() else None
                }
                
        # Quality metrics
        if 'quality_score' in data.columns:
            analysis['quality_metrics'] = {
                'average_quality': float(data['quality_score'].mean()) if data['quality_score'].notna().any() else None,
                'quality_distribution': data['quality_score'].describe().to_dict() if data['quality_score'].notna().any() else {}
            }
            
        # Anomaly analysis
        if 'has_anomaly' in data.columns:
            analysis['anomalies'] = {
                'total_anomalies': int(data['has_anomaly'].sum()),
                'anomaly_rate': float(data['has_anomaly'].mean()) if len(data) > 0 else 0,
                'anomaly_types': data[data['has_anomaly']]['anomaly_flags'].value_counts().to_dict() if data['has_anomaly'].any() else {}
            }
            
        return analysis
        
    async def _store_kpi_data(self, data: pd.DataFrame) -> None:
        """Store KPI data in database with partitioning."""
        if not hasattr(self, 'db_ops') or not self.db_ops:
            raise ValueError("Database operations not configured")
            
        # Generate table name using enhanced naming logic
        # Try to get source path from context
        source_path = getattr(self, 'current_source_path', None)
        if source_path:
            table_name = self.get_table_name(str(source_path))
        else:
            # Fallback to network type based naming
            table_name = f'kpi_data_{self.kpi_config.network_type.lower()}'
        
        # Apply time partitioning if enabled
        if self.kpi_config.enable_time_partitioning and 'measurement_date' in data.columns:
            # Group by date and store in separate partitions
            for measurement_date, group in data.groupby('measurement_date'):
                partition_table = f"{table_name}_{measurement_date.strftime('%Y_%m')}"
                await self.db_ops.store_dataframe(
                    group, 
                    partition_table,
                    if_exists='append'
                )
        else:
            # Store in single table
            await self.db_ops.store_dataframe(
                data, 
                table_name,
                if_exists='append'
            )
            
    async def validate_source(self, source: Union[str, Path]) -> bool:
        """Validate KPI data source.
        
        Args:
            source: Data source to validate
            
        Returns:
            True if source is valid
        """
        try:
            source_path = Path(source)
            
            # Check if file exists
            if not source_path.exists():
                self.logger.error(f"Source file does not exist: {source}")
                return False
                
            # Check file format
            if source_path.suffix.lower() not in self.supported_formats:
                self.logger.error(f"Unsupported file format: {source_path.suffix}")
                return False
                
            # Check file size (basic validation)
            file_size_mb = source_path.stat().st_size / 1024 / 1024
            if file_size_mb > 2000:  # 2GB limit for KPI files
                self.logger.warning(f"Large KPI file detected: {file_size_mb:.1f}MB")
                
            return True
            
        except Exception as e:
            self.logger.error(f"Source validation failed: {e}")
            return False
            
    async def get_source_info(self, source: Union[str, Path]) -> Dict[str, Any]:
        """Get information about KPI data source.
        
        Args:
            source: Data source
            
        Returns:
            Source information dictionary
        """
        try:
            source_path = Path(source)
            stat = source_path.stat()
            
            return {
                'file_path': str(source_path),
                'file_name': source_path.name,
                'file_size_bytes': stat.st_size,
                'file_size_mb': stat.st_size / 1024 / 1024,
                'file_format': source_path.suffix.lower(),
                'modified_time': datetime.fromtimestamp(stat.st_mtime),
                'is_supported': source_path.suffix.lower() in self.supported_formats,
                'estimated_records': self._estimate_record_count(source_path),
                'network_type': self.kpi_config.network_type,
                'operator': self.kpi_config.operator
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get source info: {e}")
            return {'error': str(e)}
            
    def _estimate_record_count(self, file_path: Path) -> Optional[int]:
        """Estimate number of records in file."""
        try:
            file_size_mb = file_path.stat().st_size / 1024 / 1024
            
            # Rough estimation based on file size and format
            if file_path.suffix.lower() == '.csv':
                # Assume ~150 bytes per KPI record on average
                return int(file_size_mb * 1024 * 1024 / 150)
            elif file_path.suffix.lower() in ['.xlsx', '.xls']:
                # Excel files are more compressed
                return int(file_size_mb * 1024 * 1024 / 120)
            else:
                return None
                
        except Exception:
            return None
            
    def get_operator_schema(self, operator: str) -> Dict[str, str]:
        """Get column schema for specific operator.
        
        Args:
            operator: Operator name
            
        Returns:
            Column mapping dictionary
        """
        return self.OPERATOR_SCHEMAS.get(operator, self.OPERATOR_SCHEMAS['generic'])
        
    def get_supported_operators(self) -> List[str]:
        """Get list of supported operators.
        
        Returns:
            List of supported operator names
        """
        return list(self.OPERATOR_SCHEMAS.keys())
        
    def get_network_thresholds(self, network_type: str) -> Dict[str, float]:
        """Get KPI thresholds for specific network type.
        
        Args:
            network_type: Network type (2G, 3G, 4G, 5G)
            
        Returns:
            Threshold dictionary
        """
        return self.NETWORK_THRESHOLDS.get(network_type, self.NETWORK_THRESHOLDS['4G'])