#!/usr/bin/env pwsh
# Connect命令的一劳永逸解决方案 - PowerShell版本
# 确保使用正确的Poetry环境执行connect命令
# 解决 ModuleNotFoundError: No module named 'i<PERSON><PERSON>' 问题

param(
    [Parameter(ValueFromRemainingArguments = $true)]
    [string[]]$Arguments
)

# 设置错误处理
$ErrorActionPreference = "Stop"

try {
    # 切换到项目根目录
    Set-Location $PSScriptRoot
    
    # Check if Poetry is available
    try {
        $poetryVersion = poetry --version 2>$null
        Write-Host "[INFO] Poetry detected: $poetryVersion" -ForegroundColor Green
    }
    catch {
        Write-Host "[ERROR] Poetry not installed or not in PATH" -ForegroundColor Red
        Write-Host "Please install Poetry: https://python-poetry.org/docs/#installation" -ForegroundColor Yellow
        exit 1
    }
    
    # Check if pyproject.toml exists
    if (-not (Test-Path "pyproject.toml")) {
        Write-Host "[ERROR] pyproject.toml file not found" -ForegroundColor Red
        Write-Host "Please ensure you are running this script from the project root directory" -ForegroundColor Yellow
        exit 1
    }
    
    # Display execution information
    $commandArgs = if ($Arguments) { $Arguments -join " " } else { "" }
    Write-Host "[INFO] Executing with Poetry environment: connect $commandArgs" -ForegroundColor Cyan
    Write-Host "[INFO] Project directory: $(Get-Location)" -ForegroundColor Cyan
    Write-Host ""
    
    # 执行connect命令
    if ($Arguments) {
        poetry run connect @Arguments
    } else {
        poetry run connect
    }
    
    $exitCode = $LASTEXITCODE
    
    # If command failed, provide help information
    if ($exitCode -ne 0) {
        Write-Host ""
        Write-Host "[TIPS] If you encounter module import errors, try:" -ForegroundColor Yellow
        Write-Host "  1. poetry install  # Reinstall dependencies" -ForegroundColor Yellow
        Write-Host "  2. poetry env remove python; poetry install  # Rebuild environment" -ForegroundColor Yellow
        Write-Host "  3. poetry run python -c 'import ijson; print(\"ijson OK\")'  # Verify ijson" -ForegroundColor Yellow
    }
    
    exit $exitCode
}
catch {
    Write-Host "[ERROR] Exception occurred during execution: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}