<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">8%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-07-25 14:44 +0200
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_04e2950766bc0f15___init___py.html">src\api\__init__.py</a></td>
                <td class="name left"><a href="z_04e2950766bc0f15___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_04e2950766bc0f15_import_api_py.html#t73">src\api\import_api.py</a></td>
                <td class="name left"><a href="z_04e2950766bc0f15_import_api_py.html#t73"><data value='ImportRequest'>ImportRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_04e2950766bc0f15_import_api_py.html#t86">src\api\import_api.py</a></td>
                <td class="name left"><a href="z_04e2950766bc0f15_import_api_py.html#t86"><data value='ImportResponse'>ImportResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_04e2950766bc0f15_import_api_py.html#t97">src\api\import_api.py</a></td>
                <td class="name left"><a href="z_04e2950766bc0f15_import_api_py.html#t97"><data value='JobStatusResponse'>JobStatusResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_04e2950766bc0f15_import_api_py.html#t111">src\api\import_api.py</a></td>
                <td class="name left"><a href="z_04e2950766bc0f15_import_api_py.html#t111"><data value='PerformanceMetricsResponse'>PerformanceMetricsResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_04e2950766bc0f15_import_api_py.html">src\api\import_api.py</a></td>
                <td class="name left"><a href="z_04e2950766bc0f15_import_api_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>133</td>
                <td>133</td>
                <td>3</td>
                <td class="right" data-ratio="0 133">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997___init___py.html">src\config\__init__.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>15</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="12 15">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_core_py.html#t26">src\config\core.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_core_py.html#t26"><data value='ConnectConfigManager'>ConnectConfigManager</data></a></td>
                <td>149</td>
                <td>149</td>
                <td>0</td>
                <td class="right" data-ratio="0 149">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_core_py.html">src\config\core.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_core_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>56</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="43 56">77%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_environment_py.html#t20">src\config\environment.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_environment_py.html#t20"><data value='EnvironmentManager'>EnvironmentManager</data></a></td>
                <td>158</td>
                <td>158</td>
                <td>0</td>
                <td class="right" data-ratio="0 158">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_environment_py.html">src\config\environment.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_environment_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t20">src\config\loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html#t20"><data value='ConfigLoader'>ConfigLoader</data></a></td>
                <td>100</td>
                <td>100</td>
                <td>0</td>
                <td class="right" data-ratio="0 100">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html">src\config\loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>32</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="23 32">72%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t25">src\config\models.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t25"><data value='Environment'>Environment</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t33">src\config\models.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t33"><data value='LogLevel'>LogLevel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t42">src\config\models.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t42"><data value='DatabasePoolConfig'>DatabasePoolConfig</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t57">src\config\models.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t57"><data value='DatabaseConnectionConfig'>DatabaseConnectionConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t65">src\config\models.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t65"><data value='DatabaseConfig'>DatabaseConfig</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t85">src\config\models.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t85"><data value='LoggingConfig'>LoggingConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t101">src\config\models.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t101"><data value='ProjectConfig'>ProjectConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t108">src\config\models.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t108"><data value='GeoConfig'>GeoConfig</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t136">src\config\models.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t136"><data value='QGISConfig'>QGISConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t146">src\config\models.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t146"><data value='DataConfig'>DataConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t161">src\config\models.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t161"><data value='CDRConfig'>CDRConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t169">src\config\models.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t169"><data value='EPConfig'>EPConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t177">src\config\models.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t177"><data value='KPIAlertThresholds'>KPIAlertThresholds</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t184">src\config\models.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t184"><data value='KPIConfig'>KPIConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t192">src\config\models.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t192"><data value='PerformanceConfig'>PerformanceConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t199">src\config\models.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t199"><data value='TelecomConfig'>TelecomConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t207">src\config\models.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t207"><data value='JWTConfig'>JWTConfig</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t222">src\config\models.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t222"><data value='PasswordConfig'>PasswordConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t231">src\config\models.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t231"><data value='SecurityConfig'>SecurityConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t237">src\config\models.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t237"><data value='AlertThresholds'>AlertThresholds</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t245">src\config\models.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t245"><data value='AlertsConfig'>AlertsConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t251">src\config\models.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t251"><data value='MonitoringConfig'>MonitoringConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t261">src\config\models.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t261"><data value='ProcessingConfig'>ProcessingConfig</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t282">src\config\models.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t282"><data value='EnvironmentConfig'>EnvironmentConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t289">src\config\models.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html#t289"><data value='ConnectConfig'>ConnectConfig</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html">src\config\models.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>182</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="177 182">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t26">src\config\settings.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t26"><data value='Settings'>Settings</data></a></td>
                <td>147</td>
                <td>147</td>
                <td>0</td>
                <td class="right" data-ratio="0 147">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t514">src\config\settings.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t514"><data value='ConfigManager'>ConfigManager</data></a></td>
                <td>122</td>
                <td>122</td>
                <td>0</td>
                <td class="right" data-ratio="0 122">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html">src\config\settings.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>50</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="45 50">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3348db692d34d8b7___init___py.html">src\connect_types\__init__.py</a></td>
                <td class="name left"><a href="z_3348db692d34d8b7___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t43">src\connect_types\telecom_types.py</a></td>
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t43"><data value='DataSourceType'>DataSourceType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t53">src\connect_types\telecom_types.py</a></td>
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t53"><data value='ProcessingStatus'>ProcessingStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t63">src\connect_types\telecom_types.py</a></td>
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t63"><data value='SignalType'>SignalType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t71">src\connect_types\telecom_types.py</a></td>
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t71"><data value='NetworkTechnology'>NetworkTechnology</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t79">src\connect_types\telecom_types.py</a></td>
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t79"><data value='CallStatus'>CallStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t88">src\connect_types\telecom_types.py</a></td>
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t88"><data value='CallType'>CallType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t99">src\connect_types\telecom_types.py</a></td>
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t99"><data value='CDRRecord'>CDRRecord</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t117">src\connect_types\telecom_types.py</a></td>
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t117"><data value='EPRecord'>EPRecord</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t133">src\connect_types\telecom_types.py</a></td>
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t133"><data value='KPIRecord'>KPIRecord</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t148">src\connect_types\telecom_types.py</a></td>
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t148"><data value='ImportResult'>ImportResult</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t165">src\connect_types\telecom_types.py</a></td>
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t165"><data value='ValidationResult'>ValidationResult</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t181">src\connect_types\telecom_types.py</a></td>
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t181"><data value='ProcessingMetrics'>ProcessingMetrics</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t193">src\connect_types\telecom_types.py</a></td>
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t193"><data value='DatabaseConfig'>DatabaseConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t209">src\connect_types\telecom_types.py</a></td>
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t209"><data value='GeospatialConfig'>GeospatialConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t220">src\connect_types\telecom_types.py</a></td>
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t220"><data value='ProcessingConfig'>ProcessingConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t231">src\connect_types\telecom_types.py</a></td>
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t231"><data value='TelecomConfig'>TelecomConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t242">src\connect_types\telecom_types.py</a></td>
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t242"><data value='ProcessingResult'>ProcessingResult</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t259">src\connect_types\telecom_types.py</a></td>
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t259"><data value='DataImporter'>DataImporter</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>4</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t271">src\connect_types\telecom_types.py</a></td>
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t271"><data value='DataProcessor'>DataProcessor</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>4</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t283">src\connect_types\telecom_types.py</a></td>
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t283"><data value='KPICalculator'>KPICalculator</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>4</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t296">src\connect_types\telecom_types.py</a></td>
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html#t296"><data value='DataContainer'>DataContainer</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html">src\connect_types\telecom_types.py</a></td>
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>209</td>
                <td>209</td>
                <td>18</td>
                <td class="right" data-ratio="0 209">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41___init___py.html">src\core\__init__.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912___init___py.html">src\core\data_processing\__init__.py</a></td>
                <td class="name left"><a href="z_cc98060f564b2912___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_685d3cbf13dcde2b___init___py.html">src\core\data_processing\adapters\__init__.py</a></td>
                <td class="name left"><a href="z_685d3cbf13dcde2b___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_685d3cbf13dcde2b_adapter_factory_py.html#t22">src\core\data_processing\adapters\adapter_factory.py</a></td>
                <td class="name left"><a href="z_685d3cbf13dcde2b_adapter_factory_py.html#t22"><data value='AdapterFactory'>AdapterFactory</data></a></td>
                <td>120</td>
                <td>120</td>
                <td>0</td>
                <td class="right" data-ratio="0 120">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_685d3cbf13dcde2b_adapter_factory_py.html">src\core\data_processing\adapters\adapter_factory.py</a></td>
                <td class="name left"><a href="z_685d3cbf13dcde2b_adapter_factory_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_685d3cbf13dcde2b_base_adapter_py.html#t30">src\core\data_processing\adapters\base_adapter.py</a></td>
                <td class="name left"><a href="z_685d3cbf13dcde2b_base_adapter_py.html#t30"><data value='AdapterError'>AdapterError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_685d3cbf13dcde2b_base_adapter_py.html#t35">src\core\data_processing\adapters\base_adapter.py</a></td>
                <td class="name left"><a href="z_685d3cbf13dcde2b_base_adapter_py.html#t35"><data value='EngineNotAvailableError'>EngineNotAvailableError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_685d3cbf13dcde2b_base_adapter_py.html#t40">src\core\data_processing\adapters\base_adapter.py</a></td>
                <td class="name left"><a href="z_685d3cbf13dcde2b_base_adapter_py.html#t40"><data value='MemoryLimitExceededError'>MemoryLimitExceededError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_685d3cbf13dcde2b_base_adapter_py.html#t45">src\core\data_processing\adapters\base_adapter.py</a></td>
                <td class="name left"><a href="z_685d3cbf13dcde2b_base_adapter_py.html#t45"><data value='ProcessingTimeoutError'>ProcessingTimeoutError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_685d3cbf13dcde2b_base_adapter_py.html#t50">src\core\data_processing\adapters\base_adapter.py</a></td>
                <td class="name left"><a href="z_685d3cbf13dcde2b_base_adapter_py.html#t50"><data value='BaseAdapter'>BaseAdapter</data></a></td>
                <td>71</td>
                <td>71</td>
                <td>91</td>
                <td class="right" data-ratio="0 71">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_685d3cbf13dcde2b_base_adapter_py.html">src\core\data_processing\adapters\base_adapter.py</a></td>
                <td class="name left"><a href="z_685d3cbf13dcde2b_base_adapter_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>32</td>
                <td>32</td>
                <td>26</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_685d3cbf13dcde2b_pandas_adapter_py.html#t33">src\core\data_processing\adapters\pandas_adapter.py</a></td>
                <td class="name left"><a href="z_685d3cbf13dcde2b_pandas_adapter_py.html#t33"><data value='PandasAdapter'>PandasAdapter</data></a></td>
                <td>230</td>
                <td>230</td>
                <td>0</td>
                <td class="right" data-ratio="0 230">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_685d3cbf13dcde2b_pandas_adapter_py.html">src\core\data_processing\adapters\pandas_adapter.py</a></td>
                <td class="name left"><a href="z_685d3cbf13dcde2b_pandas_adapter_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_685d3cbf13dcde2b_polars_adapter_py.html#t45">src\core\data_processing\adapters\polars_adapter.py</a></td>
                <td class="name left"><a href="z_685d3cbf13dcde2b_polars_adapter_py.html#t45"><data value='PolarsAdapter'>PolarsAdapter</data></a></td>
                <td>272</td>
                <td>272</td>
                <td>0</td>
                <td class="right" data-ratio="0 272">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_685d3cbf13dcde2b_polars_adapter_py.html">src\core\data_processing\adapters\polars_adapter.py</a></td>
                <td class="name left"><a href="z_685d3cbf13dcde2b_polars_adapter_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_batch_processor_py.html#t33">src\core\data_processing\batch_processor.py</a></td>
                <td class="name left"><a href="z_cc98060f564b2912_batch_processor_py.html#t33"><data value='BatchStrategy'>BatchStrategy</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_batch_processor_py.html#t42">src\core\data_processing\batch_processor.py</a></td>
                <td class="name left"><a href="z_cc98060f564b2912_batch_processor_py.html#t42"><data value='BatchMode'>BatchMode</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_batch_processor_py.html#t51">src\core\data_processing\batch_processor.py</a></td>
                <td class="name left"><a href="z_cc98060f564b2912_batch_processor_py.html#t51"><data value='BatchConfig'>BatchConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_batch_processor_py.html#t88">src\core\data_processing\batch_processor.py</a></td>
                <td class="name left"><a href="z_cc98060f564b2912_batch_processor_py.html#t88"><data value='BatchInfo'>BatchInfo</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_batch_processor_py.html#t102">src\core\data_processing\batch_processor.py</a></td>
                <td class="name left"><a href="z_cc98060f564b2912_batch_processor_py.html#t102"><data value='BatchResult'>BatchResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_batch_processor_py.html#t116">src\core\data_processing\batch_processor.py</a></td>
                <td class="name left"><a href="z_cc98060f564b2912_batch_processor_py.html#t116"><data value='BatchProcessingResult'>BatchProcessingResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_batch_processor_py.html#t133">src\core\data_processing\batch_processor.py</a></td>
                <td class="name left"><a href="z_cc98060f564b2912_batch_processor_py.html#t133"><data value='BatchProcessor'>BatchProcessor</data></a></td>
                <td>300</td>
                <td>300</td>
                <td>0</td>
                <td class="right" data-ratio="0 300">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_batch_processor_py.html">src\core\data_processing\batch_processor.py</a></td>
                <td class="name left"><a href="z_cc98060f564b2912_batch_processor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>105</td>
                <td>105</td>
                <td>0</td>
                <td class="right" data-ratio="0 105">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_csv_processor_py.html#t29">src\core\data_processing\csv_processor.py</a></td>
                <td class="name left"><a href="z_cc98060f564b2912_csv_processor_py.html#t29"><data value='CSVProcessor'>CSVProcessor</data></a></td>
                <td>186</td>
                <td>186</td>
                <td>0</td>
                <td class="right" data-ratio="0 186">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_csv_processor_py.html">src\core\data_processing\csv_processor.py</a></td>
                <td class="name left"><a href="z_cc98060f564b2912_csv_processor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_data_cleaner_py.html#t31">src\core\data_processing\data_cleaner.py</a></td>
                <td class="name left"><a href="z_cc98060f564b2912_data_cleaner_py.html#t31"><data value='DataCleaner'>DataCleaner</data></a></td>
                <td>419</td>
                <td>419</td>
                <td>0</td>
                <td class="right" data-ratio="0 419">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_data_cleaner_py.html">src\core\data_processing\data_cleaner.py</a></td>
                <td class="name left"><a href="z_cc98060f564b2912_data_cleaner_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_excel_processor_py.html#t28">src\core\data_processing\excel_processor.py</a></td>
                <td class="name left"><a href="z_cc98060f564b2912_excel_processor_py.html#t28"><data value='ExcelProcessor'>ExcelProcessor</data></a></td>
                <td>218</td>
                <td>218</td>
                <td>0</td>
                <td class="right" data-ratio="0 218">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_excel_processor_py.html">src\core\data_processing\excel_processor.py</a></td>
                <td class="name left"><a href="z_cc98060f564b2912_excel_processor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_transformer_py.html#t31">src\core\data_processing\transformer.py</a></td>
                <td class="name left"><a href="z_cc98060f564b2912_transformer_py.html#t31"><data value='TransformationType'>TransformationType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_transformer_py.html#t51">src\core\data_processing\transformer.py</a></td>
                <td class="name left"><a href="z_cc98060f564b2912_transformer_py.html#t51"><data value='AggregationFunction'>AggregationFunction</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_transformer_py.html#t67">src\core\data_processing\transformer.py</a></td>
                <td class="name left"><a href="z_cc98060f564b2912_transformer_py.html#t67"><data value='TransformationRule'>TransformationRule</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_transformer_py.html#t79">src\core\data_processing\transformer.py</a></td>
                <td class="name left"><a href="z_cc98060f564b2912_transformer_py.html#t79"><data value='TransformationPipeline'>TransformationPipeline</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_transformer_py.html#t90">src\core\data_processing\transformer.py</a></td>
                <td class="name left"><a href="z_cc98060f564b2912_transformer_py.html#t90"><data value='TransformationResult'>TransformationResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_transformer_py.html#t101">src\core\data_processing\transformer.py</a></td>
                <td class="name left"><a href="z_cc98060f564b2912_transformer_py.html#t101"><data value='DataTransformer'>DataTransformer</data></a></td>
                <td>386</td>
                <td>386</td>
                <td>0</td>
                <td class="right" data-ratio="0 386">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_transformer_py.html">src\core\data_processing\transformer.py</a></td>
                <td class="name left"><a href="z_cc98060f564b2912_transformer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>96</td>
                <td>96</td>
                <td>0</td>
                <td class="right" data-ratio="0 96">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_types_py.html#t23">src\core\data_processing\types.py</a></td>
                <td class="name left"><a href="z_cc98060f564b2912_types_py.html#t23"><data value='ProcessingEngine'>ProcessingEngine</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_types_py.html#t31">src\core\data_processing\types.py</a></td>
                <td class="name left"><a href="z_cc98060f564b2912_types_py.html#t31"><data value='ProcessingMode'>ProcessingMode</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_types_py.html#t39">src\core\data_processing\types.py</a></td>
                <td class="name left"><a href="z_cc98060f564b2912_types_py.html#t39"><data value='DataFormat'>DataFormat</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_types_py.html#t51">src\core\data_processing\types.py</a></td>
                <td class="name left"><a href="z_cc98060f564b2912_types_py.html#t51"><data value='ProcessingStatus'>ProcessingStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_types_py.html#t63">src\core\data_processing\types.py</a></td>
                <td class="name left"><a href="z_cc98060f564b2912_types_py.html#t63"><data value='QualityLevel'>QualityLevel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_types_py.html#t73">src\core\data_processing\types.py</a></td>
                <td class="name left"><a href="z_cc98060f564b2912_types_py.html#t73"><data value='ProcessingMetrics'>ProcessingMetrics</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_types_py.html#t136">src\core\data_processing\types.py</a></td>
                <td class="name left"><a href="z_cc98060f564b2912_types_py.html#t136"><data value='ProcessingResult'>ProcessingResult</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_types_py.html#t164">src\core\data_processing\types.py</a></td>
                <td class="name left"><a href="z_cc98060f564b2912_types_py.html#t164"><data value='ProcessingConfig'>ProcessingConfig</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_types_py.html#t215">src\core\data_processing\types.py</a></td>
                <td class="name left"><a href="z_cc98060f564b2912_types_py.html#t215"><data value='ChunkInfo'>ChunkInfo</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_types_py.html#t229">src\core\data_processing\types.py</a></td>
                <td class="name left"><a href="z_cc98060f564b2912_types_py.html#t229"><data value='FileInfo'>FileInfo</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_types_py.html">src\core\data_processing\types.py</a></td>
                <td class="name left"><a href="z_cc98060f564b2912_types_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>144</td>
                <td>144</td>
                <td>0</td>
                <td class="right" data-ratio="0 144">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_890733416bc8af2f___init___py.html">src\core\utils\__init__.py</a></td>
                <td class="name left"><a href="z_890733416bc8af2f___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_890733416bc8af2f_encoding_py.html">src\core\utils\encoding.py</a></td>
                <td class="name left"><a href="z_890733416bc8af2f_encoding_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>52</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="0 52">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_890733416bc8af2f_logging_py.html#t27">src\core\utils\logging.py</a></td>
                <td class="name left"><a href="z_890733416bc8af2f_logging_py.html#t27"><data value='LogLevel'>LogLevel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_890733416bc8af2f_logging_py.html#t37">src\core\utils\logging.py</a></td>
                <td class="name left"><a href="z_890733416bc8af2f_logging_py.html#t37"><data value='LogContext'>LogContext</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_890733416bc8af2f_logging_py.html#t56">src\core\utils\logging.py</a></td>
                <td class="name left"><a href="z_890733416bc8af2f_logging_py.html#t56"><data value='TelecomLogEntry'>TelecomLogEntry</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_890733416bc8af2f_logging_py.html#t93">src\core\utils\logging.py</a></td>
                <td class="name left"><a href="z_890733416bc8af2f_logging_py.html#t93"><data value='TelecomFormatter'>TelecomFormatter</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_890733416bc8af2f_logging_py.html#t187">src\core\utils\logging.py</a></td>
                <td class="name left"><a href="z_890733416bc8af2f_logging_py.html#t187"><data value='TelecomLogger'>TelecomLogger</data></a></td>
                <td>69</td>
                <td>69</td>
                <td>0</td>
                <td class="right" data-ratio="0 69">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_890733416bc8af2f_logging_py.html">src\core\utils\logging.py</a></td>
                <td class="name left"><a href="z_890733416bc8af2f_logging_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>88</td>
                <td>88</td>
                <td>0</td>
                <td class="right" data-ratio="0 88">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_890733416bc8af2f_memory_py.html#t24">src\core\utils\memory.py</a></td>
                <td class="name left"><a href="z_890733416bc8af2f_memory_py.html#t24"><data value='MemoryError'>MemoryError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_890733416bc8af2f_memory_py.html#t29">src\core\utils\memory.py</a></td>
                <td class="name left"><a href="z_890733416bc8af2f_memory_py.html#t29"><data value='MemoryLimitExceededError'>MemoryLimitExceededError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_890733416bc8af2f_memory_py.html#t35">src\core\utils\memory.py</a></td>
                <td class="name left"><a href="z_890733416bc8af2f_memory_py.html#t35"><data value='MemorySnapshot'>MemorySnapshot</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_890733416bc8af2f_memory_py.html#t48">src\core\utils\memory.py</a></td>
                <td class="name left"><a href="z_890733416bc8af2f_memory_py.html#t48"><data value='MemoryStats'>MemoryStats</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_890733416bc8af2f_memory_py.html#t62">src\core\utils\memory.py</a></td>
                <td class="name left"><a href="z_890733416bc8af2f_memory_py.html#t62"><data value='MemoryMonitor'>MemoryMonitor</data></a></td>
                <td>165</td>
                <td>165</td>
                <td>0</td>
                <td class="right" data-ratio="0 165">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_890733416bc8af2f_memory_py.html">src\core\utils\memory.py</a></td>
                <td class="name left"><a href="z_890733416bc8af2f_memory_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>58</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="0 58">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_890733416bc8af2f_performance_py.html#t24">src\core\utils\performance.py</a></td>
                <td class="name left"><a href="z_890733416bc8af2f_performance_py.html#t24"><data value='PerformanceMetrics'>PerformanceMetrics</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_890733416bc8af2f_performance_py.html#t55">src\core\utils\performance.py</a></td>
                <td class="name left"><a href="z_890733416bc8af2f_performance_py.html#t55"><data value='PerformanceStats'>PerformanceStats</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_890733416bc8af2f_performance_py.html#t78">src\core\utils\performance.py</a></td>
                <td class="name left"><a href="z_890733416bc8af2f_performance_py.html#t78"><data value='PerformanceMonitor'>PerformanceMonitor</data></a></td>
                <td>131</td>
                <td>131</td>
                <td>0</td>
                <td class="right" data-ratio="0 131">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_890733416bc8af2f_performance_py.html">src\core\utils\performance.py</a></td>
                <td class="name left"><a href="z_890733416bc8af2f_performance_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>65</td>
                <td>65</td>
                <td>0</td>
                <td class="right" data-ratio="0 65">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_890733416bc8af2f_validation_py.html#t24">src\core\utils\validation.py</a></td>
                <td class="name left"><a href="z_890733416bc8af2f_validation_py.html#t24"><data value='ValidationError'>ValidationError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_890733416bc8af2f_validation_py.html#t29">src\core\utils\validation.py</a></td>
                <td class="name left"><a href="z_890733416bc8af2f_validation_py.html#t29"><data value='FileValidationError'>FileValidationError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_890733416bc8af2f_validation_py.html#t34">src\core\utils\validation.py</a></td>
                <td class="name left"><a href="z_890733416bc8af2f_validation_py.html#t34"><data value='DataValidationError'>DataValidationError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_890733416bc8af2f_validation_py.html#t39">src\core\utils\validation.py</a></td>
                <td class="name left"><a href="z_890733416bc8af2f_validation_py.html#t39"><data value='TelecomValidationError'>TelecomValidationError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_890733416bc8af2f_validation_py.html#t45">src\core\utils\validation.py</a></td>
                <td class="name left"><a href="z_890733416bc8af2f_validation_py.html#t45"><data value='ValidationRule'>ValidationRule</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_890733416bc8af2f_validation_py.html#t55">src\core\utils\validation.py</a></td>
                <td class="name left"><a href="z_890733416bc8af2f_validation_py.html#t55"><data value='ValidationResult'>ValidationResult</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_890733416bc8af2f_validation_py.html#t80">src\core\utils\validation.py</a></td>
                <td class="name left"><a href="z_890733416bc8af2f_validation_py.html#t80"><data value='DataValidator'>DataValidator</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_890733416bc8af2f_validation_py.html#t480">src\core\utils\validation.py</a></td>
                <td class="name left"><a href="z_890733416bc8af2f_validation_py.html#t480"><data value='TelecomValidator'>TelecomValidator</data></a></td>
                <td>80</td>
                <td>80</td>
                <td>0</td>
                <td class="right" data-ratio="0 80">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_890733416bc8af2f_validation_py.html">src\core\utils\validation.py</a></td>
                <td class="name left"><a href="z_890733416bc8af2f_validation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>158</td>
                <td>158</td>
                <td>0</td>
                <td class="right" data-ratio="0 158">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d___init___py.html">src\database\__init__.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_config_py.html#t192">src\database\config.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_config_py.html#t192"><data value='SettingsProxy'>SettingsProxy</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_config_py.html">src\database\config.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>75</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="19 75">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85e7da93a84ca6b6___init___py.html">src\database\connection\__init__.py</a></td>
                <td class="name left"><a href="z_85e7da93a84ca6b6___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85e7da93a84ca6b6_adaptive_pool_py.html#t20">src\database\connection\adaptive_pool.py</a></td>
                <td class="name left"><a href="z_85e7da93a84ca6b6_adaptive_pool_py.html#t20"><data value='PoolMetrics'>PoolMetrics</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85e7da93a84ca6b6_adaptive_pool_py.html#t33">src\database\connection\adaptive_pool.py</a></td>
                <td class="name left"><a href="z_85e7da93a84ca6b6_adaptive_pool_py.html#t33"><data value='AdaptiveConfig'>AdaptiveConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85e7da93a84ca6b6_adaptive_pool_py.html#t52">src\database\connection\adaptive_pool.py</a></td>
                <td class="name left"><a href="z_85e7da93a84ca6b6_adaptive_pool_py.html#t52"><data value='AdaptiveConnectionPool'>AdaptiveConnectionPool</data></a></td>
                <td>110</td>
                <td>110</td>
                <td>0</td>
                <td class="right" data-ratio="0 110">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85e7da93a84ca6b6_adaptive_pool_py.html">src\database\connection\adaptive_pool.py</a></td>
                <td class="name left"><a href="z_85e7da93a84ca6b6_adaptive_pool_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>45</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85e7da93a84ca6b6_health_check_py.html#t23">src\database\connection\health_check.py</a></td>
                <td class="name left"><a href="z_85e7da93a84ca6b6_health_check_py.html#t23"><data value='HealthStatus'>HealthStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85e7da93a84ca6b6_health_check_py.html#t33">src\database\connection\health_check.py</a></td>
                <td class="name left"><a href="z_85e7da93a84ca6b6_health_check_py.html#t33"><data value='HealthCheckResult'>HealthCheckResult</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85e7da93a84ca6b6_health_check_py.html#t56">src\database\connection\health_check.py</a></td>
                <td class="name left"><a href="z_85e7da93a84ca6b6_health_check_py.html#t56"><data value='HealthStats'>HealthStats</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85e7da93a84ca6b6_health_check_py.html#t115">src\database\connection\health_check.py</a></td>
                <td class="name left"><a href="z_85e7da93a84ca6b6_health_check_py.html#t115"><data value='HealthChecker'>HealthChecker</data></a></td>
                <td>180</td>
                <td>180</td>
                <td>0</td>
                <td class="right" data-ratio="0 180">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85e7da93a84ca6b6_health_check_py.html">src\database\connection\health_check.py</a></td>
                <td class="name left"><a href="z_85e7da93a84ca6b6_health_check_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>77</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="60 77">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85e7da93a84ca6b6_pool_py.html#t45">src\database\connection\pool.py</a></td>
                <td class="name left"><a href="z_85e7da93a84ca6b6_pool_py.html#t45"><data value='DatabasePoolManager'>DatabasePoolManager</data></a></td>
                <td>216</td>
                <td>216</td>
                <td>0</td>
                <td class="right" data-ratio="0 216">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85e7da93a84ca6b6_pool_py.html">src\database\connection\pool.py</a></td>
                <td class="name left"><a href="z_85e7da93a84ca6b6_pool_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>59</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="41 59">69%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85e7da93a84ca6b6_read_write_splitter_py.html#t47">src\database\connection\read_write_splitter.py</a></td>
                <td class="name left"><a href="z_85e7da93a84ca6b6_read_write_splitter_py.html#t47"><data value='LoadBalancingStrategy'>LoadBalancingStrategy</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85e7da93a84ca6b6_read_write_splitter_py.html#t55">src\database\connection\read_write_splitter.py</a></td>
                <td class="name left"><a href="z_85e7da93a84ca6b6_read_write_splitter_py.html#t55"><data value='ReadWriteSplitter'>ReadWriteSplitter</data></a></td>
                <td>182</td>
                <td>182</td>
                <td>0</td>
                <td class="right" data-ratio="0 182">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85e7da93a84ca6b6_read_write_splitter_py.html">src\database\connection\read_write_splitter.py</a></td>
                <td class="name left"><a href="z_85e7da93a84ca6b6_read_write_splitter_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>44</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="35 44">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85e7da93a84ca6b6_session_py.html#t43">src\database\connection\session.py</a></td>
                <td class="name left"><a href="z_85e7da93a84ca6b6_session_py.html#t43"><data value='SessionManager'>SessionManager</data></a></td>
                <td>60</td>
                <td>60</td>
                <td>0</td>
                <td class="right" data-ratio="0 60">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85e7da93a84ca6b6_session_py.html">src\database\connection\session.py</a></td>
                <td class="name left"><a href="z_85e7da93a84ca6b6_session_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>69</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="29 69">42%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_constants_py.html#t48">src\database\constants.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_constants_py.html#t48"><data value='ErrorCode'>ErrorCode</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_constants_py.html#t104">src\database\constants.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_constants_py.html#t104"><data value='DataSourceType'>DataSourceType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_constants_py.html#t116">src\database\constants.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_constants_py.html#t116"><data value='CellType'>CellType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_constants_py.html#t126">src\database\constants.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_constants_py.html#t126"><data value='ServiceType'>ServiceType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_constants_py.html#t136">src\database\constants.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_constants_py.html#t136"><data value='Operator'>Operator</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_constants_py.html#t145">src\database\constants.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_constants_py.html#t145"><data value='Priority'>Priority</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_constants_py.html#t155">src\database\constants.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_constants_py.html#t155"><data value='LogLevel'>LogLevel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_constants_py.html#t166">src\database\constants.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_constants_py.html#t166"><data value='SQLOperation'>SQLOperation</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_constants_py.html#t180">src\database\constants.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_constants_py.html#t180"><data value='IsolationLevel'>IsolationLevel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_constants_py.html#t190">src\database\constants.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_constants_py.html#t190"><data value='PoolState'>PoolState</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_constants_py.html">src\database\constants.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_constants_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>139</td>
                <td>139</td>
                <td>0</td>
                <td class="right" data-ratio="0 139">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8___init___py.html">src\database\etl\__init__.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>19</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="15 19">79%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_batch_processor_py.html#t28">src\database\etl\batch_processor.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_batch_processor_py.html#t28"><data value='BatchStrategy'>BatchStrategy</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_batch_processor_py.html#t38">src\database\etl\batch_processor.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_batch_processor_py.html#t38"><data value='ProcessingMode'>ProcessingMode</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_batch_processor_py.html#t47">src\database\etl\batch_processor.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_batch_processor_py.html#t47"><data value='BatchConfig'>BatchConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_batch_processor_py.html#t64">src\database\etl\batch_processor.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_batch_processor_py.html#t64"><data value='BatchInfo'>BatchInfo</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_batch_processor_py.html#t79">src\database\etl\batch_processor.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_batch_processor_py.html#t79"><data value='BatchResult'>BatchResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_batch_processor_py.html#t91">src\database\etl\batch_processor.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_batch_processor_py.html#t91"><data value='BatchProcessingResult'>BatchProcessingResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_batch_processor_py.html#t107">src\database\etl\batch_processor.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_batch_processor_py.html#t107"><data value='BatchProcessor'>BatchProcessor</data></a></td>
                <td>277</td>
                <td>277</td>
                <td>0</td>
                <td class="right" data-ratio="0 277">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_batch_processor_py.html">src\database\etl\batch_processor.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_batch_processor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>101</td>
                <td>101</td>
                <td>0</td>
                <td class="right" data-ratio="0 101">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_error_handler_py.html#t24">src\database\etl\error_handler.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_error_handler_py.html#t24"><data value='ErrorSeverity'>ErrorSeverity</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_error_handler_py.html#t33">src\database\etl\error_handler.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_error_handler_py.html#t33"><data value='ErrorCategory'>ErrorCategory</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_error_handler_py.html#t48">src\database\etl\error_handler.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_error_handler_py.html#t48"><data value='RecoveryAction'>RecoveryAction</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_error_handler_py.html#t60">src\database\etl\error_handler.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_error_handler_py.html#t60"><data value='ErrorInfo'>ErrorInfo</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_error_handler_py.html#t78">src\database\etl\error_handler.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_error_handler_py.html#t78"><data value='RecoveryStrategy'>RecoveryStrategy</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_error_handler_py.html#t93">src\database\etl\error_handler.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_error_handler_py.html#t93"><data value='ErrorHandlingConfig'>ErrorHandlingConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_error_handler_py.html#t108">src\database\etl\error_handler.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_error_handler_py.html#t108"><data value='ErrorHandler'>ErrorHandler</data></a></td>
                <td>246</td>
                <td>246</td>
                <td>0</td>
                <td class="right" data-ratio="0 246">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_error_handler_py.html">src\database\etl\error_handler.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_error_handler_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>107</td>
                <td>107</td>
                <td>0</td>
                <td class="right" data-ratio="0 107">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_excel_processor_py.html#t42">src\database\etl\excel_processor.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_excel_processor_py.html#t42"><data value='ExcelReadOptions'>ExcelReadOptions</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_excel_processor_py.html#t66">src\database\etl\excel_processor.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_excel_processor_py.html#t66"><data value='ExcelWriteOptions'>ExcelWriteOptions</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_excel_processor_py.html#t84">src\database\etl\excel_processor.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_excel_processor_py.html#t84"><data value='CellStyle'>CellStyle</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_excel_processor_py.html#t103">src\database\etl\excel_processor.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_excel_processor_py.html#t103"><data value='ExcelProcessingResult'>ExcelProcessingResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_excel_processor_py.html#t116">src\database\etl\excel_processor.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_excel_processor_py.html#t116"><data value='ExcelProcessor'>ExcelProcessor</data></a></td>
                <td>260</td>
                <td>260</td>
                <td>0</td>
                <td class="right" data-ratio="0 260">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_excel_processor_py.html">src\database\etl\excel_processor.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_excel_processor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>110</td>
                <td>110</td>
                <td>0</td>
                <td class="right" data-ratio="0 110">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_extractor_py.html#t26">src\database\etl\extractor.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_extractor_py.html#t26"><data value='DataExtractor'>DataExtractor</data></a></td>
                <td>85</td>
                <td>85</td>
                <td>0</td>
                <td class="right" data-ratio="0 85">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_extractor_py.html">src\database\etl\extractor.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_extractor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_json_processor_py.html#t41">src\database\etl\json_processor.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_json_processor_py.html#t41"><data value='JSONFormat'>JSONFormat</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_json_processor_py.html#t50">src\database\etl\json_processor.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_json_processor_py.html#t50"><data value='CompressionType'>CompressionType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_json_processor_py.html#t60">src\database\etl\json_processor.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_json_processor_py.html#t60"><data value='JSONReadOptions'>JSONReadOptions</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_json_processor_py.html#t84">src\database\etl\json_processor.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_json_processor_py.html#t84"><data value='JSONWriteOptions'>JSONWriteOptions</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_json_processor_py.html#t105">src\database\etl\json_processor.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_json_processor_py.html#t105"><data value='JSONProcessingResult'>JSONProcessingResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_json_processor_py.html#t119">src\database\etl\json_processor.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_json_processor_py.html#t119"><data value='JSONProcessor'>JSONProcessor</data></a></td>
                <td>303</td>
                <td>303</td>
                <td>0</td>
                <td class="right" data-ratio="0 303">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_json_processor_py.html">src\database\etl\json_processor.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_json_processor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>130</td>
                <td>130</td>
                <td>0</td>
                <td class="right" data-ratio="0 130">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_loader_py.html#t27">src\database\etl\loader.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_loader_py.html#t27"><data value='DataLoader'>DataLoader</data></a></td>
                <td>145</td>
                <td>145</td>
                <td>0</td>
                <td class="right" data-ratio="0 145">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_loader_py.html">src\database\etl\loader.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_loader_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>27</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="27 27">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_pipeline_py.html#t22">src\database\etl\pipeline.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_pipeline_py.html#t22"><data value='DataCleaner'>DataCleaner</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_pipeline_py.html#t89">src\database\etl\pipeline.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_pipeline_py.html#t89"><data value='ETLPipeline'>ETLPipeline</data></a></td>
                <td>310</td>
                <td>310</td>
                <td>0</td>
                <td class="right" data-ratio="0 310">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_pipeline_py.html">src\database\etl\pipeline.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_pipeline_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>58</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="58 58">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d83242ce7f5e6032___init___py.html">src\database\etl\processors\__init__.py</a></td>
                <td class="name left"><a href="z_d83242ce7f5e6032___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d83242ce7f5e6032_csv_processor_py.html">src\database\etl\processors\csv_processor.py</a></td>
                <td class="name left"><a href="z_d83242ce7f5e6032_csv_processor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>244</td>
                <td>223</td>
                <td>0</td>
                <td class="right" data-ratio="21 244">9%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_transformer_py.html#t25">src\database\etl\transformer.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_transformer_py.html#t25"><data value='TransformationType'>TransformationType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_transformer_py.html#t35">src\database\etl\transformer.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_transformer_py.html#t35"><data value='DataType'>DataType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_transformer_py.html#t49">src\database\etl\transformer.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_transformer_py.html#t49"><data value='TransformationRule'>TransformationRule</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_transformer_py.html#t63">src\database\etl\transformer.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_transformer_py.html#t63"><data value='TransformationResult'>TransformationResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_transformer_py.html#t74">src\database\etl\transformer.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_transformer_py.html#t74"><data value='Transformer'>Transformer</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>10</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_transformer_py.html#t118">src\database\etl\transformer.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_transformer_py.html#t118"><data value='FieldTransformer'>FieldTransformer</data></a></td>
                <td>131</td>
                <td>131</td>
                <td>0</td>
                <td class="right" data-ratio="0 131">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_transformer_py.html#t470">src\database\etl\transformer.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_transformer_py.html#t470"><data value='ValidationTransformer'>ValidationTransformer</data></a></td>
                <td>81</td>
                <td>81</td>
                <td>0</td>
                <td class="right" data-ratio="0 81">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_transformer_py.html#t678">src\database\etl\transformer.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_transformer_py.html#t678"><data value='AggregationTransformer'>AggregationTransformer</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_transformer_py.html#t749">src\database\etl\transformer.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_transformer_py.html#t749"><data value='FilterTransformer'>FilterTransformer</data></a></td>
                <td>62</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="0 62">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_transformer_py.html#t935">src\database\etl\transformer.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_transformer_py.html#t935"><data value='DataTransformer'>DataTransformer</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_transformer_py.html#t987">src\database\etl\transformer.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_transformer_py.html#t987"><data value='TransformationPipeline'>TransformationPipeline</data></a></td>
                <td>61</td>
                <td>61</td>
                <td>0</td>
                <td class="right" data-ratio="0 61">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_transformer_py.html">src\database\etl\transformer.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_transformer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>93</td>
                <td>0</td>
                <td>4</td>
                <td class="right" data-ratio="93 93">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_validator_py.html#t36">src\database\etl\validator.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_validator_py.html#t36"><data value='ValidationType'>ValidationType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_validator_py.html#t51">src\database\etl\validator.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_validator_py.html#t51"><data value='ValidationSeverity'>ValidationSeverity</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_validator_py.html#t60">src\database\etl\validator.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_validator_py.html#t60"><data value='ValidationRule'>ValidationRule</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_validator_py.html#t75">src\database\etl\validator.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_validator_py.html#t75"><data value='ValidationIssue'>ValidationIssue</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_validator_py.html#t90">src\database\etl\validator.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_validator_py.html#t90"><data value='ValidationResult'>ValidationResult</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_validator_py.html#t136">src\database\etl\validator.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_validator_py.html#t136"><data value='DataValidator'>DataValidator</data></a></td>
                <td>359</td>
                <td>359</td>
                <td>0</td>
                <td class="right" data-ratio="0 359">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_validator_py.html">src\database\etl\validator.py</a></td>
                <td class="name left"><a href="z_d70c6fad0b89c4e8_validator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>107</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="99 107">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exception_handlers_py.html#t27">src\database\exception_handlers.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exception_handlers_py.html#t27"><data value='ExceptionHandlerConfig'>ExceptionHandlerConfig</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exception_handlers_py.html#t79">src\database\exception_handlers.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exception_handlers_py.html#t79"><data value='ErrorContext'>ErrorContext</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exception_handlers_py.html#t103">src\database\exception_handlers.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exception_handlers_py.html#t103"><data value='ExceptionHandler'>ExceptionHandler</data></a></td>
                <td>83</td>
                <td>80</td>
                <td>0</td>
                <td class="right" data-ratio="3 83">4%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exception_handlers_py.html">src\database\exception_handlers.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exception_handlers_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>61</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="42 61">69%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t13">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t13"><data value='DatabaseError'>DatabaseError</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>7</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t66">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t66"><data value='ConnectionError'>ConnectionError</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t106">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t106"><data value='ImportError'>ImportError</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t133">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t133"><data value='MonitoringError'>MonitoringError</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t160">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t160"><data value='OperationError'>OperationError</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t187">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t187"><data value='ValidationError'>ValidationError</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t223">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t223"><data value='SecurityError'>SecurityError</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t258">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t258"><data value='PipelineError'>PipelineError</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t293">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t293"><data value='GeospatialError'>GeospatialError</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t328">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t328"><data value='NoAvailableReplicasError'>NoAvailableReplicasError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t353">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t353"><data value='PrimaryDatabaseUnavailableError'>PrimaryDatabaseUnavailableError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t381">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t381"><data value='PermissionError'>PermissionError</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t420">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t420"><data value='QueryError'>QueryError</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t455">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t455"><data value='TransactionError'>TransactionError</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t488">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t488"><data value='TelecomDataError'>TelecomDataError</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t528">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t528"><data value='CDRProcessingError'>CDRProcessingError</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t570">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t570"><data value='EPProcessingError'>EPProcessingError</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t612">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t612"><data value='KPICalculationError'>KPICalculationError</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t654">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t654"><data value='NetworkCoverageError'>NetworkCoverageError</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t695">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t695"><data value='SchemaError'>SchemaError</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t734">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t734"><data value='ConfigurationError'>ConfigurationError</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t769">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t769"><data value='TimeoutError'>TimeoutError</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t804">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t804"><data value='TableNotFoundError'>TableNotFoundError</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t839">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t839"><data value='TableExistsError'>TableExistsError</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t874">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t874"><data value='SchemaNotFoundError'>SchemaNotFoundError</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t905">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t905"><data value='OptimizationError'>OptimizationError</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t940">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t940"><data value='FileOperationError'>FileOperationError</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t975">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t975"><data value='TransformationError'>TransformationError</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t1010">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t1010"><data value='ETLError'>ETLError</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t1041">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t1041"><data value='DataProcessingError'>DataProcessingError</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t1078">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t1078"><data value='BatchProcessingError'>BatchProcessingError</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t1117">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t1117"><data value='MemoryError'>MemoryError</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t1156">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t1156"><data value='CacheError'>CacheError</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t1195">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t1195"><data value='QGISError'>QGISError</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t1234">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t1234"><data value='PerformanceError'>PerformanceError</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t1277">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t1277"><data value='RetryableError'>RetryableError</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t1315">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html#t1315"><data value='NonRetryableError'>NonRetryableError</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html">src\database\exceptions.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>80</td>
                <td>0</td>
                <td>1</td>
                <td class="right" data-ratio="80 80">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21a03ec279c707f5___init___py.html">src\database\geospatial\__init__.py</a></td>
                <td class="name left"><a href="z_21a03ec279c707f5___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21a03ec279c707f5_indexer_py.html#t27">src\database\geospatial\indexer.py</a></td>
                <td class="name left"><a href="z_21a03ec279c707f5_indexer_py.html#t27"><data value='SpatialIndexer'>SpatialIndexer</data></a></td>
                <td>50</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="0 50">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21a03ec279c707f5_indexer_py.html#t218">src\database\geospatial\indexer.py</a></td>
                <td class="name left"><a href="z_21a03ec279c707f5_indexer_py.html#t218"><data value='RTreeIndexer'>RTreeIndexer</data></a></td>
                <td>73</td>
                <td>73</td>
                <td>0</td>
                <td class="right" data-ratio="0 73">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21a03ec279c707f5_indexer_py.html">src\database\geospatial\indexer.py</a></td>
                <td class="name left"><a href="z_21a03ec279c707f5_indexer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21a03ec279c707f5_polygon_handler_py.html#t39">src\database\geospatial\polygon_handler.py</a></td>
                <td class="name left"><a href="z_21a03ec279c707f5_polygon_handler_py.html#t39"><data value='PolygonHandler'>PolygonHandler</data></a></td>
                <td>152</td>
                <td>152</td>
                <td>0</td>
                <td class="right" data-ratio="0 152">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21a03ec279c707f5_polygon_handler_py.html">src\database\geospatial\polygon_handler.py</a></td>
                <td class="name left"><a href="z_21a03ec279c707f5_polygon_handler_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>36</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="28 36">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21a03ec279c707f5_processor_py.html#t41">src\database\geospatial\processor.py</a></td>
                <td class="name left"><a href="z_21a03ec279c707f5_processor_py.html#t41"><data value='GeospatialError'>GeospatialError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21a03ec279c707f5_processor_py.html#t47">src\database\geospatial\processor.py</a></td>
                <td class="name left"><a href="z_21a03ec279c707f5_processor_py.html#t47"><data value='GeospatialProcessor'>GeospatialProcessor</data></a></td>
                <td>126</td>
                <td>126</td>
                <td>0</td>
                <td class="right" data-ratio="0 126">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21a03ec279c707f5_processor_py.html">src\database\geospatial\processor.py</a></td>
                <td class="name left"><a href="z_21a03ec279c707f5_processor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>42</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="34 42">81%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21a03ec279c707f5_validator_py.html#t22">src\database\geospatial\validator.py</a></td>
                <td class="name left"><a href="z_21a03ec279c707f5_validator_py.html#t22"><data value='CoordinateValidator'>CoordinateValidator</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21a03ec279c707f5_validator_py.html#t100">src\database\geospatial\validator.py</a></td>
                <td class="name left"><a href="z_21a03ec279c707f5_validator_py.html#t100"><data value='GeometryValidator'>GeometryValidator</data></a></td>
                <td>180</td>
                <td>180</td>
                <td>0</td>
                <td class="right" data-ratio="0 180">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21a03ec279c707f5_validator_py.html">src\database\geospatial\validator.py</a></td>
                <td class="name left"><a href="z_21a03ec279c707f5_validator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>43</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="43 43">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21a03ec279c707f5_vendor_tagger_py.html#t41">src\database\geospatial\vendor_tagger.py</a></td>
                <td class="name left"><a href="z_21a03ec279c707f5_vendor_tagger_py.html#t41"><data value='VendorTagger'>VendorTagger</data></a></td>
                <td>167</td>
                <td>167</td>
                <td>0</td>
                <td class="right" data-ratio="0 167">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21a03ec279c707f5_vendor_tagger_py.html">src\database\geospatial\vendor_tagger.py</a></td>
                <td class="name left"><a href="z_21a03ec279c707f5_vendor_tagger_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>42</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="34 42">81%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08849049b6a766aa___init___py.html">src\database\monitoring\__init__.py</a></td>
                <td class="name left"><a href="z_08849049b6a766aa___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08849049b6a766aa_alerts_py.html#t5">src\database\monitoring\alerts.py</a></td>
                <td class="name left"><a href="z_08849049b6a766aa_alerts_py.html#t5"><data value='AlertSeverity'>AlertSeverity</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08849049b6a766aa_alerts_py.html#t11">src\database\monitoring\alerts.py</a></td>
                <td class="name left"><a href="z_08849049b6a766aa_alerts_py.html#t11"><data value='AlertRule'>AlertRule</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08849049b6a766aa_alerts_py.html#t48">src\database\monitoring\alerts.py</a></td>
                <td class="name left"><a href="z_08849049b6a766aa_alerts_py.html#t48"><data value='AlertManager'>AlertManager</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08849049b6a766aa_alerts_py.html">src\database\monitoring\alerts.py</a></td>
                <td class="name left"><a href="z_08849049b6a766aa_alerts_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08849049b6a766aa_health_py.html#t4">src\database\monitoring\health.py</a></td>
                <td class="name left"><a href="z_08849049b6a766aa_health_py.html#t4"><data value='HealthChecker'>HealthChecker</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08849049b6a766aa_health_py.html#t18">src\database\monitoring\health.py</a></td>
                <td class="name left"><a href="z_08849049b6a766aa_health_py.html#t18"><data value='SystemHealthChecker'>SystemHealthChecker</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08849049b6a766aa_health_py.html#t34">src\database\monitoring\health.py</a></td>
                <td class="name left"><a href="z_08849049b6a766aa_health_py.html#t34"><data value='HealthMonitor'>HealthMonitor</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08849049b6a766aa_health_py.html">src\database\monitoring\health.py</a></td>
                <td class="name left"><a href="z_08849049b6a766aa_health_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08849049b6a766aa_logger_py.html#t37">src\database\monitoring\logger.py</a></td>
                <td class="name left"><a href="z_08849049b6a766aa_logger_py.html#t37"><data value='DatabaseLogger'>DatabaseLogger</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08849049b6a766aa_logger_py.html#t91">src\database\monitoring\logger.py</a></td>
                <td class="name left"><a href="z_08849049b6a766aa_logger_py.html#t91"><data value='QueryLogger'>QueryLogger</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08849049b6a766aa_logger_py.html#t190">src\database\monitoring\logger.py</a></td>
                <td class="name left"><a href="z_08849049b6a766aa_logger_py.html#t190"><data value='PerformanceLogger'>PerformanceLogger</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08849049b6a766aa_logger_py.html">src\database\monitoring\logger.py</a></td>
                <td class="name left"><a href="z_08849049b6a766aa_logger_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>79</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="44 79">56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08849049b6a766aa_metrics_py.html#t24">src\database\monitoring\metrics.py</a></td>
                <td class="name left"><a href="z_08849049b6a766aa_metrics_py.html#t24"><data value='QueryMetrics'>QueryMetrics</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08849049b6a766aa_metrics_py.html#t36">src\database\monitoring\metrics.py</a></td>
                <td class="name left"><a href="z_08849049b6a766aa_metrics_py.html#t36"><data value='PoolMetrics'>PoolMetrics</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08849049b6a766aa_metrics_py.html#t50">src\database\monitoring\metrics.py</a></td>
                <td class="name left"><a href="z_08849049b6a766aa_metrics_py.html#t50"><data value='PerformanceMetrics'>PerformanceMetrics</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08849049b6a766aa_metrics_py.html#t62">src\database\monitoring\metrics.py</a></td>
                <td class="name left"><a href="z_08849049b6a766aa_metrics_py.html#t62"><data value='MetricsSummary'>MetricsSummary</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08849049b6a766aa_metrics_py.html#t77">src\database\monitoring\metrics.py</a></td>
                <td class="name left"><a href="z_08849049b6a766aa_metrics_py.html#t77"><data value='MetricsCollector'>MetricsCollector</data></a></td>
                <td>98</td>
                <td>98</td>
                <td>0</td>
                <td class="right" data-ratio="0 98">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08849049b6a766aa_metrics_py.html">src\database\monitoring\metrics.py</a></td>
                <td class="name left"><a href="z_08849049b6a766aa_metrics_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>73</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="66 73">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eb0a16ccbf01ec23___init___py.html">src\database\operations\__init__.py</a></td>
                <td class="name left"><a href="z_eb0a16ccbf01ec23___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eb0a16ccbf01ec23_bulk_operations_py.html#t22">src\database\operations\bulk_operations.py</a></td>
                <td class="name left"><a href="z_eb0a16ccbf01ec23_bulk_operations_py.html#t22"><data value='BulkOperations'>BulkOperations</data></a></td>
                <td>427</td>
                <td>427</td>
                <td>0</td>
                <td class="right" data-ratio="0 427">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eb0a16ccbf01ec23_bulk_operations_py.html">src\database\operations\bulk_operations.py</a></td>
                <td class="name left"><a href="z_eb0a16ccbf01ec23_bulk_operations_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eb0a16ccbf01ec23_crud_py.html#t25">src\database\operations\crud.py</a></td>
                <td class="name left"><a href="z_eb0a16ccbf01ec23_crud_py.html#t25"><data value='CRUDOperations'>CRUDOperations</data></a></td>
                <td>427</td>
                <td>427</td>
                <td>0</td>
                <td class="right" data-ratio="0 427">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eb0a16ccbf01ec23_crud_py.html">src\database\operations\crud.py</a></td>
                <td class="name left"><a href="z_eb0a16ccbf01ec23_crud_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>32</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="32 32">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eb0a16ccbf01ec23_database_manager_py.html#t41">src\database\operations\database_manager.py</a></td>
                <td class="name left"><a href="z_eb0a16ccbf01ec23_database_manager_py.html#t41"><data value='DatabaseManager'>DatabaseManager</data></a></td>
                <td>255</td>
                <td>255</td>
                <td>0</td>
                <td class="right" data-ratio="0 255">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eb0a16ccbf01ec23_database_manager_py.html">src\database\operations\database_manager.py</a></td>
                <td class="name left"><a href="z_eb0a16ccbf01ec23_database_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>37</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="28 37">76%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eb0a16ccbf01ec23_exporter_py.html#t31">src\database\operations\exporter.py</a></td>
                <td class="name left"><a href="z_eb0a16ccbf01ec23_exporter_py.html#t31"><data value='DataExporter'>DataExporter</data></a></td>
                <td>150</td>
                <td>150</td>
                <td>0</td>
                <td class="right" data-ratio="0 150">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eb0a16ccbf01ec23_exporter_py.html">src\database\operations\exporter.py</a></td>
                <td class="name left"><a href="z_eb0a16ccbf01ec23_exporter_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>27</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="23 27">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eb0a16ccbf01ec23_importer_py.html#t33">src\database\operations\importer.py</a></td>
                <td class="name left"><a href="z_eb0a16ccbf01ec23_importer_py.html#t33"><data value='DataImporter'>DataImporter</data></a></td>
                <td>499</td>
                <td>499</td>
                <td>0</td>
                <td class="right" data-ratio="0 499">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eb0a16ccbf01ec23_importer_py.html">src\database\operations\importer.py</a></td>
                <td class="name left"><a href="z_eb0a16ccbf01ec23_importer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>34</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="32 34">94%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eb0a16ccbf01ec23_table_operations_py.html#t23">src\database\operations\table_operations.py</a></td>
                <td class="name left"><a href="z_eb0a16ccbf01ec23_table_operations_py.html#t23"><data value='TableOperationManager'>TableOperationManager</data></a></td>
                <td>133</td>
                <td>133</td>
                <td>0</td>
                <td class="right" data-ratio="0 133">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eb0a16ccbf01ec23_table_operations_py.html">src\database\operations\table_operations.py</a></td>
                <td class="name left"><a href="z_eb0a16ccbf01ec23_table_operations_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eb0a16ccbf01ec23_transaction_manager_py.html#t15">src\database\operations\transaction_manager.py</a></td>
                <td class="name left"><a href="z_eb0a16ccbf01ec23_transaction_manager_py.html#t15"><data value='TransactionManager'>TransactionManager</data></a></td>
                <td>40</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="0 40">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eb0a16ccbf01ec23_transaction_manager_py.html">src\database\operations\transaction_manager.py</a></td>
                <td class="name left"><a href="z_eb0a16ccbf01ec23_transaction_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7___init___py.html">src\database\query_builder\__init__.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_aggregations_py.html#t17">src\database\query_builder\aggregations.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_aggregations_py.html#t17"><data value='AggregateFunction'>AggregateFunction</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_aggregations_py.html#t44">src\database\query_builder\aggregations.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_aggregations_py.html#t44"><data value='WindowFunction'>WindowFunction</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_aggregations_py.html#t60">src\database\query_builder\aggregations.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_aggregations_py.html#t60"><data value='Aggregation'>Aggregation</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>9</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_aggregations_py.html#t111">src\database\query_builder\aggregations.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_aggregations_py.html#t111"><data value='SimpleAggregation'>SimpleAggregation</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_aggregations_py.html#t164">src\database\query_builder\aggregations.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_aggregations_py.html#t164"><data value='ConditionalAggregation'>ConditionalAggregation</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_aggregations_py.html#t224">src\database\query_builder\aggregations.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_aggregations_py.html#t224"><data value='WindowAggregation'>WindowAggregation</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_aggregations_py.html#t309">src\database\query_builder\aggregations.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_aggregations_py.html#t309"><data value='CustomAggregation'>CustomAggregation</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_aggregations_py.html#t353">src\database\query_builder\aggregations.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_aggregations_py.html#t353"><data value='GroupBy'>GroupBy</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_aggregations_py.html">src\database\query_builder\aggregations.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_aggregations_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>100</td>
                <td>23</td>
                <td>2</td>
                <td class="right" data-ratio="77 100">77%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_builder_py.html#t23">src\database\query_builder\builder.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_builder_py.html#t23"><data value='QueryType'>QueryType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_builder_py.html#t36">src\database\query_builder\builder.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_builder_py.html#t36"><data value='QueryContext'>QueryContext</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_builder_py.html#t48">src\database\query_builder\builder.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_builder_py.html#t48"><data value='BaseQuery'>BaseQuery</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>6</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_builder_py.html#t123">src\database\query_builder\builder.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_builder_py.html#t123"><data value='SelectQuery'>SelectQuery</data></a></td>
                <td>101</td>
                <td>101</td>
                <td>0</td>
                <td class="right" data-ratio="0 101">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_builder_py.html#t404">src\database\query_builder\builder.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_builder_py.html#t404"><data value='InsertQuery'>InsertQuery</data></a></td>
                <td>55</td>
                <td>55</td>
                <td>0</td>
                <td class="right" data-ratio="0 55">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_builder_py.html#t561">src\database\query_builder\builder.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_builder_py.html#t561"><data value='UpdateQuery'>UpdateQuery</data></a></td>
                <td>46</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_builder_py.html#t685">src\database\query_builder\builder.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_builder_py.html#t685"><data value='DeleteQuery'>DeleteQuery</data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_builder_py.html#t785">src\database\query_builder\builder.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_builder_py.html#t785"><data value='QueryBuilder'>QueryBuilder</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_builder_py.html">src\database\query_builder\builder.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_builder_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>80</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="80 80">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_conditions_py.html#t17">src\database\query_builder\conditions.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_conditions_py.html#t17"><data value='Operator'>Operator</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_conditions_py.html#t44">src\database\query_builder\conditions.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_conditions_py.html#t44"><data value='Condition'>Condition</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>9</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_conditions_py.html#t118">src\database\query_builder\conditions.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_conditions_py.html#t118"><data value='SimpleCondition'>SimpleCondition</data></a></td>
                <td>53</td>
                <td>53</td>
                <td>0</td>
                <td class="right" data-ratio="0 53">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_conditions_py.html#t226">src\database\query_builder\conditions.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_conditions_py.html#t226"><data value='RawCondition'>RawCondition</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_conditions_py.html#t252">src\database\query_builder\conditions.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_conditions_py.html#t252"><data value='AndCondition'>AndCondition</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_conditions_py.html#t292">src\database\query_builder\conditions.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_conditions_py.html#t292"><data value='OrCondition'>OrCondition</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_conditions_py.html#t332">src\database\query_builder\conditions.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_conditions_py.html#t332"><data value='NotCondition'>NotCondition</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_conditions_py.html#t363">src\database\query_builder\conditions.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_conditions_py.html#t363"><data value='FieldCondition'>FieldCondition</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_conditions_py.html">src\database\query_builder\conditions.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_conditions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>118</td>
                <td>32</td>
                <td>2</td>
                <td class="right" data-ratio="86 118">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_dialects_py.html#t16">src\database\query_builder\dialects.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_dialects_py.html#t16"><data value='DatabaseType'>DatabaseType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_dialects_py.html#t31">src\database\query_builder\dialects.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_dialects_py.html#t31"><data value='Dialect'>Dialect</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>43</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_dialects_py.html#t147">src\database\query_builder\dialects.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_dialects_py.html#t147"><data value='PostgreSQLDialect'>PostgreSQLDialect</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_dialects_py.html#t283">src\database\query_builder\dialects.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_dialects_py.html#t283"><data value='MySQLDialect'>MySQLDialect</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_dialects_py.html#t417">src\database\query_builder\dialects.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_dialects_py.html#t417"><data value='SQLiteDialect'>SQLiteDialect</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_dialects_py.html#t539">src\database\query_builder\dialects.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_dialects_py.html#t539"><data value='OracleDialect'>OracleDialect</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_dialects_py.html#t643">src\database\query_builder\dialects.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_dialects_py.html#t643"><data value='DialectFactory'>DialectFactory</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_dialects_py.html">src\database\query_builder\dialects.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_dialects_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>83</td>
                <td>15</td>
                <td>10</td>
                <td class="right" data-ratio="68 83">82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_joins_py.html#t18">src\database\query_builder\joins.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_joins_py.html#t18"><data value='JoinType'>JoinType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_joins_py.html#t34">src\database\query_builder\joins.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_joins_py.html#t34"><data value='Join'>Join</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>9</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_joins_py.html#t97">src\database\query_builder\joins.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_joins_py.html#t97"><data value='ConditionalJoin'>ConditionalJoin</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_joins_py.html#t142">src\database\query_builder\joins.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_joins_py.html#t142"><data value='UsingJoin'>UsingJoin</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_joins_py.html#t195">src\database\query_builder\joins.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_joins_py.html#t195"><data value='CrossJoin'>CrossJoin</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_joins_py.html#t229">src\database\query_builder\joins.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_joins_py.html#t229"><data value='NaturalJoin'>NaturalJoin</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_joins_py.html#t278">src\database\query_builder\joins.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_joins_py.html#t278"><data value='SubqueryJoin'>SubqueryJoin</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_joins_py.html#t322">src\database\query_builder\joins.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_joins_py.html#t322"><data value='InnerJoin'>InnerJoin</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_joins_py.html#t343">src\database\query_builder\joins.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_joins_py.html#t343"><data value='LeftJoin'>LeftJoin</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_joins_py.html#t364">src\database\query_builder\joins.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_joins_py.html#t364"><data value='RightJoin'>RightJoin</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_joins_py.html#t385">src\database\query_builder\joins.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_joins_py.html#t385"><data value='FullJoin'>FullJoin</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_joins_py.html#t406">src\database\query_builder\joins.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_joins_py.html#t406"><data value='LeftOuterJoin'>LeftOuterJoin</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_joins_py.html#t427">src\database\query_builder\joins.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_joins_py.html#t427"><data value='RightOuterJoin'>RightOuterJoin</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_joins_py.html">src\database\query_builder\joins.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_joins_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>72</td>
                <td>11</td>
                <td>2</td>
                <td class="right" data-ratio="61 72">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_optimizers_py.html#t19">src\database\query_builder\optimizers.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_optimizers_py.html#t19"><data value='OptimizationLevel'>OptimizationLevel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_optimizers_py.html#t28">src\database\query_builder\optimizers.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_optimizers_py.html#t28"><data value='OptimizationResult'>OptimizationResult</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_optimizers_py.html#t42">src\database\query_builder\optimizers.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_optimizers_py.html#t42"><data value='Optimizer'>Optimizer</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>14</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_optimizers_py.html#t76">src\database\query_builder\optimizers.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_optimizers_py.html#t76"><data value='SelectOptimizer'>SelectOptimizer</data></a></td>
                <td>71</td>
                <td>71</td>
                <td>0</td>
                <td class="right" data-ratio="0 71">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_optimizers_py.html#t303">src\database\query_builder\optimizers.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_optimizers_py.html#t303"><data value='SubqueryOptimizer'>SubqueryOptimizer</data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_optimizers_py.html#t431">src\database\query_builder\optimizers.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_optimizers_py.html#t431"><data value='IndexOptimizer'>IndexOptimizer</data></a></td>
                <td>44</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="0 44">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_optimizers_py.html#t576">src\database\query_builder\optimizers.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_optimizers_py.html#t576"><data value='CompositeOptimizer'>CompositeOptimizer</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_optimizers_py.html">src\database\query_builder\optimizers.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_optimizers_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>55</td>
                <td>6</td>
                <td>7</td>
                <td class="right" data-ratio="49 55">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_validators_py.html#t18">src\database\query_builder\validators.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_validators_py.html#t18"><data value='ValidationLevel'>ValidationLevel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_validators_py.html#t26">src\database\query_builder\validators.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_validators_py.html#t26"><data value='SecurityLevel'>SecurityLevel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_validators_py.html#t34">src\database\query_builder\validators.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_validators_py.html#t34"><data value='Validator'>Validator</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>14</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_validators_py.html#t68">src\database\query_builder\validators.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_validators_py.html#t68"><data value='SQLInjectionValidator'>SQLInjectionValidator</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_validators_py.html#t223">src\database\query_builder\validators.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_validators_py.html#t223"><data value='SyntaxValidator'>SyntaxValidator</data></a></td>
                <td>39</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_validators_py.html#t407">src\database\query_builder\validators.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_validators_py.html#t407"><data value='PerformanceValidator'>PerformanceValidator</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_validators_py.html#t499">src\database\query_builder\validators.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_validators_py.html#t499"><data value='CompositeValidator'>CompositeValidator</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_validators_py.html">src\database\query_builder\validators.py</a></td>
                <td class="name left"><a href="z_41f753dd6039bbb7_validators_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>47</td>
                <td>6</td>
                <td>7</td>
                <td class="right" data-ratio="41 47">87%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d55c021fc51bfda7___init___py.html">src\database\schema\__init__.py</a></td>
                <td class="name left"><a href="z_d55c021fc51bfda7___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d55c021fc51bfda7_manager_py.html#t33">src\database\schema\manager.py</a></td>
                <td class="name left"><a href="z_d55c021fc51bfda7_manager_py.html#t33"><data value='SchemaManager'>SchemaManager</data></a></td>
                <td>264</td>
                <td>264</td>
                <td>0</td>
                <td class="right" data-ratio="0 264">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d55c021fc51bfda7_manager_py.html">src\database\schema\manager.py</a></td>
                <td class="name left"><a href="z_d55c021fc51bfda7_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d55c021fc51bfda7_models_py.html#t33">src\database\schema\models.py</a></td>
                <td class="name left"><a href="z_d55c021fc51bfda7_models_py.html#t33"><data value='TableDefinition'>TableDefinition</data></a></td>
                <td>43</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d55c021fc51bfda7_models_py.html">src\database\schema\models.py</a></td>
                <td class="name left"><a href="z_d55c021fc51bfda7_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>51</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="23 51">45%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d55c021fc51bfda7_router_py.html#t33">src\database\schema\router.py</a></td>
                <td class="name left"><a href="z_d55c021fc51bfda7_router_py.html#t33"><data value='RoutingStrategy'>RoutingStrategy</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d55c021fc51bfda7_router_py.html#t43">src\database\schema\router.py</a></td>
                <td class="name left"><a href="z_d55c021fc51bfda7_router_py.html#t43"><data value='RoutingRule'>RoutingRule</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d55c021fc51bfda7_router_py.html#t54">src\database\schema\router.py</a></td>
                <td class="name left"><a href="z_d55c021fc51bfda7_router_py.html#t54"><data value='SchemaRoutingError'>SchemaRoutingError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d55c021fc51bfda7_router_py.html#t59">src\database\schema\router.py</a></td>
                <td class="name left"><a href="z_d55c021fc51bfda7_router_py.html#t59"><data value='SchemaRouter'>SchemaRouter</data></a></td>
                <td>143</td>
                <td>143</td>
                <td>0</td>
                <td class="right" data-ratio="0 143">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d55c021fc51bfda7_router_py.html">src\database\schema\router.py</a></td>
                <td class="name left"><a href="z_d55c021fc51bfda7_router_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>52</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="44 52">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d55c021fc51bfda7_table_schema_py.html#t12">src\database\schema\table_schema.py</a></td>
                <td class="name left"><a href="z_d55c021fc51bfda7_table_schema_py.html#t12"><data value='ColumnSchema'>ColumnSchema</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d55c021fc51bfda7_table_schema_py.html#t48">src\database\schema\table_schema.py</a></td>
                <td class="name left"><a href="z_d55c021fc51bfda7_table_schema_py.html#t48"><data value='IndexSchema'>IndexSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d55c021fc51bfda7_table_schema_py.html#t57">src\database\schema\table_schema.py</a></td>
                <td class="name left"><a href="z_d55c021fc51bfda7_table_schema_py.html#t57"><data value='TableSchema'>TableSchema</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d55c021fc51bfda7_table_schema_py.html">src\database\schema\table_schema.py</a></td>
                <td class="name left"><a href="z_d55c021fc51bfda7_table_schema_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>32</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="32 32">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d55c021fc51bfda7_validators_py.html#t24">src\database\schema\validators.py</a></td>
                <td class="name left"><a href="z_d55c021fc51bfda7_validators_py.html#t24"><data value='ValidationSeverity'>ValidationSeverity</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d55c021fc51bfda7_validators_py.html#t34">src\database\schema\validators.py</a></td>
                <td class="name left"><a href="z_d55c021fc51bfda7_validators_py.html#t34"><data value='ValidationResult'>ValidationResult</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d55c021fc51bfda7_validators_py.html#t49">src\database\schema\validators.py</a></td>
                <td class="name left"><a href="z_d55c021fc51bfda7_validators_py.html#t49"><data value='ColumnDefinition'>ColumnDefinition</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d55c021fc51bfda7_validators_py.html#t63">src\database\schema\validators.py</a></td>
                <td class="name left"><a href="z_d55c021fc51bfda7_validators_py.html#t63"><data value='TableStructure'>TableStructure</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d55c021fc51bfda7_validators_py.html#t73">src\database\schema\validators.py</a></td>
                <td class="name left"><a href="z_d55c021fc51bfda7_validators_py.html#t73"><data value='SchemaValidator'>SchemaValidator</data></a></td>
                <td>161</td>
                <td>161</td>
                <td>0</td>
                <td class="right" data-ratio="0 161">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d55c021fc51bfda7_validators_py.html">src\database\schema\validators.py</a></td>
                <td class="name left"><a href="z_d55c021fc51bfda7_validators_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>50</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="50 50">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d30ff5053af0e8e___init___py.html">src\database\security\__init__.py</a></td>
                <td class="name left"><a href="z_3d30ff5053af0e8e___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d30ff5053af0e8e_access_control_py.html#t20">src\database\security\access_control.py</a></td>
                <td class="name left"><a href="z_3d30ff5053af0e8e_access_control_py.html#t20"><data value='Permission'>Permission</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d30ff5053af0e8e_access_control_py.html#t28">src\database\security\access_control.py</a></td>
                <td class="name left"><a href="z_3d30ff5053af0e8e_access_control_py.html#t28"><data value='Role'>Role</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d30ff5053af0e8e_access_control_py.html#t37">src\database\security\access_control.py</a></td>
                <td class="name left"><a href="z_3d30ff5053af0e8e_access_control_py.html#t37"><data value='AccessRule'>AccessRule</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d30ff5053af0e8e_access_control_py.html#t45">src\database\security\access_control.py</a></td>
                <td class="name left"><a href="z_3d30ff5053af0e8e_access_control_py.html#t45"><data value='AccessController'>AccessController</data></a></td>
                <td>42</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="0 42">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d30ff5053af0e8e_access_control_py.html">src\database\security\access_control.py</a></td>
                <td class="name left"><a href="z_3d30ff5053af0e8e_access_control_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d30ff5053af0e8e_audit_py.html#t6">src\database\security\audit.py</a></td>
                <td class="name left"><a href="z_3d30ff5053af0e8e_audit_py.html#t6"><data value='AuditLogger'>AuditLogger</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d30ff5053af0e8e_audit_py.html#t30">src\database\security\audit.py</a></td>
                <td class="name left"><a href="z_3d30ff5053af0e8e_audit_py.html#t30"><data value='AuditTrailManager'>AuditTrailManager</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d30ff5053af0e8e_audit_py.html">src\database\security\audit.py</a></td>
                <td class="name left"><a href="z_3d30ff5053af0e8e_audit_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d30ff5053af0e8e_auth_py.html#t3">src\database\security\auth.py</a></td>
                <td class="name left"><a href="z_3d30ff5053af0e8e_auth_py.html#t3"><data value='AuthenticationManager'>AuthenticationManager</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d30ff5053af0e8e_auth_py.html">src\database\security\auth.py</a></td>
                <td class="name left"><a href="z_3d30ff5053af0e8e_auth_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2b497613b0db077d___init___py.html">src\database\security\encryption\__init__.py</a></td>
                <td class="name left"><a href="z_2b497613b0db077d___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2b497613b0db077d_encryption_py.html#t3">src\database\security\encryption\encryption.py</a></td>
                <td class="name left"><a href="z_2b497613b0db077d_encryption_py.html#t3"><data value='DataEncryption'>DataEncryption</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2b497613b0db077d_encryption_py.html">src\database\security\encryption\encryption.py</a></td>
                <td class="name left"><a href="z_2b497613b0db077d_encryption_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_types_py.html#t104">src\database\types.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_types_py.html#t104"><data value='ColumnDefinition'>ColumnDefinition</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_types_py.html#t118">src\database\types.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_types_py.html#t118"><data value='TableDefinition'>TableDefinition</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_types_py.html#t129">src\database\types.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_types_py.html#t129"><data value='DataSourceMapping'>DataSourceMapping</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_types_py.html#t140">src\database\types.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_types_py.html#t140"><data value='FileMetadata'>FileMetadata</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_types_py.html#t151">src\database\types.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_types_py.html#t151"><data value='ProcessingResult'>ProcessingResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_types_py.html#t162">src\database\types.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_types_py.html#t162"><data value='QueryResult'>QueryResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_types_py.html#t170">src\database\types.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_types_py.html#t170"><data value='BulkOperationResult'>BulkOperationResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_types_py.html#t180">src\database\types.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_types_py.html#t180"><data value='ConnectionInfo'>ConnectionInfo</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_types_py.html#t192">src\database\types.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_types_py.html#t192"><data value='PoolStats'>PoolStats</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_types_py.html#t203">src\database\types.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_types_py.html#t203"><data value='QueryMetrics'>QueryMetrics</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_types_py.html#t216">src\database\types.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_types_py.html#t216"><data value='Connectable'>Connectable</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>4</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_types_py.html#t231">src\database\types.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_types_py.html#t231"><data value='Configurable'>Configurable</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>4</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_types_py.html#t244">src\database\types.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_types_py.html#t244"><data value='Validatable'>Validatable</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_types_py.html#t253">src\database\types.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_types_py.html#t253"><data value='DataProcessor'>DataProcessor</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_types_py.html">src\database\types.py</a></td>
                <td class="name left"><a href="z_67d056e3707ef46d_types_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>153</td>
                <td>3</td>
                <td>26</td>
                <td class="right" data-ratio="150 153">98%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642___init___py.html">src\database\utils\__init__.py</a></td>
                <td class="name left"><a href="z_a72551420c087642___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642_batch_processor_py.html#t366">src\database\utils\batch_processor.py</a></td>
                <td class="name left"><a href="z_a72551420c087642_batch_processor_py.html#t366"><data value='BatchProcessor'>BatchProcessor</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642_batch_processor_py.html">src\database\utils\batch_processor.py</a></td>
                <td class="name left"><a href="z_a72551420c087642_batch_processor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>127</td>
                <td>110</td>
                <td>0</td>
                <td class="right" data-ratio="17 127">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642_column_mapper_py.html#t14">src\database\utils\column_mapper.py</a></td>
                <td class="name left"><a href="z_a72551420c087642_column_mapper_py.html#t14"><data value='ColumnMapper'>ColumnMapper</data></a></td>
                <td>49</td>
                <td>49</td>
                <td>0</td>
                <td class="right" data-ratio="0 49">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642_column_mapper_py.html">src\database\utils\column_mapper.py</a></td>
                <td class="name left"><a href="z_a72551420c087642_column_mapper_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642_decorators_py.html">src\database\utils\decorators.py</a></td>
                <td class="name left"><a href="z_a72551420c087642_decorators_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>210</td>
                <td>192</td>
                <td>0</td>
                <td class="right" data-ratio="18 210">9%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642_excel_processor_py.html#t18">src\database\utils\excel_processor.py</a></td>
                <td class="name left"><a href="z_a72551420c087642_excel_processor_py.html#t18"><data value='MultiOperatorExcelProcessor'>MultiOperatorExcelProcessor</data></a></td>
                <td>83</td>
                <td>83</td>
                <td>0</td>
                <td class="right" data-ratio="0 83">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642_excel_processor_py.html">src\database\utils\excel_processor.py</a></td>
                <td class="name left"><a href="z_a72551420c087642_excel_processor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642_helpers_py.html">src\database\utils\helpers.py</a></td>
                <td class="name left"><a href="z_a72551420c087642_helpers_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>73</td>
                <td>57</td>
                <td>0</td>
                <td class="right" data-ratio="16 73">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642_parallel_config_py.html#t16">src\database\utils\parallel_config.py</a></td>
                <td class="name left"><a href="z_a72551420c087642_parallel_config_py.html#t16"><data value='ParallelProcessingConfig'>ParallelProcessingConfig</data></a></td>
                <td>63</td>
                <td>63</td>
                <td>0</td>
                <td class="right" data-ratio="0 63">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642_parallel_config_py.html">src\database\utils\parallel_config.py</a></td>
                <td class="name left"><a href="z_a72551420c087642_parallel_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642_performance_py.html#t213">src\database\utils\performance.py</a></td>
                <td class="name left"><a href="z_a72551420c087642_performance_py.html#t213"><data value='PerformanceTimer'>PerformanceTimer</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642_performance_py.html#t408">src\database\utils\performance.py</a></td>
                <td class="name left"><a href="z_a72551420c087642_performance_py.html#t408"><data value='PerformanceMonitor'>PerformanceMonitor</data></a></td>
                <td>45</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642_performance_py.html">src\database\utils\performance.py</a></td>
                <td class="name left"><a href="z_a72551420c087642_performance_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>143</td>
                <td>104</td>
                <td>0</td>
                <td class="right" data-ratio="39 143">27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642_progress_tracker_py.html#t12">src\database\utils\progress_tracker.py</a></td>
                <td class="name left"><a href="z_a72551420c087642_progress_tracker_py.html#t12"><data value='ProgressTracker'>ProgressTracker</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642_progress_tracker_py.html">src\database\utils\progress_tracker.py</a></td>
                <td class="name left"><a href="z_a72551420c087642_progress_tracker_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642_query_optimizer_py.html#t14">src\database\utils\query_optimizer.py</a></td>
                <td class="name left"><a href="z_a72551420c087642_query_optimizer_py.html#t14"><data value='QueryOptimizer'>QueryOptimizer</data></a></td>
                <td>58</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="0 58">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642_query_optimizer_py.html">src\database\utils\query_optimizer.py</a></td>
                <td class="name left"><a href="z_a72551420c087642_query_optimizer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642_security_py.html#t19">src\database\utils\security.py</a></td>
                <td class="name left"><a href="z_a72551420c087642_security_py.html#t19"><data value='SQLInjectionGuard'>SQLInjectionGuard</data></a></td>
                <td>49</td>
                <td>49</td>
                <td>0</td>
                <td class="right" data-ratio="0 49">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642_security_py.html">src\database\utils\security.py</a></td>
                <td class="name left"><a href="z_a72551420c087642_security_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642_table_naming_py.html#t17">src\database\utils\table_naming.py</a></td>
                <td class="name left"><a href="z_a72551420c087642_table_naming_py.html#t17"><data value='TableNamingManager'>TableNamingManager</data></a></td>
                <td>311</td>
                <td>311</td>
                <td>0</td>
                <td class="right" data-ratio="0 311">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642_table_naming_py.html#t941">src\database\utils\table_naming.py</a></td>
                <td class="name left"><a href="z_a72551420c087642_table_naming_py.html#t941"><data value='OperatorDetector'>OperatorDetector</data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642_table_naming_py.html">src\database\utils\table_naming.py</a></td>
                <td class="name left"><a href="z_a72551420c087642_table_naming_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>37</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="37 37">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642_validators_py.html#t10">src\database\utils\validators.py</a></td>
                <td class="name left"><a href="z_a72551420c087642_validators_py.html#t10"><data value='InputValidator'>InputValidator</data></a></td>
                <td>57</td>
                <td>57</td>
                <td>0</td>
                <td class="right" data-ratio="0 57">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642_validators_py.html">src\database\utils\validators.py</a></td>
                <td class="name left"><a href="z_a72551420c087642_validators_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_51b442c1a5736944___init___py.html">src\exporters\__init__.py</a></td>
                <td class="name left"><a href="z_51b442c1a5736944___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_51b442c1a5736944_base_py.html#t20">src\exporters\base.py</a></td>
                <td class="name left"><a href="z_51b442c1a5736944_base_py.html#t20"><data value='ExportError'>ExportError</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_51b442c1a5736944_base_py.html#t35">src\exporters\base.py</a></td>
                <td class="name left"><a href="z_51b442c1a5736944_base_py.html#t35"><data value='ExportResult'>ExportResult</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_51b442c1a5736944_base_py.html#t53">src\exporters\base.py</a></td>
                <td class="name left"><a href="z_51b442c1a5736944_base_py.html#t53"><data value='BaseExporter'>BaseExporter</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>13</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_51b442c1a5736944_base_py.html">src\exporters\base.py</a></td>
                <td class="name left"><a href="z_51b442c1a5736944_base_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>26</td>
                <td>26</td>
                <td>2</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_51b442c1a5736944_csv_exporter_py.html#t22">src\exporters\csv_exporter.py</a></td>
                <td class="name left"><a href="z_51b442c1a5736944_csv_exporter_py.html#t22"><data value='CSVExporter'>CSVExporter</data></a></td>
                <td>45</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_51b442c1a5736944_csv_exporter_py.html">src\exporters\csv_exporter.py</a></td>
                <td class="name left"><a href="z_51b442c1a5736944_csv_exporter_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_51b442c1a5736944_data_formatter_py.html#t20">src\exporters\data_formatter.py</a></td>
                <td class="name left"><a href="z_51b442c1a5736944_data_formatter_py.html#t20"><data value='DataFormatter'>DataFormatter</data></a></td>
                <td>53</td>
                <td>53</td>
                <td>0</td>
                <td class="right" data-ratio="0 53">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_51b442c1a5736944_data_formatter_py.html">src\exporters\data_formatter.py</a></td>
                <td class="name left"><a href="z_51b442c1a5736944_data_formatter_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_51b442c1a5736944_excel_exporter_py.html#t21">src\exporters\excel_exporter.py</a></td>
                <td class="name left"><a href="z_51b442c1a5736944_excel_exporter_py.html#t21"><data value='ExcelExporter'>ExcelExporter</data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_51b442c1a5736944_excel_exporter_py.html">src\exporters\excel_exporter.py</a></td>
                <td class="name left"><a href="z_51b442c1a5736944_excel_exporter_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_51b442c1a5736944_file_manager_py.html#t18">src\exporters\file_manager.py</a></td>
                <td class="name left"><a href="z_51b442c1a5736944_file_manager_py.html#t18"><data value='FileManager'>FileManager</data></a></td>
                <td>66</td>
                <td>66</td>
                <td>0</td>
                <td class="right" data-ratio="0 66">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_51b442c1a5736944_file_manager_py.html">src\exporters\file_manager.py</a></td>
                <td class="name left"><a href="z_51b442c1a5736944_file_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_51b442c1a5736944_geojson_exporter_py.html#t22">src\exporters\geojson_exporter.py</a></td>
                <td class="name left"><a href="z_51b442c1a5736944_geojson_exporter_py.html#t22"><data value='GeoJSONExporter'>GeoJSONExporter</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_51b442c1a5736944_geojson_exporter_py.html">src\exporters\geojson_exporter.py</a></td>
                <td class="name left"><a href="z_51b442c1a5736944_geojson_exporter_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_51b442c1a5736944_json_exporter_py.html#t22">src\exporters\json_exporter.py</a></td>
                <td class="name left"><a href="z_51b442c1a5736944_json_exporter_py.html#t22"><data value='JSONExporter'>JSONExporter</data></a></td>
                <td>39</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_51b442c1a5736944_json_exporter_py.html">src\exporters\json_exporter.py</a></td>
                <td class="name left"><a href="z_51b442c1a5736944_json_exporter_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_51b442c1a5736944_report_generator_py.html#t21">src\exporters\report_generator.py</a></td>
                <td class="name left"><a href="z_51b442c1a5736944_report_generator_py.html#t21"><data value='ReportGenerator'>ReportGenerator</data></a></td>
                <td>43</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_51b442c1a5736944_report_generator_py.html">src\exporters\report_generator.py</a></td>
                <td class="name left"><a href="z_51b442c1a5736944_report_generator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_13fedfab229146ee___init___py.html">src\geo\__init__.py</a></td>
                <td class="name left"><a href="z_13fedfab229146ee___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_13fedfab229146ee_geometry_py.html#t32">src\geo\geometry.py</a></td>
                <td class="name left"><a href="z_13fedfab229146ee_geometry_py.html#t32"><data value='GeometryProcessor'>GeometryProcessor</data></a></td>
                <td>101</td>
                <td>101</td>
                <td>0</td>
                <td class="right" data-ratio="0 101">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_13fedfab229146ee_geometry_py.html">src\geo\geometry.py</a></td>
                <td class="name left"><a href="z_13fedfab229146ee_geometry_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_13fedfab229146ee_qgis_integration_py.html#t19">src\geo\qgis_integration.py</a></td>
                <td class="name left"><a href="z_13fedfab229146ee_qgis_integration_py.html#t19"><data value='QGISIntegration'>QGISIntegration</data></a></td>
                <td>196</td>
                <td>196</td>
                <td>0</td>
                <td class="right" data-ratio="0 196">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_13fedfab229146ee_qgis_integration_py.html">src\geo\qgis_integration.py</a></td>
                <td class="name left"><a href="z_13fedfab229146ee_qgis_integration_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_13fedfab229146ee_raster_py.html#t25">src\geo\raster.py</a></td>
                <td class="name left"><a href="z_13fedfab229146ee_raster_py.html#t25"><data value='RasterProcessor'>RasterProcessor</data></a></td>
                <td>88</td>
                <td>88</td>
                <td>0</td>
                <td class="right" data-ratio="0 88">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_13fedfab229146ee_raster_py.html">src\geo\raster.py</a></td>
                <td class="name left"><a href="z_13fedfab229146ee_raster_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_13fedfab229146ee_vector_py.html#t22">src\geo\vector.py</a></td>
                <td class="name left"><a href="z_13fedfab229146ee_vector_py.html#t22"><data value='VectorProcessor'>VectorProcessor</data></a></td>
                <td>65</td>
                <td>65</td>
                <td>0</td>
                <td class="right" data-ratio="0 65">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_13fedfab229146ee_vector_py.html">src\geo\vector.py</a></td>
                <td class="name left"><a href="z_13fedfab229146ee_vector_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_22a7215c525dcefb___init___py.html">src\geospatial\__init__.py</a></td>
                <td class="name left"><a href="z_22a7215c525dcefb___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_22a7215c525dcefb_analyzer_py.html#t23">src\geospatial\analyzer.py</a></td>
                <td class="name left"><a href="z_22a7215c525dcefb_analyzer_py.html#t23"><data value='SpatialAnalyzer'>SpatialAnalyzer</data></a></td>
                <td>140</td>
                <td>140</td>
                <td>0</td>
                <td class="right" data-ratio="0 140">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_22a7215c525dcefb_analyzer_py.html">src\geospatial\analyzer.py</a></td>
                <td class="name left"><a href="z_22a7215c525dcefb_analyzer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_22a7215c525dcefb_indexer_py.html#t22">src\geospatial\indexer.py</a></td>
                <td class="name left"><a href="z_22a7215c525dcefb_indexer_py.html#t22"><data value='SpatialIndexer'>SpatialIndexer</data></a></td>
                <td>143</td>
                <td>143</td>
                <td>0</td>
                <td class="right" data-ratio="0 143">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_22a7215c525dcefb_indexer_py.html">src\geospatial\indexer.py</a></td>
                <td class="name left"><a href="z_22a7215c525dcefb_indexer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_22a7215c525dcefb_processor_py.html#t24">src\geospatial\processor.py</a></td>
                <td class="name left"><a href="z_22a7215c525dcefb_processor_py.html#t24"><data value='GeospatialProcessor'>GeospatialProcessor</data></a></td>
                <td>164</td>
                <td>164</td>
                <td>0</td>
                <td class="right" data-ratio="0 164">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_22a7215c525dcefb_processor_py.html">src\geospatial\processor.py</a></td>
                <td class="name left"><a href="z_22a7215c525dcefb_processor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66___init___py.html">src\importers\__init__.py</a></td>
                <td class="name left"><a href="z_668c0f7178569a66___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>46</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_base_py.html#t20">src\importers\base.py</a></td>
                <td class="name left"><a href="z_668c0f7178569a66_base_py.html#t20"><data value='ImportError'>ImportError</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_base_py.html#t35">src\importers\base.py</a></td>
                <td class="name left"><a href="z_668c0f7178569a66_base_py.html#t35"><data value='ImportResult'>ImportResult</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_base_py.html">src\importers\base.py</a></td>
                <td class="name left"><a href="z_668c0f7178569a66_base_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2824de5b4311dfa3___init___py.html">src\importers\base\__init__.py</a></td>
                <td class="name left"><a href="z_2824de5b4311dfa3___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2824de5b4311dfa3_abstract_importer_py.html#t22">src\importers\base\abstract_importer.py</a></td>
                <td class="name left"><a href="z_2824de5b4311dfa3_abstract_importer_py.html#t22"><data value='ImportStatus'>ImportStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2824de5b4311dfa3_abstract_importer_py.html#t32">src\importers\base\abstract_importer.py</a></td>
                <td class="name left"><a href="z_2824de5b4311dfa3_abstract_importer_py.html#t32"><data value='DataEngine'>DataEngine</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2824de5b4311dfa3_abstract_importer_py.html#t39">src\importers\base\abstract_importer.py</a></td>
                <td class="name left"><a href="z_2824de5b4311dfa3_abstract_importer_py.html#t39"><data value='ImportMetrics'>ImportMetrics</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2824de5b4311dfa3_abstract_importer_py.html#t61">src\importers\base\abstract_importer.py</a></td>
                <td class="name left"><a href="z_2824de5b4311dfa3_abstract_importer_py.html#t61"><data value='ImportResult'>ImportResult</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2824de5b4311dfa3_abstract_importer_py.html#t83">src\importers\base\abstract_importer.py</a></td>
                <td class="name left"><a href="z_2824de5b4311dfa3_abstract_importer_py.html#t83"><data value='ImporterConfig'>ImporterConfig</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2824de5b4311dfa3_abstract_importer_py.html#t112">src\importers\base\abstract_importer.py</a></td>
                <td class="name left"><a href="z_2824de5b4311dfa3_abstract_importer_py.html#t112"><data value='TelecomImportError'>TelecomImportError</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2824de5b4311dfa3_abstract_importer_py.html#t121">src\importers\base\abstract_importer.py</a></td>
                <td class="name left"><a href="z_2824de5b4311dfa3_abstract_importer_py.html#t121"><data value='ValidationError'>ValidationError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2824de5b4311dfa3_abstract_importer_py.html#t126">src\importers\base\abstract_importer.py</a></td>
                <td class="name left"><a href="z_2824de5b4311dfa3_abstract_importer_py.html#t126"><data value='ProcessingError'>ProcessingError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2824de5b4311dfa3_abstract_importer_py.html#t131">src\importers\base\abstract_importer.py</a></td>
                <td class="name left"><a href="z_2824de5b4311dfa3_abstract_importer_py.html#t131"><data value='ConfigurationError'>ConfigurationError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2824de5b4311dfa3_abstract_importer_py.html#t136">src\importers\base\abstract_importer.py</a></td>
                <td class="name left"><a href="z_2824de5b4311dfa3_abstract_importer_py.html#t136"><data value='AbstractImporter'>AbstractImporter</data></a></td>
                <td>133</td>
                <td>133</td>
                <td>27</td>
                <td class="right" data-ratio="0 133">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2824de5b4311dfa3_abstract_importer_py.html">src\importers\base\abstract_importer.py</a></td>
                <td class="name left"><a href="z_2824de5b4311dfa3_abstract_importer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>88</td>
                <td>88</td>
                <td>6</td>
                <td class="right" data-ratio="0 88">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2824de5b4311dfa3_performance_mixin_py.html#t27">src\importers\base\performance_mixin.py</a></td>
                <td class="name left"><a href="z_2824de5b4311dfa3_performance_mixin_py.html#t27"><data value='PerformanceSnapshot'>PerformanceSnapshot</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2824de5b4311dfa3_performance_mixin_py.html#t55">src\importers\base\performance_mixin.py</a></td>
                <td class="name left"><a href="z_2824de5b4311dfa3_performance_mixin_py.html#t55"><data value='OperationMetrics'>OperationMetrics</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2824de5b4311dfa3_performance_mixin_py.html#t106">src\importers\base\performance_mixin.py</a></td>
                <td class="name left"><a href="z_2824de5b4311dfa3_performance_mixin_py.html#t106"><data value='PerformanceConfig'>PerformanceConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2824de5b4311dfa3_performance_mixin_py.html#t139">src\importers\base\performance_mixin.py</a></td>
                <td class="name left"><a href="z_2824de5b4311dfa3_performance_mixin_py.html#t139"><data value='PerformanceMixin'>PerformanceMixin</data></a></td>
                <td>158</td>
                <td>158</td>
                <td>0</td>
                <td class="right" data-ratio="0 158">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2824de5b4311dfa3_performance_mixin_py.html">src\importers\base\performance_mixin.py</a></td>
                <td class="name left"><a href="z_2824de5b4311dfa3_performance_mixin_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>82</td>
                <td>82</td>
                <td>0</td>
                <td class="right" data-ratio="0 82">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2824de5b4311dfa3_processing_mixin_py.html#t25">src\importers\base\processing_mixin.py</a></td>
                <td class="name left"><a href="z_2824de5b4311dfa3_processing_mixin_py.html#t25"><data value='ProcessingMetrics'>ProcessingMetrics</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2824de5b4311dfa3_processing_mixin_py.html#t42">src\importers\base\processing_mixin.py</a></td>
                <td class="name left"><a href="z_2824de5b4311dfa3_processing_mixin_py.html#t42"><data value='ProcessingConfig'>ProcessingConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2824de5b4311dfa3_processing_mixin_py.html#t67">src\importers\base\processing_mixin.py</a></td>
                <td class="name left"><a href="z_2824de5b4311dfa3_processing_mixin_py.html#t67"><data value='ProcessingMixin'>ProcessingMixin</data></a></td>
                <td>235</td>
                <td>235</td>
                <td>0</td>
                <td class="right" data-ratio="0 235">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2824de5b4311dfa3_processing_mixin_py.html">src\importers\base\processing_mixin.py</a></td>
                <td class="name left"><a href="z_2824de5b4311dfa3_processing_mixin_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>63</td>
                <td>63</td>
                <td>0</td>
                <td class="right" data-ratio="0 63">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2824de5b4311dfa3_validation_mixin_py.html#t21">src\importers\base\validation_mixin.py</a></td>
                <td class="name left"><a href="z_2824de5b4311dfa3_validation_mixin_py.html#t21"><data value='ValidationRule'>ValidationRule</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2824de5b4311dfa3_validation_mixin_py.html#t32">src\importers\base\validation_mixin.py</a></td>
                <td class="name left"><a href="z_2824de5b4311dfa3_validation_mixin_py.html#t32"><data value='ValidationResult'>ValidationResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2824de5b4311dfa3_validation_mixin_py.html#t42">src\importers\base\validation_mixin.py</a></td>
                <td class="name left"><a href="z_2824de5b4311dfa3_validation_mixin_py.html#t42"><data value='TelecomValidationConfig'>TelecomValidationConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2824de5b4311dfa3_validation_mixin_py.html#t84">src\importers\base\validation_mixin.py</a></td>
                <td class="name left"><a href="z_2824de5b4311dfa3_validation_mixin_py.html#t84"><data value='ValidationMixin'>ValidationMixin</data></a></td>
                <td>137</td>
                <td>137</td>
                <td>0</td>
                <td class="right" data-ratio="0 137">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2824de5b4311dfa3_validation_mixin_py.html">src\importers\base\validation_mixin.py</a></td>
                <td class="name left"><a href="z_2824de5b4311dfa3_validation_mixin_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>54</td>
                <td>54</td>
                <td>0</td>
                <td class="right" data-ratio="0 54">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_batch_processor_py.html#t20">src\importers\batch_processor.py</a></td>
                <td class="name left"><a href="z_668c0f7178569a66_batch_processor_py.html#t20"><data value='JobMetrics'>JobMetrics</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_batch_processor_py.html#t30">src\importers\batch_processor.py</a></td>
                <td class="name left"><a href="z_668c0f7178569a66_batch_processor_py.html#t30"><data value='ImportJob'>ImportJob</data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_batch_processor_py.html#t126">src\importers\batch_processor.py</a></td>
                <td class="name left"><a href="z_668c0f7178569a66_batch_processor_py.html#t126"><data value='BatchProcessor'>BatchProcessor</data></a></td>
                <td>75</td>
                <td>75</td>
                <td>0</td>
                <td class="right" data-ratio="0 75">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_batch_processor_py.html">src\importers\batch_processor.py</a></td>
                <td class="name left"><a href="z_668c0f7178569a66_batch_processor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_cdr_importer_py.html#t34">src\importers\cdr_importer.py</a></td>
                <td class="name left"><a href="z_668c0f7178569a66_cdr_importer_py.html#t34"><data value='CDRImporter'>CDRImporter</data></a></td>
                <td>603</td>
                <td>603</td>
                <td>0</td>
                <td class="right" data-ratio="0 603">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_cdr_importer_py.html">src\importers\cdr_importer.py</a></td>
                <td class="name left"><a href="z_668c0f7178569a66_cdr_importer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>57</td>
                <td>57</td>
                <td>0</td>
                <td class="right" data-ratio="0 57">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_cfg_importer_py.html#t25">src\importers\cfg_importer.py</a></td>
                <td class="name left"><a href="z_668c0f7178569a66_cfg_importer_py.html#t25"><data value='CFGImporter'>CFGImporter</data></a></td>
                <td>150</td>
                <td>150</td>
                <td>0</td>
                <td class="right" data-ratio="0 150">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_cfg_importer_py.html">src\importers\cfg_importer.py</a></td>
                <td class="name left"><a href="z_668c0f7178569a66_cfg_importer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_csv_importer_py.html#t22">src\importers\csv_importer.py</a></td>
                <td class="name left"><a href="z_668c0f7178569a66_csv_importer_py.html#t22"><data value='CSVImporter'>CSVImporter</data></a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_csv_importer_py.html">src\importers\csv_importer.py</a></td>
                <td class="name left"><a href="z_668c0f7178569a66_csv_importer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_data_transformer_py.html#t9">src\importers\data_transformer.py</a></td>
                <td class="name left"><a href="z_668c0f7178569a66_data_transformer_py.html#t9"><data value='SchemaMapper'>SchemaMapper</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_data_transformer_py.html#t37">src\importers\data_transformer.py</a></td>
                <td class="name left"><a href="z_668c0f7178569a66_data_transformer_py.html#t37"><data value='DataTransformer'>DataTransformer</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_data_transformer_py.html">src\importers\data_transformer.py</a></td>
                <td class="name left"><a href="z_668c0f7178569a66_data_transformer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_ep_importer_py.html#t19">src\importers\ep_importer.py</a></td>
                <td class="name left"><a href="z_668c0f7178569a66_ep_importer_py.html#t19"><data value='EPImporter'>EPImporter</data></a></td>
                <td>416</td>
                <td>416</td>
                <td>0</td>
                <td class="right" data-ratio="0 416">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_ep_importer_py.html#t437">src\importers\ep_importer.py</a></td>
                <td class="name left"><a href="z_668c0f7178569a66_ep_importer_py.html#t437"><data value='BatchResult'>EPImporter.process_batch.BatchResult</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_ep_importer_py.html">src\importers\ep_importer.py</a></td>
                <td class="name left"><a href="z_668c0f7178569a66_ep_importer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_excel_importer_py.html#t21">src\importers\excel_importer.py</a></td>
                <td class="name left"><a href="z_668c0f7178569a66_excel_importer_py.html#t21"><data value='ExcelImporter'>ExcelImporter</data></a></td>
                <td>47</td>
                <td>47</td>
                <td>0</td>
                <td class="right" data-ratio="0 47">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_excel_importer_py.html">src\importers\excel_importer.py</a></td>
                <td class="name left"><a href="z_668c0f7178569a66_excel_importer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4522cb7338698c28___init___py.html">src\importers\generic\__init__.py</a></td>
                <td class="name left"><a href="z_4522cb7338698c28___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4522cb7338698c28_csv_importer_py.html#t31">src\importers\generic\csv_importer.py</a></td>
                <td class="name left"><a href="z_4522cb7338698c28_csv_importer_py.html#t31"><data value='CSVConfig'>CSVConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4522cb7338698c28_csv_importer_py.html#t79">src\importers\generic\csv_importer.py</a></td>
                <td class="name left"><a href="z_4522cb7338698c28_csv_importer_py.html#t79"><data value='CSVImporter'>CSVImporter</data></a></td>
                <td>311</td>
                <td>311</td>
                <td>0</td>
                <td class="right" data-ratio="0 311">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4522cb7338698c28_csv_importer_py.html">src\importers\generic\csv_importer.py</a></td>
                <td class="name left"><a href="z_4522cb7338698c28_csv_importer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>56</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="0 56">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4522cb7338698c28_excel_importer_py.html#t31">src\importers\generic\excel_importer.py</a></td>
                <td class="name left"><a href="z_4522cb7338698c28_excel_importer_py.html#t31"><data value='ExcelConfig'>ExcelConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4522cb7338698c28_excel_importer_py.html#t86">src\importers\generic\excel_importer.py</a></td>
                <td class="name left"><a href="z_4522cb7338698c28_excel_importer_py.html#t86"><data value='ExcelImporter'>ExcelImporter</data></a></td>
                <td>322</td>
                <td>322</td>
                <td>0</td>
                <td class="right" data-ratio="0 322">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4522cb7338698c28_excel_importer_py.html">src\importers\generic\excel_importer.py</a></td>
                <td class="name left"><a href="z_4522cb7338698c28_excel_importer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>59</td>
                <td>59</td>
                <td>0</td>
                <td class="right" data-ratio="0 59">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4522cb7338698c28_json_importer_py.html#t33">src\importers\generic\json_importer.py</a></td>
                <td class="name left"><a href="z_4522cb7338698c28_json_importer_py.html#t33"><data value='JSONConfig'>JSONConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4522cb7338698c28_json_importer_py.html#t86">src\importers\generic\json_importer.py</a></td>
                <td class="name left"><a href="z_4522cb7338698c28_json_importer_py.html#t86"><data value='JSONImporter'>JSONImporter</data></a></td>
                <td>413</td>
                <td>413</td>
                <td>0</td>
                <td class="right" data-ratio="0 413">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4522cb7338698c28_json_importer_py.html">src\importers\generic\json_importer.py</a></td>
                <td class="name left"><a href="z_4522cb7338698c28_json_importer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>66</td>
                <td>66</td>
                <td>0</td>
                <td class="right" data-ratio="0 66">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_import_manager_py.html#t50">src\importers\import_manager.py</a></td>
                <td class="name left"><a href="z_668c0f7178569a66_import_manager_py.html#t50"><data value='ImportJobConfig'>ImportJobConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_import_manager_py.html#t79">src\importers\import_manager.py</a></td>
                <td class="name left"><a href="z_668c0f7178569a66_import_manager_py.html#t79"><data value='ImportJobResult'>ImportJobResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_import_manager_py.html#t109">src\importers\import_manager.py</a></td>
                <td class="name left"><a href="z_668c0f7178569a66_import_manager_py.html#t109"><data value='ImportManager'>ImportManager</data></a></td>
                <td>263</td>
                <td>263</td>
                <td>0</td>
                <td class="right" data-ratio="0 263">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_import_manager_py.html#t575">src\importers\import_manager.py</a></td>
                <td class="name left"><a href="z_668c0f7178569a66_import_manager_py.html#t575"><data value='DatabaseOperationsWrapper'>ImportManager._create_db_operations_wrapper.DatabaseOperationsWrapper</data></a></td>
                <td>39</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_import_manager_py.html#t751">src\importers\import_manager.py</a></td>
                <td class="name left"><a href="z_668c0f7178569a66_import_manager_py.html#t751"><data value='MockImporter'>ImportManager._create_mock_importer.MockImporter</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_import_manager_py.html">src\importers\import_manager.py</a></td>
                <td class="name left"><a href="z_668c0f7178569a66_import_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>70</td>
                <td>70</td>
                <td>0</td>
                <td class="right" data-ratio="0 70">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_json_importer_py.html#t22">src\importers\json_importer.py</a></td>
                <td class="name left"><a href="z_668c0f7178569a66_json_importer_py.html#t22"><data value='JSONImporter'>JSONImporter</data></a></td>
                <td>51</td>
                <td>51</td>
                <td>0</td>
                <td class="right" data-ratio="0 51">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_json_importer_py.html">src\importers\json_importer.py</a></td>
                <td class="name left"><a href="z_668c0f7178569a66_json_importer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_kpi_importer_py.html#t25">src\importers\kpi_importer.py</a></td>
                <td class="name left"><a href="z_668c0f7178569a66_kpi_importer_py.html#t25"><data value='KPIImporter'>KPIImporter</data></a></td>
                <td>206</td>
                <td>206</td>
                <td>0</td>
                <td class="right" data-ratio="0 206">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_kpi_importer_py.html">src\importers\kpi_importer.py</a></td>
                <td class="name left"><a href="z_668c0f7178569a66_kpi_importer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_nlg_importer_py.html#t27">src\importers\nlg_importer.py</a></td>
                <td class="name left"><a href="z_668c0f7178569a66_nlg_importer_py.html#t27"><data value='NLGImporter'>NLGImporter</data></a></td>
                <td>382</td>
                <td>382</td>
                <td>0</td>
                <td class="right" data-ratio="0 382">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_nlg_importer_py.html">src\importers\nlg_importer.py</a></td>
                <td class="name left"><a href="z_668c0f7178569a66_nlg_importer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_score_importer_py.html#t25">src\importers\score_importer.py</a></td>
                <td class="name left"><a href="z_668c0f7178569a66_score_importer_py.html#t25"><data value='ScoreImporter'>ScoreImporter</data></a></td>
                <td>204</td>
                <td>204</td>
                <td>0</td>
                <td class="right" data-ratio="0 204">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_score_importer_py.html">src\importers\score_importer.py</a></td>
                <td class="name left"><a href="z_668c0f7178569a66_score_importer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_37c60dde8ac853d0___init___py.html">src\importers\telecom\__init__.py</a></td>
                <td class="name left"><a href="z_37c60dde8ac853d0___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>117</td>
                <td>117</td>
                <td>0</td>
                <td class="right" data-ratio="0 117">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_37c60dde8ac853d0_cdr_importer_py.html#t30">src\importers\telecom\cdr_importer.py</a></td>
                <td class="name left"><a href="z_37c60dde8ac853d0_cdr_importer_py.html#t30"><data value='CDRConfig'>CDRConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_37c60dde8ac853d0_cdr_importer_py.html#t90">src\importers\telecom\cdr_importer.py</a></td>
                <td class="name left"><a href="z_37c60dde8ac853d0_cdr_importer_py.html#t90"><data value='CDRImporter'>CDRImporter</data></a></td>
                <td>273</td>
                <td>273</td>
                <td>0</td>
                <td class="right" data-ratio="0 273">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_37c60dde8ac853d0_cdr_importer_py.html">src\importers\telecom\cdr_importer.py</a></td>
                <td class="name left"><a href="z_37c60dde8ac853d0_cdr_importer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>63</td>
                <td>63</td>
                <td>0</td>
                <td class="right" data-ratio="0 63">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_37c60dde8ac853d0_ep_importer_py.html#t31">src\importers\telecom\ep_importer.py</a></td>
                <td class="name left"><a href="z_37c60dde8ac853d0_ep_importer_py.html#t31"><data value='EPConfig'>EPConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_37c60dde8ac853d0_ep_importer_py.html#t120">src\importers\telecom\ep_importer.py</a></td>
                <td class="name left"><a href="z_37c60dde8ac853d0_ep_importer_py.html#t120"><data value='EPImporter'>EPImporter</data></a></td>
                <td>608</td>
                <td>608</td>
                <td>0</td>
                <td class="right" data-ratio="0 608">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_37c60dde8ac853d0_ep_importer_py.html">src\importers\telecom\ep_importer.py</a></td>
                <td class="name left"><a href="z_37c60dde8ac853d0_ep_importer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>68</td>
                <td>68</td>
                <td>77</td>
                <td class="right" data-ratio="0 68">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_37c60dde8ac853d0_kpi_importer_py.html#t29">src\importers\telecom\kpi_importer.py</a></td>
                <td class="name left"><a href="z_37c60dde8ac853d0_kpi_importer_py.html#t29"><data value='KPIConfig'>KPIConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_37c60dde8ac853d0_kpi_importer_py.html#t67">src\importers\telecom\kpi_importer.py</a></td>
                <td class="name left"><a href="z_37c60dde8ac853d0_kpi_importer_py.html#t67"><data value='KPIImporter'>KPIImporter</data></a></td>
                <td>271</td>
                <td>271</td>
                <td>0</td>
                <td class="right" data-ratio="0 271">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_37c60dde8ac853d0_kpi_importer_py.html">src\importers\telecom\kpi_importer.py</a></td>
                <td class="name left"><a href="z_37c60dde8ac853d0_kpi_importer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>55</td>
                <td>55</td>
                <td>0</td>
                <td class="right" data-ratio="0 55">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_37c60dde8ac853d0_nlg_importer_py.html#t31">src\importers\telecom\nlg_importer.py</a></td>
                <td class="name left"><a href="z_37c60dde8ac853d0_nlg_importer_py.html#t31"><data value='NLGConfig'>NLGConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_37c60dde8ac853d0_nlg_importer_py.html#t75">src\importers\telecom\nlg_importer.py</a></td>
                <td class="name left"><a href="z_37c60dde8ac853d0_nlg_importer_py.html#t75"><data value='NLGImporter'>NLGImporter</data></a></td>
                <td>492</td>
                <td>492</td>
                <td>0</td>
                <td class="right" data-ratio="0 492">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_37c60dde8ac853d0_nlg_importer_py.html">src\importers\telecom\nlg_importer.py</a></td>
                <td class="name left"><a href="z_37c60dde8ac853d0_nlg_importer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>70</td>
                <td>70</td>
                <td>9</td>
                <td class="right" data-ratio="0 70">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_628569355ef1e7af_csv_health_monitor_py.html#t28">src\monitoring\csv_health_monitor.py</a></td>
                <td class="name left"><a href="z_628569355ef1e7af_csv_health_monitor_py.html#t28"><data value='ProcessingMetrics'>ProcessingMetrics</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_628569355ef1e7af_csv_health_monitor_py.html#t42">src\monitoring\csv_health_monitor.py</a></td>
                <td class="name left"><a href="z_628569355ef1e7af_csv_health_monitor_py.html#t42"><data value='HealthStatus'>HealthStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_628569355ef1e7af_csv_health_monitor_py.html#t53">src\monitoring\csv_health_monitor.py</a></td>
                <td class="name left"><a href="z_628569355ef1e7af_csv_health_monitor_py.html#t53"><data value='CSVHealthMonitor'>CSVHealthMonitor</data></a></td>
                <td>106</td>
                <td>106</td>
                <td>0</td>
                <td class="right" data-ratio="0 106">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_628569355ef1e7af_csv_health_monitor_py.html">src\monitoring\csv_health_monitor.py</a></td>
                <td class="name left"><a href="z_628569355ef1e7af_csv_health_monitor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>54</td>
                <td>7</td>
                <td>22</td>
                <td class="right" data-ratio="47 54">87%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be___init___py.html">src\utils\__init__.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_cache_manager_py.html#t17">src\utils\cache_manager.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_cache_manager_py.html#t17"><data value='CacheEntry'>CacheEntry</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_cache_manager_py.html#t37">src\utils\cache_manager.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_cache_manager_py.html#t37"><data value='CacheStats'>CacheStats</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_cache_manager_py.html#t56">src\utils\cache_manager.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_cache_manager_py.html#t56"><data value='CacheManager'>CacheManager</data></a></td>
                <td>58</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="0 58">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_cache_manager_py.html">src\utils\cache_manager.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_cache_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>39</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_column_deduplicator_py.html#t15">src\utils\column_deduplicator.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_column_deduplicator_py.html#t15"><data value='ColumnDeduplicator'>ColumnDeduplicator</data></a></td>
                <td>135</td>
                <td>135</td>
                <td>0</td>
                <td class="right" data-ratio="0 135">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_column_deduplicator_py.html">src\utils\column_deduplicator.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_column_deduplicator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_data_validator_py.html#t17">src\utils\data_validator.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_data_validator_py.html#t17"><data value='ValidationSeverity'>ValidationSeverity</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_data_validator_py.html#t26">src\utils\data_validator.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_data_validator_py.html#t26"><data value='ValidationResult'>ValidationResult</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_data_validator_py.html#t85">src\utils\data_validator.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_data_validator_py.html#t85"><data value='ValidationRule'>ValidationRule</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_data_validator_py.html#t116">src\utils\data_validator.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_data_validator_py.html#t116"><data value='DataValidator'>DataValidator</data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_data_validator_py.html">src\utils\data_validator.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_data_validator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>47</td>
                <td>47</td>
                <td>0</td>
                <td class="right" data-ratio="0 47">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_disk_space_manager_py.html#t16">src\utils\disk_space_manager.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_disk_space_manager_py.html#t16"><data value='DiskSpaceInfo'>DiskSpaceInfo</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_disk_space_manager_py.html#t34">src\utils\disk_space_manager.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_disk_space_manager_py.html#t34"><data value='CleanupResult'>CleanupResult</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_disk_space_manager_py.html#t54">src\utils\disk_space_manager.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_disk_space_manager_py.html#t54"><data value='DiskSpaceManager'>DiskSpaceManager</data></a></td>
                <td>175</td>
                <td>175</td>
                <td>0</td>
                <td class="right" data-ratio="0 175">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_disk_space_manager_py.html">src\utils\disk_space_manager.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_disk_space_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>54</td>
                <td>54</td>
                <td>0</td>
                <td class="right" data-ratio="0 54">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_file_handler_py.html#t21">src\utils\file_handler.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_file_handler_py.html#t21"><data value='FileError'>FileError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_file_handler_py.html#t27">src\utils\file_handler.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_file_handler_py.html#t27"><data value='FileInfo'>FileInfo</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_file_handler_py.html#t86">src\utils\file_handler.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_file_handler_py.html#t86"><data value='FileHandler'>FileHandler</data></a></td>
                <td>100</td>
                <td>100</td>
                <td>0</td>
                <td class="right" data-ratio="0 100">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_file_handler_py.html">src\utils\file_handler.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be_file_handler_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>54</td>
                <td>54</td>
                <td>0</td>
                <td class="right" data-ratio="0 54">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de___init___py.html">src\validation\__init__.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>51</td>
                <td>51</td>
                <td>0</td>
                <td class="right" data-ratio="0 51">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_benchmark_py.html#t41">src\validation\benchmark.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_benchmark_py.html#t41"><data value='BenchmarkConfig'>BenchmarkConfig</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_benchmark_py.html#t73">src\validation\benchmark.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_benchmark_py.html#t73"><data value='BenchmarkResult'>BenchmarkResult</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_benchmark_py.html#t105">src\validation\benchmark.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_benchmark_py.html#t105"><data value='SystemMonitor'>SystemMonitor</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_benchmark_py.html#t158">src\validation\benchmark.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_benchmark_py.html#t158"><data value='BenchmarkRunner'>BenchmarkRunner</data></a></td>
                <td>203</td>
                <td>203</td>
                <td>0</td>
                <td class="right" data-ratio="0 203">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_benchmark_py.html">src\validation\benchmark.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_benchmark_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>109</td>
                <td>109</td>
                <td>2</td>
                <td class="right" data-ratio="0 109">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_config_py.html#t14">src\validation\config.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_config_py.html#t14"><data value='ValidationMode'>ValidationMode</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_config_py.html#t21">src\validation\config.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_config_py.html#t21"><data value='LogLevel'>LogLevel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_config_py.html#t31">src\validation\config.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_config_py.html#t31"><data value='PerformanceConfig'>PerformanceConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_config_py.html#t43">src\validation\config.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_config_py.html#t43"><data value='CacheConfig'>CacheConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_config_py.html#t53">src\validation\config.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_config_py.html#t53"><data value='ValidationConfig'>ValidationConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_config_py.html#t65">src\validation\config.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_config_py.html#t65"><data value='LoggingConfig'>LoggingConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_config_py.html#t77">src\validation\config.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_config_py.html#t77"><data value='MonitoringConfig'>MonitoringConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_config_py.html#t93">src\validation\config.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_config_py.html#t93"><data value='SecurityConfig'>SecurityConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_config_py.html#t106">src\validation\config.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_config_py.html#t106"><data value='TelecomConfig'>TelecomConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_config_py.html#t144">src\validation\config.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_config_py.html#t144"><data value='ValidationFrameworkConfig'>ValidationFrameworkConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_config_py.html#t160">src\validation\config.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_config_py.html#t160"><data value='ConfigManager'>ConfigManager</data></a></td>
                <td>94</td>
                <td>94</td>
                <td>0</td>
                <td class="right" data-ratio="0 94">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_config_py.html">src\validation\config.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>139</td>
                <td>139</td>
                <td>0</td>
                <td class="right" data-ratio="0 139">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_core_py.html#t22">src\validation\core.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_core_py.html#t22"><data value='ValidationType'>ValidationType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_core_py.html#t38">src\validation\core.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_core_py.html#t38"><data value='ValidationSeverity'>ValidationSeverity</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_core_py.html#t47">src\validation\core.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_core_py.html#t47"><data value='ValidationContext'>ValidationContext</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_core_py.html#t71">src\validation\core.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_core_py.html#t71"><data value='ValidationIssue'>ValidationIssue</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_core_py.html#t101">src\validation\core.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_core_py.html#t101"><data value='ValidationResult'>ValidationResult</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_core_py.html#t173">src\validation\core.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_core_py.html#t173"><data value='ValidationRule'>ValidationRule</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>10</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_core_py.html#t218">src\validation\core.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_core_py.html#t218"><data value='ValidationFramework'>ValidationFramework</data></a></td>
                <td>67</td>
                <td>67</td>
                <td>0</td>
                <td class="right" data-ratio="0 67">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_core_py.html">src\validation\core.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_core_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>84</td>
                <td>84</td>
                <td>2</td>
                <td class="right" data-ratio="0 84">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_deploy_py.html#t20">src\validation\deploy.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_deploy_py.html#t20"><data value='DeploymentConfig'>DeploymentConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_deploy_py.html#t54">src\validation\deploy.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_deploy_py.html#t54"><data value='DeploymentValidator'>DeploymentValidator</data></a></td>
                <td>67</td>
                <td>67</td>
                <td>0</td>
                <td class="right" data-ratio="0 67">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_deploy_py.html#t188">src\validation\deploy.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_deploy_py.html#t188"><data value='DeploymentManager'>DeploymentManager</data></a></td>
                <td>169</td>
                <td>169</td>
                <td>0</td>
                <td class="right" data-ratio="0 169">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_deploy_py.html">src\validation\deploy.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_deploy_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>100</td>
                <td>100</td>
                <td>3</td>
                <td class="right" data-ratio="0 100">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_docs_generator_py.html#t24">src\validation\docs_generator.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_docs_generator_py.html#t24"><data value='DocConfig'>DocConfig</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_docs_generator_py.html#t50">src\validation\docs_generator.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_docs_generator_py.html#t50"><data value='ClassInfo'>ClassInfo</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_docs_generator_py.html#t63">src\validation\docs_generator.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_docs_generator_py.html#t63"><data value='FunctionInfo'>FunctionInfo</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_docs_generator_py.html#t76">src\validation\docs_generator.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_docs_generator_py.html#t76"><data value='ModuleInfo'>ModuleInfo</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_docs_generator_py.html#t86">src\validation\docs_generator.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_docs_generator_py.html#t86"><data value='CodeAnalyzer'>CodeAnalyzer</data></a></td>
                <td>155</td>
                <td>155</td>
                <td>0</td>
                <td class="right" data-ratio="0 155">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_docs_generator_py.html#t409">src\validation\docs_generator.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_docs_generator_py.html#t409"><data value='MarkdownGenerator'>MarkdownGenerator</data></a></td>
                <td>128</td>
                <td>128</td>
                <td>0</td>
                <td class="right" data-ratio="0 128">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_docs_generator_py.html#t1253">src\validation\docs_generator.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_docs_generator_py.html#t1253"><data value='DocumentationGenerator'>DocumentationGenerator</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_docs_generator_py.html">src\validation\docs_generator.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_docs_generator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>100</td>
                <td>100</td>
                <td>2</td>
                <td class="right" data-ratio="0 100">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_examples_py.html">src\validation\examples.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_examples_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>121</td>
                <td>121</td>
                <td>2</td>
                <td class="right" data-ratio="0 121">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_exceptions_py.html#t12">src\validation\exceptions.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_exceptions_py.html#t12"><data value='ValidationError'>ValidationError</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_exceptions_py.html#t50">src\validation\exceptions.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_exceptions_py.html#t50"><data value='ValidationConfigError'>ValidationConfigError</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_exceptions_py.html#t69">src\validation\exceptions.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_exceptions_py.html#t69"><data value='ValidationRuleError'>ValidationRuleError</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_exceptions_py.html#t84">src\validation\exceptions.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_exceptions_py.html#t84"><data value='DataValidationError'>DataValidationError</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_exceptions_py.html#t112">src\validation\exceptions.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_exceptions_py.html#t112"><data value='SchemaValidationError'>SchemaValidationError</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_exceptions_py.html#t136">src\validation\exceptions.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_exceptions_py.html#t136"><data value='FileValidationError'>FileValidationError</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_exceptions_py.html#t160">src\validation\exceptions.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_exceptions_py.html#t160"><data value='TelecomValidationError'>TelecomValidationError</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_exceptions_py.html#t180">src\validation\exceptions.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_exceptions_py.html#t180"><data value='ValidationTimeoutError'>ValidationTimeoutError</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_exceptions_py.html#t200">src\validation\exceptions.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_exceptions_py.html#t200"><data value='ValidationDependencyError'>ValidationDependencyError</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_exceptions_py.html">src\validation\exceptions.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_exceptions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_factory_py.html#t19">src\validation\factory.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_factory_py.html#t19"><data value='ValidationFactory'>ValidationFactory</data></a></td>
                <td>57</td>
                <td>57</td>
                <td>0</td>
                <td class="right" data-ratio="0 57">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_factory_py.html">src\validation\factory.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_factory_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>48</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="0 48">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_install_py.html#t26">src\validation\install.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_install_py.html#t26"><data value='InstallConfig'>InstallConfig</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_install_py.html#t60">src\validation\install.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_install_py.html#t60"><data value='SystemChecker'>SystemChecker</data></a></td>
                <td>58</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="0 58">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_install_py.html#t167">src\validation\install.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_install_py.html#t167"><data value='DependencyInstaller'>DependencyInstaller</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_install_py.html#t241">src\validation\install.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_install_py.html#t241"><data value='ConfigGenerator'>ConfigGenerator</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_install_py.html#t395">src\validation\install.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_install_py.html#t395"><data value='TestRunner'>TestRunner</data></a></td>
                <td>40</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="0 40">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_install_py.html#t477">src\validation\install.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_install_py.html#t477"><data value='ValidationFrameworkInstaller'>ValidationFrameworkInstaller</data></a></td>
                <td>184</td>
                <td>184</td>
                <td>1</td>
                <td class="right" data-ratio="0 184">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_install_py.html">src\validation\install.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_install_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>78</td>
                <td>78</td>
                <td>2</td>
                <td class="right" data-ratio="0 78">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_monitoring_py.html#t20">src\validation\monitoring.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_monitoring_py.html#t20"><data value='ValidationMetrics'>ValidationMetrics</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_monitoring_py.html#t56">src\validation\monitoring.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_monitoring_py.html#t56"><data value='AlertThreshold'>AlertThreshold</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_monitoring_py.html#t67">src\validation\monitoring.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_monitoring_py.html#t67"><data value='Alert'>Alert</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_monitoring_py.html#t90">src\validation\monitoring.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_monitoring_py.html#t90"><data value='PerformanceTimer'>PerformanceTimer</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_monitoring_py.html#t120">src\validation\monitoring.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_monitoring_py.html#t120"><data value='SystemMonitor'>SystemMonitor</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_monitoring_py.html#t176">src\validation\monitoring.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_monitoring_py.html#t176"><data value='MetricsCollector'>MetricsCollector</data></a></td>
                <td>45</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_monitoring_py.html#t301">src\validation\monitoring.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_monitoring_py.html#t301"><data value='AlertManager'>AlertManager</data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_monitoring_py.html#t443">src\validation\monitoring.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_monitoring_py.html#t443"><data value='ValidationMonitor'>ValidationMonitor</data></a></td>
                <td>75</td>
                <td>75</td>
                <td>0</td>
                <td class="right" data-ratio="0 75">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_monitoring_py.html">src\validation\monitoring.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_monitoring_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>103</td>
                <td>103</td>
                <td>0</td>
                <td class="right" data-ratio="0 103">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_rules_py.html#t20">src\validation\rules.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_rules_py.html#t20"><data value='CDRValidationRules'>CDRValidationRules</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_rules_py.html#t117">src\validation\rules.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_rules_py.html#t117"><data value='KPIValidationRules'>KPIValidationRules</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_rules_py.html#t205">src\validation\rules.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_rules_py.html#t205"><data value='CFGValidationRules'>CFGValidationRules</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_rules_py.html#t279">src\validation\rules.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_rules_py.html#t279"><data value='SCOREValidationRules'>SCOREValidationRules</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_rules_py.html#t339">src\validation\rules.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_rules_py.html#t339"><data value='DatabaseValidationRules'>DatabaseValidationRules</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_rules_py.html#t353">src\validation\rules.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_rules_py.html#t353"><data value='ValidationRuleFactory'>ValidationRuleFactory</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_rules_py.html">src\validation\rules.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_rules_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>56</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="0 56">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_utils_py.html#t26">src\validation\utils.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_utils_py.html#t26"><data value='DataConverter'>DataConverter</data></a></td>
                <td>63</td>
                <td>63</td>
                <td>0</td>
                <td class="right" data-ratio="0 63">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_utils_py.html#t175">src\validation\utils.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_utils_py.html#t175"><data value='BatchProcessor'>BatchProcessor</data></a></td>
                <td>45</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_utils_py.html#t276">src\validation\utils.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_utils_py.html#t276"><data value='ReportGenerator'>ReportGenerator</data></a></td>
                <td>46</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_utils_py.html#t495">src\validation\utils.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_utils_py.html#t495"><data value='FileUtils'>FileUtils</data></a></td>
                <td>46</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_utils_py.html#t641">src\validation\utils.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_utils_py.html#t641"><data value='DataSampler'>DataSampler</data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_utils_py.html">src\validation\utils.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>78</td>
                <td>78</td>
                <td>0</td>
                <td class="right" data-ratio="0 78">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_validators_py.html#t32">src\validation\validators.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_validators_py.html#t32"><data value='DataStructureValidator'>DataStructureValidator</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_validators_py.html#t121">src\validation\validators.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_validators_py.html#t121"><data value='DataValueValidator'>DataValueValidator</data></a></td>
                <td>50</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="0 50">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_validators_py.html#t248">src\validation\validators.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_validators_py.html#t248"><data value='TelecomDataValidator'>TelecomDataValidator</data></a></td>
                <td>51</td>
                <td>51</td>
                <td>0</td>
                <td class="right" data-ratio="0 51">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_validators_py.html#t387">src\validation\validators.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_validators_py.html#t387"><data value='DatabaseValidator'>DatabaseValidator</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_validators_py.html#t461">src\validation\validators.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_validators_py.html#t461"><data value='FileValidator'>FileValidator</data></a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_validators_py.html">src\validation\validators.py</a></td>
                <td class="name left"><a href="z_721810ebc2c600de_validators_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>30960</td>
                <td>28423</td>
                <td>526</td>
                <td class="right" data-ratio="2537 30960">8%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-07-25 14:44 +0200
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
