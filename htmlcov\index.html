<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">8%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-07-25 14:44 +0200
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_04e2950766bc0f15___init___py.html">src\api\__init__.py</a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_04e2950766bc0f15_import_api_py.html">src\api\import_api.py</a></td>
                <td>133</td>
                <td>133</td>
                <td>3</td>
                <td class="right" data-ratio="0 133">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997___init___py.html">src\config\__init__.py</a></td>
                <td>15</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="12 15">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_core_py.html">src\config\core.py</a></td>
                <td>205</td>
                <td>162</td>
                <td>0</td>
                <td class="right" data-ratio="43 205">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_environment_py.html">src\config\environment.py</a></td>
                <td>180</td>
                <td>158</td>
                <td>0</td>
                <td class="right" data-ratio="22 180">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_loader_py.html">src\config\loader.py</a></td>
                <td>132</td>
                <td>109</td>
                <td>0</td>
                <td class="right" data-ratio="23 132">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_models_py.html">src\config\models.py</a></td>
                <td>221</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="177 221">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html">src\config\settings.py</a></td>
                <td>319</td>
                <td>274</td>
                <td>0</td>
                <td class="right" data-ratio="45 319">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3348db692d34d8b7___init___py.html">src\connect_types\__init__.py</a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3348db692d34d8b7_telecom_types_py.html">src\connect_types\telecom_types.py</a></td>
                <td>226</td>
                <td>226</td>
                <td>30</td>
                <td class="right" data-ratio="0 226">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41___init___py.html">src\core\__init__.py</a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912___init___py.html">src\core\data_processing\__init__.py</a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_685d3cbf13dcde2b___init___py.html">src\core\data_processing\adapters\__init__.py</a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_685d3cbf13dcde2b_adapter_factory_py.html">src\core\data_processing\adapters\adapter_factory.py</a></td>
                <td>153</td>
                <td>153</td>
                <td>0</td>
                <td class="right" data-ratio="0 153">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_685d3cbf13dcde2b_base_adapter_py.html">src\core\data_processing\adapters\base_adapter.py</a></td>
                <td>103</td>
                <td>103</td>
                <td>117</td>
                <td class="right" data-ratio="0 103">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_685d3cbf13dcde2b_pandas_adapter_py.html">src\core\data_processing\adapters\pandas_adapter.py</a></td>
                <td>261</td>
                <td>261</td>
                <td>0</td>
                <td class="right" data-ratio="0 261">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_685d3cbf13dcde2b_polars_adapter_py.html">src\core\data_processing\adapters\polars_adapter.py</a></td>
                <td>313</td>
                <td>313</td>
                <td>0</td>
                <td class="right" data-ratio="0 313">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_batch_processor_py.html">src\core\data_processing\batch_processor.py</a></td>
                <td>405</td>
                <td>405</td>
                <td>0</td>
                <td class="right" data-ratio="0 405">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_csv_processor_py.html">src\core\data_processing\csv_processor.py</a></td>
                <td>210</td>
                <td>210</td>
                <td>0</td>
                <td class="right" data-ratio="0 210">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_data_cleaner_py.html">src\core\data_processing\data_cleaner.py</a></td>
                <td>460</td>
                <td>460</td>
                <td>0</td>
                <td class="right" data-ratio="0 460">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_excel_processor_py.html">src\core\data_processing\excel_processor.py</a></td>
                <td>246</td>
                <td>246</td>
                <td>0</td>
                <td class="right" data-ratio="0 246">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_transformer_py.html">src\core\data_processing\transformer.py</a></td>
                <td>482</td>
                <td>482</td>
                <td>0</td>
                <td class="right" data-ratio="0 482">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cc98060f564b2912_types_py.html">src\core\data_processing\types.py</a></td>
                <td>168</td>
                <td>168</td>
                <td>0</td>
                <td class="right" data-ratio="0 168">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_890733416bc8af2f___init___py.html">src\core\utils\__init__.py</a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_890733416bc8af2f_encoding_py.html">src\core\utils\encoding.py</a></td>
                <td>52</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="0 52">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_890733416bc8af2f_logging_py.html">src\core\utils\logging.py</a></td>
                <td>196</td>
                <td>196</td>
                <td>0</td>
                <td class="right" data-ratio="0 196">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_890733416bc8af2f_memory_py.html">src\core\utils\memory.py</a></td>
                <td>223</td>
                <td>223</td>
                <td>0</td>
                <td class="right" data-ratio="0 223">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_890733416bc8af2f_performance_py.html">src\core\utils\performance.py</a></td>
                <td>199</td>
                <td>199</td>
                <td>0</td>
                <td class="right" data-ratio="0 199">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_890733416bc8af2f_validation_py.html">src\core\utils\validation.py</a></td>
                <td>255</td>
                <td>255</td>
                <td>0</td>
                <td class="right" data-ratio="0 255">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d___init___py.html">src\database\__init__.py</a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_config_py.html">src\database\config.py</a></td>
                <td>81</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="19 81">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85e7da93a84ca6b6___init___py.html">src\database\connection\__init__.py</a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85e7da93a84ca6b6_adaptive_pool_py.html">src\database\connection\adaptive_pool.py</a></td>
                <td>155</td>
                <td>155</td>
                <td>0</td>
                <td class="right" data-ratio="0 155">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85e7da93a84ca6b6_health_check_py.html">src\database\connection\health_check.py</a></td>
                <td>274</td>
                <td>214</td>
                <td>0</td>
                <td class="right" data-ratio="60 274">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85e7da93a84ca6b6_pool_py.html">src\database\connection\pool.py</a></td>
                <td>275</td>
                <td>234</td>
                <td>0</td>
                <td class="right" data-ratio="41 275">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85e7da93a84ca6b6_read_write_splitter_py.html">src\database\connection\read_write_splitter.py</a></td>
                <td>226</td>
                <td>191</td>
                <td>0</td>
                <td class="right" data-ratio="35 226">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85e7da93a84ca6b6_session_py.html">src\database\connection\session.py</a></td>
                <td>129</td>
                <td>100</td>
                <td>0</td>
                <td class="right" data-ratio="29 129">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_constants_py.html">src\database\constants.py</a></td>
                <td>139</td>
                <td>139</td>
                <td>0</td>
                <td class="right" data-ratio="0 139">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8___init___py.html">src\database\etl\__init__.py</a></td>
                <td>19</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="15 19">79%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_batch_processor_py.html">src\database\etl\batch_processor.py</a></td>
                <td>378</td>
                <td>378</td>
                <td>0</td>
                <td class="right" data-ratio="0 378">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_error_handler_py.html">src\database\etl\error_handler.py</a></td>
                <td>353</td>
                <td>353</td>
                <td>0</td>
                <td class="right" data-ratio="0 353">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_excel_processor_py.html">src\database\etl\excel_processor.py</a></td>
                <td>370</td>
                <td>370</td>
                <td>0</td>
                <td class="right" data-ratio="0 370">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_extractor_py.html">src\database\etl\extractor.py</a></td>
                <td>107</td>
                <td>85</td>
                <td>0</td>
                <td class="right" data-ratio="22 107">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_json_processor_py.html">src\database\etl\json_processor.py</a></td>
                <td>433</td>
                <td>433</td>
                <td>0</td>
                <td class="right" data-ratio="0 433">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_loader_py.html">src\database\etl\loader.py</a></td>
                <td>172</td>
                <td>145</td>
                <td>0</td>
                <td class="right" data-ratio="27 172">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_pipeline_py.html">src\database\etl\pipeline.py</a></td>
                <td>379</td>
                <td>321</td>
                <td>0</td>
                <td class="right" data-ratio="58 379">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d83242ce7f5e6032___init___py.html">src\database\etl\processors\__init__.py</a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d83242ce7f5e6032_csv_processor_py.html">src\database\etl\processors\csv_processor.py</a></td>
                <td>244</td>
                <td>223</td>
                <td>0</td>
                <td class="right" data-ratio="21 244">9%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_transformer_py.html">src\database\etl\transformer.py</a></td>
                <td>466</td>
                <td>373</td>
                <td>14</td>
                <td class="right" data-ratio="93 466">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d70c6fad0b89c4e8_validator_py.html">src\database\etl\validator.py</a></td>
                <td>469</td>
                <td>370</td>
                <td>0</td>
                <td class="right" data-ratio="99 469">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exception_handlers_py.html">src\database\exception_handlers.py</a></td>
                <td>159</td>
                <td>104</td>
                <td>0</td>
                <td class="right" data-ratio="55 159">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_exceptions_py.html">src\database\exceptions.py</a></td>
                <td>315</td>
                <td>235</td>
                <td>8</td>
                <td class="right" data-ratio="80 315">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21a03ec279c707f5___init___py.html">src\database\geospatial\__init__.py</a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21a03ec279c707f5_indexer_py.html">src\database\geospatial\indexer.py</a></td>
                <td>164</td>
                <td>164</td>
                <td>0</td>
                <td class="right" data-ratio="0 164">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21a03ec279c707f5_polygon_handler_py.html">src\database\geospatial\polygon_handler.py</a></td>
                <td>188</td>
                <td>160</td>
                <td>0</td>
                <td class="right" data-ratio="28 188">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21a03ec279c707f5_processor_py.html">src\database\geospatial\processor.py</a></td>
                <td>168</td>
                <td>134</td>
                <td>0</td>
                <td class="right" data-ratio="34 168">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21a03ec279c707f5_validator_py.html">src\database\geospatial\validator.py</a></td>
                <td>237</td>
                <td>194</td>
                <td>0</td>
                <td class="right" data-ratio="43 237">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_21a03ec279c707f5_vendor_tagger_py.html">src\database\geospatial\vendor_tagger.py</a></td>
                <td>209</td>
                <td>175</td>
                <td>0</td>
                <td class="right" data-ratio="34 209">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08849049b6a766aa___init___py.html">src\database\monitoring\__init__.py</a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08849049b6a766aa_alerts_py.html">src\database\monitoring\alerts.py</a></td>
                <td>38</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="14 38">37%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08849049b6a766aa_health_py.html">src\database\monitoring\health.py</a></td>
                <td>21</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="8 21">38%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08849049b6a766aa_logger_py.html">src\database\monitoring\logger.py</a></td>
                <td>147</td>
                <td>103</td>
                <td>0</td>
                <td class="right" data-ratio="44 147">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_08849049b6a766aa_metrics_py.html">src\database\monitoring\metrics.py</a></td>
                <td>171</td>
                <td>105</td>
                <td>0</td>
                <td class="right" data-ratio="66 171">39%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eb0a16ccbf01ec23___init___py.html">src\database\operations\__init__.py</a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eb0a16ccbf01ec23_bulk_operations_py.html">src\database\operations\bulk_operations.py</a></td>
                <td>452</td>
                <td>427</td>
                <td>0</td>
                <td class="right" data-ratio="25 452">6%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eb0a16ccbf01ec23_crud_py.html">src\database\operations\crud.py</a></td>
                <td>459</td>
                <td>427</td>
                <td>0</td>
                <td class="right" data-ratio="32 459">7%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eb0a16ccbf01ec23_database_manager_py.html">src\database\operations\database_manager.py</a></td>
                <td>292</td>
                <td>264</td>
                <td>0</td>
                <td class="right" data-ratio="28 292">10%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eb0a16ccbf01ec23_exporter_py.html">src\database\operations\exporter.py</a></td>
                <td>177</td>
                <td>154</td>
                <td>0</td>
                <td class="right" data-ratio="23 177">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eb0a16ccbf01ec23_importer_py.html">src\database\operations\importer.py</a></td>
                <td>533</td>
                <td>501</td>
                <td>0</td>
                <td class="right" data-ratio="32 533">6%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eb0a16ccbf01ec23_table_operations_py.html">src\database\operations\table_operations.py</a></td>
                <td>155</td>
                <td>133</td>
                <td>0</td>
                <td class="right" data-ratio="22 155">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_eb0a16ccbf01ec23_transaction_manager_py.html">src\database\operations\transaction_manager.py</a></td>
                <td>53</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="13 53">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7___init___py.html">src\database\query_builder\__init__.py</a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_aggregations_py.html">src\database\query_builder\aggregations.py</a></td>
                <td>196</td>
                <td>119</td>
                <td>11</td>
                <td class="right" data-ratio="77 196">39%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_builder_py.html">src\database\query_builder\builder.py</a></td>
                <td>351</td>
                <td>271</td>
                <td>8</td>
                <td class="right" data-ratio="80 351">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_conditions_py.html">src\database\query_builder\conditions.py</a></td>
                <td>237</td>
                <td>151</td>
                <td>11</td>
                <td class="right" data-ratio="86 237">36%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_dialects_py.html">src\database\query_builder\dialects.py</a></td>
                <td>173</td>
                <td>105</td>
                <td>53</td>
                <td class="right" data-ratio="68 173">39%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_joins_py.html">src\database\query_builder\joins.py</a></td>
                <td>148</td>
                <td>87</td>
                <td>11</td>
                <td class="right" data-ratio="61 148">41%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_optimizers_py.html">src\database\query_builder\optimizers.py</a></td>
                <td>226</td>
                <td>177</td>
                <td>21</td>
                <td class="right" data-ratio="49 226">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f753dd6039bbb7_validators_py.html">src\database\query_builder\validators.py</a></td>
                <td>154</td>
                <td>113</td>
                <td>21</td>
                <td class="right" data-ratio="41 154">27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d55c021fc51bfda7___init___py.html">src\database\schema\__init__.py</a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d55c021fc51bfda7_manager_py.html">src\database\schema\manager.py</a></td>
                <td>293</td>
                <td>264</td>
                <td>0</td>
                <td class="right" data-ratio="29 293">10%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d55c021fc51bfda7_models_py.html">src\database\schema\models.py</a></td>
                <td>94</td>
                <td>71</td>
                <td>0</td>
                <td class="right" data-ratio="23 94">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d55c021fc51bfda7_router_py.html">src\database\schema\router.py</a></td>
                <td>195</td>
                <td>151</td>
                <td>0</td>
                <td class="right" data-ratio="44 195">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d55c021fc51bfda7_table_schema_py.html">src\database\schema\table_schema.py</a></td>
                <td>58</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="32 58">55%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d55c021fc51bfda7_validators_py.html">src\database\schema\validators.py</a></td>
                <td>213</td>
                <td>163</td>
                <td>0</td>
                <td class="right" data-ratio="50 213">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d30ff5053af0e8e___init___py.html">src\database\security\__init__.py</a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d30ff5053af0e8e_access_control_py.html">src\database\security\access_control.py</a></td>
                <td>71</td>
                <td>71</td>
                <td>0</td>
                <td class="right" data-ratio="0 71">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d30ff5053af0e8e_audit_py.html">src\database\security\audit.py</a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3d30ff5053af0e8e_auth_py.html">src\database\security\auth.py</a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2b497613b0db077d___init___py.html">src\database\security\encryption\__init__.py</a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2b497613b0db077d_encryption_py.html">src\database\security\encryption\encryption.py</a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_67d056e3707ef46d_types_py.html">src\database\types.py</a></td>
                <td>153</td>
                <td>3</td>
                <td>38</td>
                <td class="right" data-ratio="150 153">98%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642___init___py.html">src\database\utils\__init__.py</a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642_batch_processor_py.html">src\database\utils\batch_processor.py</a></td>
                <td>136</td>
                <td>119</td>
                <td>0</td>
                <td class="right" data-ratio="17 136">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642_column_mapper_py.html">src\database\utils\column_mapper.py</a></td>
                <td>62</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="0 62">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642_decorators_py.html">src\database\utils\decorators.py</a></td>
                <td>210</td>
                <td>192</td>
                <td>0</td>
                <td class="right" data-ratio="18 210">9%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642_excel_processor_py.html">src\database\utils\excel_processor.py</a></td>
                <td>96</td>
                <td>83</td>
                <td>0</td>
                <td class="right" data-ratio="13 96">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642_helpers_py.html">src\database\utils\helpers.py</a></td>
                <td>73</td>
                <td>57</td>
                <td>0</td>
                <td class="right" data-ratio="16 73">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642_parallel_config_py.html">src\database\utils\parallel_config.py</a></td>
                <td>77</td>
                <td>77</td>
                <td>0</td>
                <td class="right" data-ratio="0 77">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642_performance_py.html">src\database\utils\performance.py</a></td>
                <td>216</td>
                <td>177</td>
                <td>0</td>
                <td class="right" data-ratio="39 216">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642_progress_tracker_py.html">src\database\utils\progress_tracker.py</a></td>
                <td>27</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="11 27">41%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642_query_optimizer_py.html">src\database\utils\query_optimizer.py</a></td>
                <td>73</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="15 73">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642_security_py.html">src\database\utils\security.py</a></td>
                <td>71</td>
                <td>49</td>
                <td>0</td>
                <td class="right" data-ratio="22 71">31%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642_table_naming_py.html">src\database\utils\table_naming.py</a></td>
                <td>379</td>
                <td>342</td>
                <td>0</td>
                <td class="right" data-ratio="37 379">10%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a72551420c087642_validators_py.html">src\database\utils\validators.py</a></td>
                <td>81</td>
                <td>57</td>
                <td>0</td>
                <td class="right" data-ratio="24 81">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_51b442c1a5736944___init___py.html">src\exporters\__init__.py</a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_51b442c1a5736944_base_py.html">src\exporters\base.py</a></td>
                <td>40</td>
                <td>40</td>
                <td>15</td>
                <td class="right" data-ratio="0 40">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_51b442c1a5736944_csv_exporter_py.html">src\exporters\csv_exporter.py</a></td>
                <td>60</td>
                <td>60</td>
                <td>0</td>
                <td class="right" data-ratio="0 60">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_51b442c1a5736944_data_formatter_py.html">src\exporters\data_formatter.py</a></td>
                <td>71</td>
                <td>71</td>
                <td>0</td>
                <td class="right" data-ratio="0 71">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_51b442c1a5736944_excel_exporter_py.html">src\exporters\excel_exporter.py</a></td>
                <td>54</td>
                <td>54</td>
                <td>0</td>
                <td class="right" data-ratio="0 54">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_51b442c1a5736944_file_manager_py.html">src\exporters\file_manager.py</a></td>
                <td>84</td>
                <td>84</td>
                <td>0</td>
                <td class="right" data-ratio="0 84">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_51b442c1a5736944_geojson_exporter_py.html">src\exporters\geojson_exporter.py</a></td>
                <td>48</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="0 48">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_51b442c1a5736944_json_exporter_py.html">src\exporters\json_exporter.py</a></td>
                <td>54</td>
                <td>54</td>
                <td>0</td>
                <td class="right" data-ratio="0 54">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_51b442c1a5736944_report_generator_py.html">src\exporters\report_generator.py</a></td>
                <td>60</td>
                <td>60</td>
                <td>0</td>
                <td class="right" data-ratio="0 60">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_13fedfab229146ee___init___py.html">src\geo\__init__.py</a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_13fedfab229146ee_geometry_py.html">src\geo\geometry.py</a></td>
                <td>132</td>
                <td>132</td>
                <td>0</td>
                <td class="right" data-ratio="0 132">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_13fedfab229146ee_qgis_integration_py.html">src\geo\qgis_integration.py</a></td>
                <td>219</td>
                <td>219</td>
                <td>0</td>
                <td class="right" data-ratio="0 219">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_13fedfab229146ee_raster_py.html">src\geo\raster.py</a></td>
                <td>111</td>
                <td>111</td>
                <td>0</td>
                <td class="right" data-ratio="0 111">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_13fedfab229146ee_vector_py.html">src\geo\vector.py</a></td>
                <td>86</td>
                <td>86</td>
                <td>0</td>
                <td class="right" data-ratio="0 86">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_22a7215c525dcefb___init___py.html">src\geospatial\__init__.py</a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_22a7215c525dcefb_analyzer_py.html">src\geospatial\analyzer.py</a></td>
                <td>159</td>
                <td>159</td>
                <td>0</td>
                <td class="right" data-ratio="0 159">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_22a7215c525dcefb_indexer_py.html">src\geospatial\indexer.py</a></td>
                <td>167</td>
                <td>167</td>
                <td>0</td>
                <td class="right" data-ratio="0 167">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_22a7215c525dcefb_processor_py.html">src\geospatial\processor.py</a></td>
                <td>187</td>
                <td>187</td>
                <td>0</td>
                <td class="right" data-ratio="0 187">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66___init___py.html">src\importers\__init__.py</a></td>
                <td>46</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_base_py.html">src\importers\base.py</a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2824de5b4311dfa3___init___py.html">src\importers\base\__init__.py</a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2824de5b4311dfa3_abstract_importer_py.html">src\importers\base\abstract_importer.py</a></td>
                <td>234</td>
                <td>234</td>
                <td>33</td>
                <td class="right" data-ratio="0 234">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2824de5b4311dfa3_performance_mixin_py.html">src\importers\base\performance_mixin.py</a></td>
                <td>251</td>
                <td>251</td>
                <td>0</td>
                <td class="right" data-ratio="0 251">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2824de5b4311dfa3_processing_mixin_py.html">src\importers\base\processing_mixin.py</a></td>
                <td>300</td>
                <td>300</td>
                <td>0</td>
                <td class="right" data-ratio="0 300">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2824de5b4311dfa3_validation_mixin_py.html">src\importers\base\validation_mixin.py</a></td>
                <td>191</td>
                <td>191</td>
                <td>0</td>
                <td class="right" data-ratio="0 191">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_batch_processor_py.html">src\importers\batch_processor.py</a></td>
                <td>144</td>
                <td>144</td>
                <td>0</td>
                <td class="right" data-ratio="0 144">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_cdr_importer_py.html">src\importers\cdr_importer.py</a></td>
                <td>660</td>
                <td>660</td>
                <td>0</td>
                <td class="right" data-ratio="0 660">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_cfg_importer_py.html">src\importers\cfg_importer.py</a></td>
                <td>172</td>
                <td>172</td>
                <td>0</td>
                <td class="right" data-ratio="0 172">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_csv_importer_py.html">src\importers\csv_importer.py</a></td>
                <td>49</td>
                <td>49</td>
                <td>0</td>
                <td class="right" data-ratio="0 49">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_data_transformer_py.html">src\importers\data_transformer.py</a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_ep_importer_py.html">src\importers\ep_importer.py</a></td>
                <td>450</td>
                <td>450</td>
                <td>0</td>
                <td class="right" data-ratio="0 450">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_excel_importer_py.html">src\importers\excel_importer.py</a></td>
                <td>59</td>
                <td>59</td>
                <td>0</td>
                <td class="right" data-ratio="0 59">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4522cb7338698c28___init___py.html">src\importers\generic\__init__.py</a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4522cb7338698c28_csv_importer_py.html">src\importers\generic\csv_importer.py</a></td>
                <td>367</td>
                <td>367</td>
                <td>0</td>
                <td class="right" data-ratio="0 367">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4522cb7338698c28_excel_importer_py.html">src\importers\generic\excel_importer.py</a></td>
                <td>381</td>
                <td>381</td>
                <td>0</td>
                <td class="right" data-ratio="0 381">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4522cb7338698c28_json_importer_py.html">src\importers\generic\json_importer.py</a></td>
                <td>479</td>
                <td>479</td>
                <td>0</td>
                <td class="right" data-ratio="0 479">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_import_manager_py.html">src\importers\import_manager.py</a></td>
                <td>391</td>
                <td>391</td>
                <td>0</td>
                <td class="right" data-ratio="0 391">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_json_importer_py.html">src\importers\json_importer.py</a></td>
                <td>63</td>
                <td>63</td>
                <td>0</td>
                <td class="right" data-ratio="0 63">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_kpi_importer_py.html">src\importers\kpi_importer.py</a></td>
                <td>230</td>
                <td>230</td>
                <td>0</td>
                <td class="right" data-ratio="0 230">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_nlg_importer_py.html">src\importers\nlg_importer.py</a></td>
                <td>411</td>
                <td>411</td>
                <td>0</td>
                <td class="right" data-ratio="0 411">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_668c0f7178569a66_score_importer_py.html">src\importers\score_importer.py</a></td>
                <td>228</td>
                <td>228</td>
                <td>0</td>
                <td class="right" data-ratio="0 228">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_37c60dde8ac853d0___init___py.html">src\importers\telecom\__init__.py</a></td>
                <td>117</td>
                <td>117</td>
                <td>0</td>
                <td class="right" data-ratio="0 117">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_37c60dde8ac853d0_cdr_importer_py.html">src\importers\telecom\cdr_importer.py</a></td>
                <td>336</td>
                <td>336</td>
                <td>0</td>
                <td class="right" data-ratio="0 336">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_37c60dde8ac853d0_ep_importer_py.html">src\importers\telecom\ep_importer.py</a></td>
                <td>676</td>
                <td>676</td>
                <td>77</td>
                <td class="right" data-ratio="0 676">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_37c60dde8ac853d0_kpi_importer_py.html">src\importers\telecom\kpi_importer.py</a></td>
                <td>326</td>
                <td>326</td>
                <td>0</td>
                <td class="right" data-ratio="0 326">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_37c60dde8ac853d0_nlg_importer_py.html">src\importers\telecom\nlg_importer.py</a></td>
                <td>562</td>
                <td>562</td>
                <td>9</td>
                <td class="right" data-ratio="0 562">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_628569355ef1e7af_csv_health_monitor_py.html">src\monitoring\csv_health_monitor.py</a></td>
                <td>160</td>
                <td>113</td>
                <td>22</td>
                <td class="right" data-ratio="47 160">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be___init___py.html">src\utils\__init__.py</a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_cache_manager_py.html">src\utils\cache_manager.py</a></td>
                <td>105</td>
                <td>105</td>
                <td>0</td>
                <td class="right" data-ratio="0 105">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_column_deduplicator_py.html">src\utils\column_deduplicator.py</a></td>
                <td>157</td>
                <td>157</td>
                <td>0</td>
                <td class="right" data-ratio="0 157">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_data_validator_py.html">src\utils\data_validator.py</a></td>
                <td>109</td>
                <td>109</td>
                <td>0</td>
                <td class="right" data-ratio="0 109">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_disk_space_manager_py.html">src\utils\disk_space_manager.py</a></td>
                <td>241</td>
                <td>241</td>
                <td>0</td>
                <td class="right" data-ratio="0 241">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be_file_handler_py.html">src\utils\file_handler.py</a></td>
                <td>166</td>
                <td>166</td>
                <td>0</td>
                <td class="right" data-ratio="0 166">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de___init___py.html">src\validation\__init__.py</a></td>
                <td>51</td>
                <td>51</td>
                <td>0</td>
                <td class="right" data-ratio="0 51">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_benchmark_py.html">src\validation\benchmark.py</a></td>
                <td>352</td>
                <td>352</td>
                <td>2</td>
                <td class="right" data-ratio="0 352">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_config_py.html">src\validation\config.py</a></td>
                <td>233</td>
                <td>233</td>
                <td>0</td>
                <td class="right" data-ratio="0 233">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_core_py.html">src\validation\core.py</a></td>
                <td>182</td>
                <td>182</td>
                <td>12</td>
                <td class="right" data-ratio="0 182">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_deploy_py.html">src\validation\deploy.py</a></td>
                <td>336</td>
                <td>336</td>
                <td>3</td>
                <td class="right" data-ratio="0 336">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_docs_generator_py.html">src\validation\docs_generator.py</a></td>
                <td>407</td>
                <td>407</td>
                <td>2</td>
                <td class="right" data-ratio="0 407">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_examples_py.html">src\validation\examples.py</a></td>
                <td>121</td>
                <td>121</td>
                <td>2</td>
                <td class="right" data-ratio="0 121">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_exceptions_py.html">src\validation\exceptions.py</a></td>
                <td>99</td>
                <td>99</td>
                <td>0</td>
                <td class="right" data-ratio="0 99">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_factory_py.html">src\validation\factory.py</a></td>
                <td>105</td>
                <td>105</td>
                <td>0</td>
                <td class="right" data-ratio="0 105">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_install_py.html">src\validation\install.py</a></td>
                <td>423</td>
                <td>423</td>
                <td>3</td>
                <td class="right" data-ratio="0 423">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_monitoring_py.html">src\validation\monitoring.py</a></td>
                <td>302</td>
                <td>302</td>
                <td>0</td>
                <td class="right" data-ratio="0 302">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_rules_py.html">src\validation\rules.py</a></td>
                <td>90</td>
                <td>90</td>
                <td>0</td>
                <td class="right" data-ratio="0 90">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_utils_py.html">src\validation\utils.py</a></td>
                <td>314</td>
                <td>314</td>
                <td>0</td>
                <td class="right" data-ratio="0 314">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_721810ebc2c600de_validators_py.html">src\validation\validators.py</a></td>
                <td>219</td>
                <td>219</td>
                <td>0</td>
                <td class="right" data-ratio="0 219">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>30960</td>
                <td>28423</td>
                <td>526</td>
                <td class="right" data-ratio="2537 30960">8%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-07-25 14:44 +0200
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_721810ebc2c600de_validators_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_04e2950766bc0f15___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
