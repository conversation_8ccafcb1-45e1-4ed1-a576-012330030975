#!/usr/bin/env python3
"""
Test NLG import process to diagnose data storage issues
"""
import asyncio
import sys
import traceback
from pathlib import Path
import pandas as pd

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def test_nlg_import():
    """Test NLG import process step by step."""
    try:
        print("🔍 Testing NLG import process...")
        
        # Test file path
        test_file = Path("D:/connect/data/input/nlg/2023/NLG_CUBE_aktuell_2023-01-03.xlsb")
        if not test_file.exists():
            print(f"❌ Test file not found: {test_file}")
            return False
        
        print(f"✅ Test file found: {test_file}")
        
        # Import necessary modules
        from src.importers.telecom.nlg_importer import NLGImporter
        from src.database.config import get_config
        
        # Create NLG importer with proper config
        config = {
            'name': 'nlg_test',
            'data_type': 'nlg',
            'operator': 'telefonica'
        }
        
        print("🔧 Creating NLG importer...")
        nlg_importer = NLGImporter(config)
        
        # Check if db_ops is properly initialized
        if hasattr(nlg_importer, 'db_ops') and nlg_importer.db_ops:
            print("✅ Database operations initialized")
        else:
            print("❌ Database operations NOT initialized")
            return False
        
        # Test table name generation
        table_name = nlg_importer.get_table_name(str(test_file))
        print(f"📊 Generated table name: {table_name}")
        
        # Test data loading (just a small sample)
        print("📖 Testing data loading...")
        try:
            # Read just the first few rows to test
            data = pd.read_excel(test_file, sheet_name='Techno_2G_4G_5G', skiprows=4, nrows=10)
            print(f"✅ Data loaded successfully: {len(data)} rows, {len(data.columns)} columns")
            print(f"📋 Columns: {list(data.columns)[:5]}...")  # Show first 5 columns
        except Exception as e:
            print(f"❌ Data loading failed: {e}")
            return False
        
        # Test database connection through db_ops
        print("🔗 Testing database connection through db_ops...")
        try:
            # Check if we can access the database through db_ops
            if hasattr(nlg_importer.db_ops, 'pool_manager'):
                async with nlg_importer.db_ops.pool_manager.get_connection() as conn:
                    result = await conn.fetchval("SELECT 1")
                    print(f"✅ Database connection through db_ops successful: {result}")
            else:
                print("❌ No pool_manager in db_ops")
                return False
        except Exception as e:
            print(f"❌ Database connection through db_ops failed: {e}")
            traceback.print_exc()
            return False
        
        # Test table creation
        print("🏗️ Testing table creation...")
        try:
            # Check if table exists
            table_exists = await nlg_importer.db_ops._check_table_exists(table_name, 'nlg_to2')
            print(f"📊 Table {table_name} exists: {table_exists}")
            
            if not table_exists:
                print("🔨 Creating table...")
                await nlg_importer.db_ops._create_table_from_dataframe(data, table_name, 'nlg_to2')
                print("✅ Table created successfully")
            
        except Exception as e:
            print(f"❌ Table creation failed: {e}")
            traceback.print_exc()
            return False
        
        # Test data storage
        print("💾 Testing data storage...")
        try:
            await nlg_importer.db_ops.store_dataframe(
                data, 
                table_name,
                schema='nlg_to2',
                if_exists='append'
            )
            print("✅ Data storage completed")
            
            # Verify data was actually inserted
            async with nlg_importer.db_ops.pool_manager.get_connection() as conn:
                count = await conn.fetchval(f'SELECT COUNT(*) FROM nlg_to2."{table_name}"')
                print(f"📊 Records in table: {count}")
                
                if count > 0:
                    print("✅ Data successfully stored in database!")
                    return True
                else:
                    print("❌ No data found in table after storage")
                    return False
                    
        except Exception as e:
            print(f"❌ Data storage failed: {e}")
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ NLG import test failed: {e}")
        traceback.print_exc()
        return False

async def test_import_manager():
    """Test NLG import through ImportManager."""
    try:
        print("\n🔍 Testing NLG import through ImportManager...")

        from src.importers.import_manager import ImportManager, ImportJobConfig

        # Create import manager
        config = {}
        import_manager = ImportManager(config)
        await import_manager.initialize()

        # Test file path
        test_file = Path("D:/connect/data/input/nlg/2023/NLG_CUBE_aktuell_2023-01-03.xlsb")
        if not test_file.exists():
            print(f"❌ Test file not found: {test_file}")
            return False

        # Create import job config
        job_config = ImportJobConfig(
            source_path=str(test_file),
            data_type='nlg',
            operator='telefonica',
            batch_size=100,  # Small batch for testing
            validate_only=False
        )

        # Create and execute import job
        job_id = await import_manager.create_import_job(job_config)
        print(f"📋 Created import job: {job_id}")

        # Execute the job
        result = await import_manager.execute_import_job(job_id)
        print(f"✅ Import job result: {result.status}")
        print(f"📊 Records processed: {result.records_processed}")
        print(f"📊 Records imported: {result.records_imported}")
        print(f"⏱️ Processing time: {result.processing_time_seconds:.2f}s")

        if result.validation_errors:
            print(f"❌ Validation errors: {result.validation_errors}")

        if result.warnings:
            print(f"⚠️ Warnings: {result.warnings}")

        # Verify data in database
        async with import_manager.pool_manager.get_connection() as conn:
            tables = await conn.fetch(
                "SELECT table_name FROM information_schema.tables WHERE table_schema = 'nlg_to2' AND table_name LIKE 'nlg_cube_aktuell_%'"
            )
            print(f"📊 NLG tables found: {len(tables)}")
            for table in tables:
                table_name = table['table_name']
                count = await conn.fetchval(f'SELECT COUNT(*) FROM nlg_to2."{table_name}"')
                print(f"  - {table_name}: {count} rows")

        await import_manager.close()
        return result.status == 'completed'

    except Exception as e:
        print(f"❌ ImportManager test failed: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting NLG import diagnosis...")

    # Test NLG import process
    success1 = asyncio.run(test_nlg_import())

    # Test import manager
    success2 = asyncio.run(test_import_manager())

    if success1 and success2:
        print("\n✅ All tests passed!")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)
