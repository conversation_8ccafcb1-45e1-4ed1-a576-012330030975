{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.8.2", "globals": "a42aa3f5f69a967425ad45dbcd2a0dfc", "files": {"z_04e2950766bc0f15___init___py": {"hash": "36ab9987f03fd09db5c05dd00b1d20c9", "index": {"url": "z_04e2950766bc0f15___init___py.html", "file": "src\\api\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_04e2950766bc0f15_import_api_py": {"hash": "71a399a6f4c661455c7b0fa3df1530ac", "index": {"url": "z_04e2950766bc0f15_import_api_py.html", "file": "src\\api\\import_api.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 133, "n_excluded": 3, "n_missing": 133, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b695c9c33e1e1997___init___py": {"hash": "e78c804480ccef0db4fe88510e59eb8a", "index": {"url": "z_b695c9c33e1e1997___init___py.html", "file": "src\\config\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 15, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b695c9c33e1e1997_core_py": {"hash": "6961a17990d65448988978401a0a51db", "index": {"url": "z_b695c9c33e1e1997_core_py.html", "file": "src\\config\\core.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 205, "n_excluded": 0, "n_missing": 162, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b695c9c33e1e1997_environment_py": {"hash": "2589b3fc7a86bcc501b7ed0857a13200", "index": {"url": "z_b695c9c33e1e1997_environment_py.html", "file": "src\\config\\environment.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 180, "n_excluded": 0, "n_missing": 158, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b695c9c33e1e1997_loader_py": {"hash": "de2575e2d90b6137bfd3e076e6e04186", "index": {"url": "z_b695c9c33e1e1997_loader_py.html", "file": "src\\config\\loader.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 132, "n_excluded": 0, "n_missing": 109, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b695c9c33e1e1997_models_py": {"hash": "624cdfc7e7b3b399f2266758b801aecc", "index": {"url": "z_b695c9c33e1e1997_models_py.html", "file": "src\\config\\models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 221, "n_excluded": 0, "n_missing": 44, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b695c9c33e1e1997_settings_py": {"hash": "1ddb3ce39187eebc57577ecc5e00700b", "index": {"url": "z_b695c9c33e1e1997_settings_py.html", "file": "src\\config\\settings.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 319, "n_excluded": 0, "n_missing": 274, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3348db692d34d8b7___init___py": {"hash": "022d646ee62ad8989786e4ab9e31891b", "index": {"url": "z_3348db692d34d8b7___init___py.html", "file": "src\\connect_types\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3348db692d34d8b7_telecom_types_py": {"hash": "e2ac6eccf4d79881222bda48bbbc7beb", "index": {"url": "z_3348db692d34d8b7_telecom_types_py.html", "file": "src\\connect_types\\telecom_types.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 226, "n_excluded": 30, "n_missing": 226, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ce21df766c911d41___init___py": {"hash": "6d33bfd902c6a543a7b29ff6a17cf840", "index": {"url": "z_ce21df766c911d41___init___py.html", "file": "src\\core\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_cc98060f564b2912___init___py": {"hash": "8dc4d521713dc8af04c747c1ef3ce189", "index": {"url": "z_cc98060f564b2912___init___py.html", "file": "src\\core\\data_processing\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 14, "n_excluded": 0, "n_missing": 14, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_685d3cbf13dcde2b___init___py": {"hash": "d30230dbe8b7eada58adc25c4a8a8442", "index": {"url": "z_685d3cbf13dcde2b___init___py.html", "file": "src\\core\\data_processing\\adapters\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 10, "n_excluded": 0, "n_missing": 10, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_685d3cbf13dcde2b_adapter_factory_py": {"hash": "882e57f968bec5d9ddaaa70ebc29428d", "index": {"url": "z_685d3cbf13dcde2b_adapter_factory_py.html", "file": "src\\core\\data_processing\\adapters\\adapter_factory.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 153, "n_excluded": 0, "n_missing": 153, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_685d3cbf13dcde2b_base_adapter_py": {"hash": "8e60a5d3afa225979922ee22fe24ad2e", "index": {"url": "z_685d3cbf13dcde2b_base_adapter_py.html", "file": "src\\core\\data_processing\\adapters\\base_adapter.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 103, "n_excluded": 117, "n_missing": 103, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_685d3cbf13dcde2b_pandas_adapter_py": {"hash": "895b145623ba58f1405f959a71a6d77e", "index": {"url": "z_685d3cbf13dcde2b_pandas_adapter_py.html", "file": "src\\core\\data_processing\\adapters\\pandas_adapter.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 261, "n_excluded": 0, "n_missing": 261, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_685d3cbf13dcde2b_polars_adapter_py": {"hash": "574060c03f1ee107a7a2428680dcee79", "index": {"url": "z_685d3cbf13dcde2b_polars_adapter_py.html", "file": "src\\core\\data_processing\\adapters\\polars_adapter.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 313, "n_excluded": 0, "n_missing": 313, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_cc98060f564b2912_batch_processor_py": {"hash": "8deafc9759cfabdee76f1755bac25779", "index": {"url": "z_cc98060f564b2912_batch_processor_py.html", "file": "src\\core\\data_processing\\batch_processor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 405, "n_excluded": 0, "n_missing": 405, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_cc98060f564b2912_csv_processor_py": {"hash": "92a3619d75508b73c743b06f1630bd5b", "index": {"url": "z_cc98060f564b2912_csv_processor_py.html", "file": "src\\core\\data_processing\\csv_processor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 210, "n_excluded": 0, "n_missing": 210, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_cc98060f564b2912_data_cleaner_py": {"hash": "773b7cca85e72a258b4181a8c450bcd9", "index": {"url": "z_cc98060f564b2912_data_cleaner_py.html", "file": "src\\core\\data_processing\\data_cleaner.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 460, "n_excluded": 0, "n_missing": 460, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_cc98060f564b2912_excel_processor_py": {"hash": "0239848b939bd8c98618d9f9162be795", "index": {"url": "z_cc98060f564b2912_excel_processor_py.html", "file": "src\\core\\data_processing\\excel_processor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 246, "n_excluded": 0, "n_missing": 246, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_cc98060f564b2912_transformer_py": {"hash": "0ab68ee524eb2143bc1194b56bdff3f8", "index": {"url": "z_cc98060f564b2912_transformer_py.html", "file": "src\\core\\data_processing\\transformer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 482, "n_excluded": 0, "n_missing": 482, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_cc98060f564b2912_types_py": {"hash": "42cb2bbf383ffdaf93e632b058705d4b", "index": {"url": "z_cc98060f564b2912_types_py.html", "file": "src\\core\\data_processing\\types.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 168, "n_excluded": 0, "n_missing": 168, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_890733416bc8af2f___init___py": {"hash": "916fe5520909c3ec9b0aa6502224d1f1", "index": {"url": "z_890733416bc8af2f___init___py.html", "file": "src\\core\\utils\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 6, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_890733416bc8af2f_encoding_py": {"hash": "925b8205e11bcf09b4be967e6c845a7c", "index": {"url": "z_890733416bc8af2f_encoding_py.html", "file": "src\\core\\utils\\encoding.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 52, "n_excluded": 0, "n_missing": 52, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_890733416bc8af2f_logging_py": {"hash": "fb4c686f0872f06d6d65197743ccfa4d", "index": {"url": "z_890733416bc8af2f_logging_py.html", "file": "src\\core\\utils\\logging.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 196, "n_excluded": 0, "n_missing": 196, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_890733416bc8af2f_memory_py": {"hash": "f4071b4b5fe0aa7c8892adc9113fd20d", "index": {"url": "z_890733416bc8af2f_memory_py.html", "file": "src\\core\\utils\\memory.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 223, "n_excluded": 0, "n_missing": 223, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_890733416bc8af2f_performance_py": {"hash": "2e6acd07982c7de030eea207666f1ebf", "index": {"url": "z_890733416bc8af2f_performance_py.html", "file": "src\\core\\utils\\performance.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 199, "n_excluded": 0, "n_missing": 199, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_890733416bc8af2f_validation_py": {"hash": "0a0aa64208eb3599c16e7f15cce6b54d", "index": {"url": "z_890733416bc8af2f_validation_py.html", "file": "src\\core\\utils\\validation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 255, "n_excluded": 0, "n_missing": 255, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67d056e3707ef46d___init___py": {"hash": "29b1e11c56d671f5cb5dc53fef00b75b", "index": {"url": "z_67d056e3707ef46d___init___py.html", "file": "src\\database\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 12, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67d056e3707ef46d_config_py": {"hash": "ccd577fde14bc34603b7b6544700dc61", "index": {"url": "z_67d056e3707ef46d_config_py.html", "file": "src\\database\\config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 81, "n_excluded": 0, "n_missing": 62, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_85e7da93a84ca6b6___init___py": {"hash": "467d006acf7ba5579f2d28136aca0a6a", "index": {"url": "z_85e7da93a84ca6b6___init___py.html", "file": "src\\database\\connection\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 8, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_85e7da93a84ca6b6_adaptive_pool_py": {"hash": "09c3471612725b86b75ec2e9be5326ad", "index": {"url": "z_85e7da93a84ca6b6_adaptive_pool_py.html", "file": "src\\database\\connection\\adaptive_pool.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 155, "n_excluded": 0, "n_missing": 155, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_85e7da93a84ca6b6_health_check_py": {"hash": "487f4acd075e98d907ac1911794047e4", "index": {"url": "z_85e7da93a84ca6b6_health_check_py.html", "file": "src\\database\\connection\\health_check.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 274, "n_excluded": 0, "n_missing": 214, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_85e7da93a84ca6b6_pool_py": {"hash": "94609c0ea2fb8e2905f9c42270045ae9", "index": {"url": "z_85e7da93a84ca6b6_pool_py.html", "file": "src\\database\\connection\\pool.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 275, "n_excluded": 0, "n_missing": 234, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_85e7da93a84ca6b6_read_write_splitter_py": {"hash": "fe1dfb127d427af1f5402d4892163114", "index": {"url": "z_85e7da93a84ca6b6_read_write_splitter_py.html", "file": "src\\database\\connection\\read_write_splitter.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 226, "n_excluded": 0, "n_missing": 191, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_85e7da93a84ca6b6_session_py": {"hash": "44518e3ba60493d9f1cb2120416e7655", "index": {"url": "z_85e7da93a84ca6b6_session_py.html", "file": "src\\database\\connection\\session.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 129, "n_excluded": 0, "n_missing": 100, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67d056e3707ef46d_constants_py": {"hash": "c7d454970fcd90706bd918df8192068f", "index": {"url": "z_67d056e3707ef46d_constants_py.html", "file": "src\\database\\constants.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 139, "n_excluded": 0, "n_missing": 139, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d70c6fad0b89c4e8___init___py": {"hash": "4d213cd7fa5285e49296b74678ec3834", "index": {"url": "z_d70c6fad0b89c4e8___init___py.html", "file": "src\\database\\etl\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 19, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d70c6fad0b89c4e8_batch_processor_py": {"hash": "1e32d4a83c7aecee4d73147443917fc8", "index": {"url": "z_d70c6fad0b89c4e8_batch_processor_py.html", "file": "src\\database\\etl\\batch_processor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 378, "n_excluded": 0, "n_missing": 378, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d70c6fad0b89c4e8_error_handler_py": {"hash": "13c89f9b18acb026d9ac54dff2a8491d", "index": {"url": "z_d70c6fad0b89c4e8_error_handler_py.html", "file": "src\\database\\etl\\error_handler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 353, "n_excluded": 0, "n_missing": 353, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d70c6fad0b89c4e8_excel_processor_py": {"hash": "ea0a3e12a9f362aa086bb0e00d457625", "index": {"url": "z_d70c6fad0b89c4e8_excel_processor_py.html", "file": "src\\database\\etl\\excel_processor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 370, "n_excluded": 0, "n_missing": 370, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d70c6fad0b89c4e8_extractor_py": {"hash": "678a00001e17841fa70f7889e6591291", "index": {"url": "z_d70c6fad0b89c4e8_extractor_py.html", "file": "src\\database\\etl\\extractor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 107, "n_excluded": 0, "n_missing": 85, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d70c6fad0b89c4e8_json_processor_py": {"hash": "18ee2f05736325252952c86195607ff6", "index": {"url": "z_d70c6fad0b89c4e8_json_processor_py.html", "file": "src\\database\\etl\\json_processor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 433, "n_excluded": 0, "n_missing": 433, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d70c6fad0b89c4e8_loader_py": {"hash": "34a3a521f29c2720f372d3c12d36a47d", "index": {"url": "z_d70c6fad0b89c4e8_loader_py.html", "file": "src\\database\\etl\\loader.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 172, "n_excluded": 0, "n_missing": 145, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d70c6fad0b89c4e8_pipeline_py": {"hash": "d9aa4201f0b36473f6391bf88a021583", "index": {"url": "z_d70c6fad0b89c4e8_pipeline_py.html", "file": "src\\database\\etl\\pipeline.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 379, "n_excluded": 0, "n_missing": 321, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d83242ce7f5e6032___init___py": {"hash": "fa626c0979feacff0bc7b92d6b2cefd9", "index": {"url": "z_d83242ce7f5e6032___init___py.html", "file": "src\\database\\etl\\processors\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d83242ce7f5e6032_csv_processor_py": {"hash": "eeeb56e241821988c3aa299b6580ea7b", "index": {"url": "z_d83242ce7f5e6032_csv_processor_py.html", "file": "src\\database\\etl\\processors\\csv_processor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 244, "n_excluded": 0, "n_missing": 223, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d70c6fad0b89c4e8_transformer_py": {"hash": "341328a9ea15cb2d304cb450840690fe", "index": {"url": "z_d70c6fad0b89c4e8_transformer_py.html", "file": "src\\database\\etl\\transformer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 466, "n_excluded": 14, "n_missing": 373, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d70c6fad0b89c4e8_validator_py": {"hash": "9d112f215d1fa0e2adf706140690d715", "index": {"url": "z_d70c6fad0b89c4e8_validator_py.html", "file": "src\\database\\etl\\validator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 469, "n_excluded": 0, "n_missing": 370, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67d056e3707ef46d_exception_handlers_py": {"hash": "da01328ab4466f06b3184e755f6a3e69", "index": {"url": "z_67d056e3707ef46d_exception_handlers_py.html", "file": "src\\database\\exception_handlers.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 159, "n_excluded": 0, "n_missing": 104, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67d056e3707ef46d_exceptions_py": {"hash": "ff76ded02fbf434de635425db45dba46", "index": {"url": "z_67d056e3707ef46d_exceptions_py.html", "file": "src\\database\\exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 315, "n_excluded": 8, "n_missing": 235, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_21a03ec279c707f5___init___py": {"hash": "b734681a032562bd9e1d78aed43d2b30", "index": {"url": "z_21a03ec279c707f5___init___py.html", "file": "src\\database\\geospatial\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 7, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_21a03ec279c707f5_indexer_py": {"hash": "ea61482eb05c878c8aabcc8cb2695df1", "index": {"url": "z_21a03ec279c707f5_indexer_py.html", "file": "src\\database\\geospatial\\indexer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 164, "n_excluded": 0, "n_missing": 164, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_21a03ec279c707f5_polygon_handler_py": {"hash": "********************************", "index": {"url": "z_21a03ec279c707f5_polygon_handler_py.html", "file": "src\\database\\geospatial\\polygon_handler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 188, "n_excluded": 0, "n_missing": 160, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_21a03ec279c707f5_processor_py": {"hash": "c0a16a1df5e696bbc1b6a974cab5aa93", "index": {"url": "z_21a03ec279c707f5_processor_py.html", "file": "src\\database\\geospatial\\processor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 168, "n_excluded": 0, "n_missing": 134, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_21a03ec279c707f5_validator_py": {"hash": "af436b90bb61235ab0b5a72c42ce11c1", "index": {"url": "z_21a03ec279c707f5_validator_py.html", "file": "src\\database\\geospatial\\validator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 237, "n_excluded": 0, "n_missing": 194, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_21a03ec279c707f5_vendor_tagger_py": {"hash": "ad38c896d38110fee282474efc8cefaf", "index": {"url": "z_21a03ec279c707f5_vendor_tagger_py.html", "file": "src\\database\\geospatial\\vendor_tagger.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 209, "n_excluded": 0, "n_missing": 175, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_08849049b6a766aa___init___py": {"hash": "ed2645f6a4fdad662bd5ac32de28ee67", "index": {"url": "z_08849049b6a766aa___init___py.html", "file": "src\\database\\monitoring\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_08849049b6a766aa_alerts_py": {"hash": "74f0a958bcf28b95d0a7d57e9f7cd53d", "index": {"url": "z_08849049b6a766aa_alerts_py.html", "file": "src\\database\\monitoring\\alerts.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 38, "n_excluded": 0, "n_missing": 24, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_08849049b6a766aa_health_py": {"hash": "0c9b7658c32a8ea78971b34dd0bbdb52", "index": {"url": "z_08849049b6a766aa_health_py.html", "file": "src\\database\\monitoring\\health.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 21, "n_excluded": 0, "n_missing": 13, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_08849049b6a766aa_logger_py": {"hash": "3f77068c23713178fa390f53b4b542dd", "index": {"url": "z_08849049b6a766aa_logger_py.html", "file": "src\\database\\monitoring\\logger.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 147, "n_excluded": 0, "n_missing": 103, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_08849049b6a766aa_metrics_py": {"hash": "55691356fcad485de5a6d96f0c89ff41", "index": {"url": "z_08849049b6a766aa_metrics_py.html", "file": "src\\database\\monitoring\\metrics.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 171, "n_excluded": 0, "n_missing": 105, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_eb0a16ccbf01ec23___init___py": {"hash": "71929d24afebf7dca6785be09ddffc5e", "index": {"url": "z_eb0a16ccbf01ec23___init___py.html", "file": "src\\database\\operations\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 9, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_eb0a16ccbf01ec23_bulk_operations_py": {"hash": "ff0f7c5aab549d06af1b1c711bc9b6ab", "index": {"url": "z_eb0a16ccbf01ec23_bulk_operations_py.html", "file": "src\\database\\operations\\bulk_operations.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 452, "n_excluded": 0, "n_missing": 427, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_eb0a16ccbf01ec23_crud_py": {"hash": "00d0ef3047681053d6027ef9d7053a6d", "index": {"url": "z_eb0a16ccbf01ec23_crud_py.html", "file": "src\\database\\operations\\crud.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 459, "n_excluded": 0, "n_missing": 427, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_eb0a16ccbf01ec23_database_manager_py": {"hash": "0269c25c50508bae83f5e2daf20e8221", "index": {"url": "z_eb0a16ccbf01ec23_database_manager_py.html", "file": "src\\database\\operations\\database_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 292, "n_excluded": 0, "n_missing": 264, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_eb0a16ccbf01ec23_exporter_py": {"hash": "ee3430b4869d47cc31bcaef147294b30", "index": {"url": "z_eb0a16ccbf01ec23_exporter_py.html", "file": "src\\database\\operations\\exporter.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 177, "n_excluded": 0, "n_missing": 154, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_eb0a16ccbf01ec23_importer_py": {"hash": "aac00d7ee03c76057821a605c6ef31a7", "index": {"url": "z_eb0a16ccbf01ec23_importer_py.html", "file": "src\\database\\operations\\importer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 533, "n_excluded": 0, "n_missing": 501, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_eb0a16ccbf01ec23_table_operations_py": {"hash": "2fa9551b6764db69a69c945bd6e0a653", "index": {"url": "z_eb0a16ccbf01ec23_table_operations_py.html", "file": "src\\database\\operations\\table_operations.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 155, "n_excluded": 0, "n_missing": 133, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_eb0a16ccbf01ec23_transaction_manager_py": {"hash": "711744abe0f5faf74ecf7b21a1d5beb2", "index": {"url": "z_eb0a16ccbf01ec23_transaction_manager_py.html", "file": "src\\database\\operations\\transaction_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 53, "n_excluded": 0, "n_missing": 40, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_41f753dd6039bbb7___init___py": {"hash": "beec2c6125739a70b3bd11bb1984a5f4", "index": {"url": "z_41f753dd6039bbb7___init___py.html", "file": "src\\database\\query_builder\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 8, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_41f753dd6039bbb7_aggregations_py": {"hash": "e3a41f7840507f1583b90c1653e8308a", "index": {"url": "z_41f753dd6039bbb7_aggregations_py.html", "file": "src\\database\\query_builder\\aggregations.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 196, "n_excluded": 11, "n_missing": 119, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_41f753dd6039bbb7_builder_py": {"hash": "4e0948600fec33bfbfe0aa60ea1c23ef", "index": {"url": "z_41f753dd6039bbb7_builder_py.html", "file": "src\\database\\query_builder\\builder.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 351, "n_excluded": 8, "n_missing": 271, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_41f753dd6039bbb7_conditions_py": {"hash": "2b758ecefbeef1588742acdbb892a3c8", "index": {"url": "z_41f753dd6039bbb7_conditions_py.html", "file": "src\\database\\query_builder\\conditions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 237, "n_excluded": 11, "n_missing": 151, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_41f753dd6039bbb7_dialects_py": {"hash": "b7e7c5c74e48f217086acb713bcabce6", "index": {"url": "z_41f753dd6039bbb7_dialects_py.html", "file": "src\\database\\query_builder\\dialects.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 173, "n_excluded": 53, "n_missing": 105, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_41f753dd6039bbb7_joins_py": {"hash": "8d81185c68278da244f7957f5de18f8c", "index": {"url": "z_41f753dd6039bbb7_joins_py.html", "file": "src\\database\\query_builder\\joins.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 148, "n_excluded": 11, "n_missing": 87, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_41f753dd6039bbb7_optimizers_py": {"hash": "eff6fe09118b3ae59d242faf5306396e", "index": {"url": "z_41f753dd6039bbb7_optimizers_py.html", "file": "src\\database\\query_builder\\optimizers.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 226, "n_excluded": 21, "n_missing": 177, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_41f753dd6039bbb7_validators_py": {"hash": "0a6b3f605cdd25052baf337d223f902a", "index": {"url": "z_41f753dd6039bbb7_validators_py.html", "file": "src\\database\\query_builder\\validators.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 154, "n_excluded": 21, "n_missing": 113, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d55c021fc51bfda7___init___py": {"hash": "275d2e6a590080923f41fd8629c3547f", "index": {"url": "z_d55c021fc51bfda7___init___py.html", "file": "src\\database\\schema\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d55c021fc51bfda7_manager_py": {"hash": "98e057f569f1f90d3322c696e8c89ac3", "index": {"url": "z_d55c021fc51bfda7_manager_py.html", "file": "src\\database\\schema\\manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 293, "n_excluded": 0, "n_missing": 264, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d55c021fc51bfda7_models_py": {"hash": "8494a468acefd0447ca606b4aa8f81dc", "index": {"url": "z_d55c021fc51bfda7_models_py.html", "file": "src\\database\\schema\\models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 94, "n_excluded": 0, "n_missing": 71, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d55c021fc51bfda7_router_py": {"hash": "98937f6b54cca8613c5b138261478696", "index": {"url": "z_d55c021fc51bfda7_router_py.html", "file": "src\\database\\schema\\router.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 195, "n_excluded": 0, "n_missing": 151, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d55c021fc51bfda7_table_schema_py": {"hash": "8e8f173d90c2d06d7debac40c6746669", "index": {"url": "z_d55c021fc51bfda7_table_schema_py.html", "file": "src\\database\\schema\\table_schema.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 58, "n_excluded": 0, "n_missing": 26, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d55c021fc51bfda7_validators_py": {"hash": "56c8f7a8d87fe5a11f2cbb54d29ba47e", "index": {"url": "z_d55c021fc51bfda7_validators_py.html", "file": "src\\database\\schema\\validators.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 213, "n_excluded": 0, "n_missing": 163, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3d30ff5053af0e8e___init___py": {"hash": "f17943ef53ddaa8431431bc899854462", "index": {"url": "z_3d30ff5053af0e8e___init___py.html", "file": "src\\database\\security\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3d30ff5053af0e8e_access_control_py": {"hash": "e865cc5f605ebd54fbde4c9bf1e01770", "index": {"url": "z_3d30ff5053af0e8e_access_control_py.html", "file": "src\\database\\security\\access_control.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 71, "n_excluded": 0, "n_missing": 71, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3d30ff5053af0e8e_audit_py": {"hash": "f18304d49851fa01f81b0eace61299da", "index": {"url": "z_3d30ff5053af0e8e_audit_py.html", "file": "src\\database\\security\\audit.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 25, "n_excluded": 0, "n_missing": 25, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3d30ff5053af0e8e_auth_py": {"hash": "860a727e2d2ce1b40acb1135fdbae803", "index": {"url": "z_3d30ff5053af0e8e_auth_py.html", "file": "src\\database\\security\\auth.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 7, "n_excluded": 0, "n_missing": 7, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2b497613b0db077d___init___py": {"hash": "c7b42784d279e8417e5e4def8975314b", "index": {"url": "z_2b497613b0db077d___init___py.html", "file": "src\\database\\security\\encryption\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2b497613b0db077d_encryption_py": {"hash": "ca2378d57b9caa7f634abc1adf62a99c", "index": {"url": "z_2b497613b0db077d_encryption_py.html", "file": "src\\database\\security\\encryption\\encryption.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 7, "n_excluded": 0, "n_missing": 7, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_67d056e3707ef46d_types_py": {"hash": "6a2eaa68305722c1907bfdfbc3584d24", "index": {"url": "z_67d056e3707ef46d_types_py.html", "file": "src\\database\\types.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 153, "n_excluded": 38, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a72551420c087642___init___py": {"hash": "4afb8d078121a90eaa0f0cbb8841f252", "index": {"url": "z_a72551420c087642___init___py.html", "file": "src\\database\\utils\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 7, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a72551420c087642_batch_processor_py": {"hash": "4a0d8712d56baeaa759f61ebb736e1c7", "index": {"url": "z_a72551420c087642_batch_processor_py.html", "file": "src\\database\\utils\\batch_processor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 136, "n_excluded": 0, "n_missing": 119, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a72551420c087642_column_mapper_py": {"hash": "df1743050727c5dfad6a9d170f2f70c3", "index": {"url": "z_a72551420c087642_column_mapper_py.html", "file": "src\\database\\utils\\column_mapper.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 62, "n_excluded": 0, "n_missing": 62, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a72551420c087642_decorators_py": {"hash": "05ebd517d5ac3f172e9ca5850ded50fa", "index": {"url": "z_a72551420c087642_decorators_py.html", "file": "src\\database\\utils\\decorators.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 210, "n_excluded": 0, "n_missing": 192, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a72551420c087642_excel_processor_py": {"hash": "9a02f03f420f390fa7a95687abcfda6c", "index": {"url": "z_a72551420c087642_excel_processor_py.html", "file": "src\\database\\utils\\excel_processor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 96, "n_excluded": 0, "n_missing": 83, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a72551420c087642_helpers_py": {"hash": "208e71a5d2c59d73ab582a8e32dff7f6", "index": {"url": "z_a72551420c087642_helpers_py.html", "file": "src\\database\\utils\\helpers.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 73, "n_excluded": 0, "n_missing": 57, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a72551420c087642_parallel_config_py": {"hash": "c32626cfcd4637fc70ac081c48ca5be5", "index": {"url": "z_a72551420c087642_parallel_config_py.html", "file": "src\\database\\utils\\parallel_config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 77, "n_excluded": 0, "n_missing": 77, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a72551420c087642_performance_py": {"hash": "7915a843974c241809bfb84797aa1084", "index": {"url": "z_a72551420c087642_performance_py.html", "file": "src\\database\\utils\\performance.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 216, "n_excluded": 0, "n_missing": 177, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a72551420c087642_progress_tracker_py": {"hash": "440f6345f2d7a51ec37a6bb31e2d5768", "index": {"url": "z_a72551420c087642_progress_tracker_py.html", "file": "src\\database\\utils\\progress_tracker.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 27, "n_excluded": 0, "n_missing": 16, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a72551420c087642_query_optimizer_py": {"hash": "d4f0cd1a0a65a949c5c2fd1f8435cb85", "index": {"url": "z_a72551420c087642_query_optimizer_py.html", "file": "src\\database\\utils\\query_optimizer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 73, "n_excluded": 0, "n_missing": 58, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a72551420c087642_security_py": {"hash": "50a8fcb5c43c1154f1ab2a1d119e8b6a", "index": {"url": "z_a72551420c087642_security_py.html", "file": "src\\database\\utils\\security.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 71, "n_excluded": 0, "n_missing": 49, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a72551420c087642_table_naming_py": {"hash": "9b16de031d7d08bf7029484caa63748c", "index": {"url": "z_a72551420c087642_table_naming_py.html", "file": "src\\database\\utils\\table_naming.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 379, "n_excluded": 0, "n_missing": 342, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a72551420c087642_validators_py": {"hash": "5aa4eff26c1a332acaf61c3252c98dca", "index": {"url": "z_a72551420c087642_validators_py.html", "file": "src\\database\\utils\\validators.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 81, "n_excluded": 0, "n_missing": 57, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_51b442c1a5736944___init___py": {"hash": "4549f1f3df06afd4fcac3dc416d6e2fc", "index": {"url": "z_51b442c1a5736944___init___py.html", "file": "src\\exporters\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 12, "n_excluded": 0, "n_missing": 12, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_51b442c1a5736944_base_py": {"hash": "9711b530781c078bcb5ce478adc9621c", "index": {"url": "z_51b442c1a5736944_base_py.html", "file": "src\\exporters\\base.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 40, "n_excluded": 15, "n_missing": 40, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_51b442c1a5736944_csv_exporter_py": {"hash": "1500fbb8ed7876455d8c190ecbaa789d", "index": {"url": "z_51b442c1a5736944_csv_exporter_py.html", "file": "src\\exporters\\csv_exporter.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 60, "n_excluded": 0, "n_missing": 60, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_51b442c1a5736944_data_formatter_py": {"hash": "2de73de544e69c7021c3cb018c9a277b", "index": {"url": "z_51b442c1a5736944_data_formatter_py.html", "file": "src\\exporters\\data_formatter.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 71, "n_excluded": 0, "n_missing": 71, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_51b442c1a5736944_excel_exporter_py": {"hash": "fa3f30fe7e612f627e08a52b1e91c619", "index": {"url": "z_51b442c1a5736944_excel_exporter_py.html", "file": "src\\exporters\\excel_exporter.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 54, "n_excluded": 0, "n_missing": 54, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_51b442c1a5736944_file_manager_py": {"hash": "0cdbdd21e720ba7d1a3a184eb9fde9a0", "index": {"url": "z_51b442c1a5736944_file_manager_py.html", "file": "src\\exporters\\file_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 84, "n_excluded": 0, "n_missing": 84, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_51b442c1a5736944_geojson_exporter_py": {"hash": "707f1641a09984c3231d8ba078242bec", "index": {"url": "z_51b442c1a5736944_geojson_exporter_py.html", "file": "src\\exporters\\geojson_exporter.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 48, "n_excluded": 0, "n_missing": 48, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_51b442c1a5736944_json_exporter_py": {"hash": "4c04ce199a6fb7707d4d8d7de193a9c5", "index": {"url": "z_51b442c1a5736944_json_exporter_py.html", "file": "src\\exporters\\json_exporter.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 54, "n_excluded": 0, "n_missing": 54, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_51b442c1a5736944_report_generator_py": {"hash": "27dfd7fffbdd6c40bb62009e5fb5d3b2", "index": {"url": "z_51b442c1a5736944_report_generator_py.html", "file": "src\\exporters\\report_generator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 60, "n_excluded": 0, "n_missing": 60, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_13fedfab229146ee___init___py": {"hash": "40e49d3d3d9c642f71f668dd326c27c8", "index": {"url": "z_13fedfab229146ee___init___py.html", "file": "src\\geo\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 11, "n_excluded": 0, "n_missing": 11, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_13fedfab229146ee_geometry_py": {"hash": "0bd5bac34f54325dfcbf625e7e8c3763", "index": {"url": "z_13fedfab229146ee_geometry_py.html", "file": "src\\geo\\geometry.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 132, "n_excluded": 0, "n_missing": 132, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_13fedfab229146ee_qgis_integration_py": {"hash": "5523d79b86b222d98ea0f51946d09c68", "index": {"url": "z_13fedfab229146ee_qgis_integration_py.html", "file": "src\\geo\\qgis_integration.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 219, "n_excluded": 0, "n_missing": 219, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_13fedfab229146ee_raster_py": {"hash": "32581677d1e5b8cec90bfd86cdeb0cc1", "index": {"url": "z_13fedfab229146ee_raster_py.html", "file": "src\\geo\\raster.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 111, "n_excluded": 0, "n_missing": 111, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_13fedfab229146ee_vector_py": {"hash": "ac173d2c39e3aa663510553c1c22e012", "index": {"url": "z_13fedfab229146ee_vector_py.html", "file": "src\\geo\\vector.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 86, "n_excluded": 0, "n_missing": 86, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_22a7215c525dcefb___init___py": {"hash": "5f6d8d47615b7415004aa18f0f27b57a", "index": {"url": "z_22a7215c525dcefb___init___py.html", "file": "src\\geospatial\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 6, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_22a7215c525dcefb_analyzer_py": {"hash": "7ef6c0a3aa375bcf1b76849b34b6d999", "index": {"url": "z_22a7215c525dcefb_analyzer_py.html", "file": "src\\geospatial\\analyzer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 159, "n_excluded": 0, "n_missing": 159, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_22a7215c525dcefb_indexer_py": {"hash": "2a90a27094ffd640c2b92bc8fd98a27b", "index": {"url": "z_22a7215c525dcefb_indexer_py.html", "file": "src\\geospatial\\indexer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 167, "n_excluded": 0, "n_missing": 167, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_22a7215c525dcefb_processor_py": {"hash": "e52d529cd8452aa44b33188bfbb79ed3", "index": {"url": "z_22a7215c525dcefb_processor_py.html", "file": "src\\geospatial\\processor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 187, "n_excluded": 0, "n_missing": 187, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_668c0f7178569a66___init___py": {"hash": "36178cc46197cb4a29122c89ad17cd60", "index": {"url": "z_668c0f7178569a66___init___py.html", "file": "src\\importers\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 46, "n_excluded": 0, "n_missing": 46, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_668c0f7178569a66_base_py": {"hash": "fbe2a1533309f60481494c745ebe0878", "index": {"url": "z_668c0f7178569a66_base_py.html", "file": "src\\importers\\base.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 28, "n_excluded": 0, "n_missing": 28, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2824de5b4311dfa3___init___py": {"hash": "6d73f8ca0e5338278eb5e3c9fbff1be2", "index": {"url": "z_2824de5b4311dfa3___init___py.html", "file": "src\\importers\\base\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 14, "n_excluded": 0, "n_missing": 14, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2824de5b4311dfa3_abstract_importer_py": {"hash": "ef8ff5eab787b7ff028dc6c5372ea46a", "index": {"url": "z_2824de5b4311dfa3_abstract_importer_py.html", "file": "src\\importers\\base\\abstract_importer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 234, "n_excluded": 33, "n_missing": 234, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2824de5b4311dfa3_performance_mixin_py": {"hash": "7d734d14e1bd7b59c30e317f38e7f7d7", "index": {"url": "z_2824de5b4311dfa3_performance_mixin_py.html", "file": "src\\importers\\base\\performance_mixin.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 251, "n_excluded": 0, "n_missing": 251, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2824de5b4311dfa3_processing_mixin_py": {"hash": "9b92da95b22868e6f3a3930233714465", "index": {"url": "z_2824de5b4311dfa3_processing_mixin_py.html", "file": "src\\importers\\base\\processing_mixin.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 300, "n_excluded": 0, "n_missing": 300, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2824de5b4311dfa3_validation_mixin_py": {"hash": "8db23f21f9d94ffd3c3de920d995d6c4", "index": {"url": "z_2824de5b4311dfa3_validation_mixin_py.html", "file": "src\\importers\\base\\validation_mixin.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 191, "n_excluded": 0, "n_missing": 191, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_668c0f7178569a66_batch_processor_py": {"hash": "b7ea94ffc119fd9cbc47a6576404452d", "index": {"url": "z_668c0f7178569a66_batch_processor_py.html", "file": "src\\importers\\batch_processor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 144, "n_excluded": 0, "n_missing": 144, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_668c0f7178569a66_cdr_importer_py": {"hash": "6c5af8d9cb453f59de84966ecfa7c845", "index": {"url": "z_668c0f7178569a66_cdr_importer_py.html", "file": "src\\importers\\cdr_importer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 660, "n_excluded": 0, "n_missing": 660, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_668c0f7178569a66_cfg_importer_py": {"hash": "fed12649cf09b397e588836bcab1134d", "index": {"url": "z_668c0f7178569a66_cfg_importer_py.html", "file": "src\\importers\\cfg_importer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 172, "n_excluded": 0, "n_missing": 172, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_668c0f7178569a66_csv_importer_py": {"hash": "605b4b547cd6124000df798a0921375e", "index": {"url": "z_668c0f7178569a66_csv_importer_py.html", "file": "src\\importers\\csv_importer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 49, "n_excluded": 0, "n_missing": 49, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_668c0f7178569a66_data_transformer_py": {"hash": "9548dce70d03e040cb9067dc51bfbbbf", "index": {"url": "z_668c0f7178569a66_data_transformer_py.html", "file": "src\\importers\\data_transformer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 36, "n_excluded": 0, "n_missing": 36, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_668c0f7178569a66_ep_importer_py": {"hash": "1f87ec059dd693c8017e4ca435c30471", "index": {"url": "z_668c0f7178569a66_ep_importer_py.html", "file": "src\\importers\\ep_importer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 450, "n_excluded": 0, "n_missing": 450, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_668c0f7178569a66_excel_importer_py": {"hash": "d3e6404c94a0914ca039ba341531e011", "index": {"url": "z_668c0f7178569a66_excel_importer_py.html", "file": "src\\importers\\excel_importer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 59, "n_excluded": 0, "n_missing": 59, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4522cb7338698c28___init___py": {"hash": "39495d8de8e2884533538c34fcdf33ef", "index": {"url": "z_4522cb7338698c28___init___py.html", "file": "src\\importers\\generic\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 37, "n_excluded": 0, "n_missing": 37, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4522cb7338698c28_csv_importer_py": {"hash": "3d4d38c1cd4743ff9d43bef57e45d5c6", "index": {"url": "z_4522cb7338698c28_csv_importer_py.html", "file": "src\\importers\\generic\\csv_importer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 367, "n_excluded": 0, "n_missing": 367, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4522cb7338698c28_excel_importer_py": {"hash": "ed3ce03d53c57725d9e10af76a6362dd", "index": {"url": "z_4522cb7338698c28_excel_importer_py.html", "file": "src\\importers\\generic\\excel_importer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 381, "n_excluded": 0, "n_missing": 381, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4522cb7338698c28_json_importer_py": {"hash": "c9833c2449dd4c5a254619003032df42", "index": {"url": "z_4522cb7338698c28_json_importer_py.html", "file": "src\\importers\\generic\\json_importer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 479, "n_excluded": 0, "n_missing": 479, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_668c0f7178569a66_import_manager_py": {"hash": "b51a12bf07ffc398aebe3f8e5e7f8248", "index": {"url": "z_668c0f7178569a66_import_manager_py.html", "file": "src\\importers\\import_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 391, "n_excluded": 0, "n_missing": 391, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_668c0f7178569a66_json_importer_py": {"hash": "fdb8583f8ba5afe3ebd460b96319bf01", "index": {"url": "z_668c0f7178569a66_json_importer_py.html", "file": "src\\importers\\json_importer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 63, "n_excluded": 0, "n_missing": 63, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_668c0f7178569a66_kpi_importer_py": {"hash": "d62a8b9894cda900d8553ba8b45a0bfe", "index": {"url": "z_668c0f7178569a66_kpi_importer_py.html", "file": "src\\importers\\kpi_importer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 230, "n_excluded": 0, "n_missing": 230, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_668c0f7178569a66_nlg_importer_py": {"hash": "d74a925594791f0a7ea18a7840dbc8a1", "index": {"url": "z_668c0f7178569a66_nlg_importer_py.html", "file": "src\\importers\\nlg_importer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 411, "n_excluded": 0, "n_missing": 411, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_668c0f7178569a66_score_importer_py": {"hash": "da46f2824c406606a10ce0a0b9b9b4cb", "index": {"url": "z_668c0f7178569a66_score_importer_py.html", "file": "src\\importers\\score_importer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 228, "n_excluded": 0, "n_missing": 228, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_37c60dde8ac853d0___init___py": {"hash": "bfce977b1216692d57b7d918b2139662", "index": {"url": "z_37c60dde8ac853d0___init___py.html", "file": "src\\importers\\telecom\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 117, "n_excluded": 0, "n_missing": 117, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_37c60dde8ac853d0_cdr_importer_py": {"hash": "1728593096fe8ec45c9d594b8da27b19", "index": {"url": "z_37c60dde8ac853d0_cdr_importer_py.html", "file": "src\\importers\\telecom\\cdr_importer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 336, "n_excluded": 0, "n_missing": 336, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_37c60dde8ac853d0_ep_importer_py": {"hash": "99dd0d46c04d0f167d60dffb5729683e", "index": {"url": "z_37c60dde8ac853d0_ep_importer_py.html", "file": "src\\importers\\telecom\\ep_importer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 676, "n_excluded": 77, "n_missing": 676, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_37c60dde8ac853d0_kpi_importer_py": {"hash": "69dbaace16fd569251a6638f1060cb6d", "index": {"url": "z_37c60dde8ac853d0_kpi_importer_py.html", "file": "src\\importers\\telecom\\kpi_importer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 326, "n_excluded": 0, "n_missing": 326, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_37c60dde8ac853d0_nlg_importer_py": {"hash": "c5f669a6153e6c0ba3a0d3846068007b", "index": {"url": "z_37c60dde8ac853d0_nlg_importer_py.html", "file": "src\\importers\\telecom\\nlg_importer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 562, "n_excluded": 9, "n_missing": 562, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_628569355ef1e7af_csv_health_monitor_py": {"hash": "e7b65a3f45906ba6dc1df811b6e17ab2", "index": {"url": "z_628569355ef1e7af_csv_health_monitor_py.html", "file": "src\\monitoring\\csv_health_monitor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 160, "n_excluded": 22, "n_missing": 113, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6156a86a215061be___init___py": {"hash": "04312b59ba6718c33e47894e1c73c58d", "index": {"url": "z_6156a86a215061be___init___py.html", "file": "src\\utils\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 14, "n_excluded": 0, "n_missing": 14, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6156a86a215061be_cache_manager_py": {"hash": "6912a250cde66cae8639dd4b97d117f7", "index": {"url": "z_6156a86a215061be_cache_manager_py.html", "file": "src\\utils\\cache_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 105, "n_excluded": 0, "n_missing": 105, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6156a86a215061be_column_deduplicator_py": {"hash": "7d9fedfbcd115d6092819888e0c5ac8a", "index": {"url": "z_6156a86a215061be_column_deduplicator_py.html", "file": "src\\utils\\column_deduplicator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 157, "n_excluded": 0, "n_missing": 157, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6156a86a215061be_data_validator_py": {"hash": "4b96423a831bf30af5f92190a2f0ad15", "index": {"url": "z_6156a86a215061be_data_validator_py.html", "file": "src\\utils\\data_validator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 109, "n_excluded": 0, "n_missing": 109, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6156a86a215061be_disk_space_manager_py": {"hash": "a60bb95f2a66e2366ddda3f3dc5862bb", "index": {"url": "z_6156a86a215061be_disk_space_manager_py.html", "file": "src\\utils\\disk_space_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 241, "n_excluded": 0, "n_missing": 241, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6156a86a215061be_file_handler_py": {"hash": "f5be721a6f58802b2161deeb7e75538b", "index": {"url": "z_6156a86a215061be_file_handler_py.html", "file": "src\\utils\\file_handler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 166, "n_excluded": 0, "n_missing": 166, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_721810ebc2c600de___init___py": {"hash": "0a3f2eaba74bd1f69156a4b6abcd079e", "index": {"url": "z_721810ebc2c600de___init___py.html", "file": "src\\validation\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 51, "n_excluded": 0, "n_missing": 51, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_721810ebc2c600de_benchmark_py": {"hash": "f48ce42923a5c5a49e70500be7655084", "index": {"url": "z_721810ebc2c600de_benchmark_py.html", "file": "src\\validation\\benchmark.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 352, "n_excluded": 2, "n_missing": 352, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_721810ebc2c600de_config_py": {"hash": "639ce362c9bc82c301bb8c61f05351c8", "index": {"url": "z_721810ebc2c600de_config_py.html", "file": "src\\validation\\config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 233, "n_excluded": 0, "n_missing": 233, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_721810ebc2c600de_core_py": {"hash": "e5049363927631fdc9cd9316d8fc8c4a", "index": {"url": "z_721810ebc2c600de_core_py.html", "file": "src\\validation\\core.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 182, "n_excluded": 12, "n_missing": 182, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_721810ebc2c600de_deploy_py": {"hash": "80a82bdd51d50e76f3401f7ef6b25029", "index": {"url": "z_721810ebc2c600de_deploy_py.html", "file": "src\\validation\\deploy.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 336, "n_excluded": 3, "n_missing": 336, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_721810ebc2c600de_docs_generator_py": {"hash": "4426ad25fb922456d78ec9a307323437", "index": {"url": "z_721810ebc2c600de_docs_generator_py.html", "file": "src\\validation\\docs_generator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 407, "n_excluded": 2, "n_missing": 407, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_721810ebc2c600de_examples_py": {"hash": "0cd51971164df82da09f90ab3b56a7c7", "index": {"url": "z_721810ebc2c600de_examples_py.html", "file": "src\\validation\\examples.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 121, "n_excluded": 2, "n_missing": 121, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_721810ebc2c600de_exceptions_py": {"hash": "33b9240d1bd8251e1a797f1ae6c2ad63", "index": {"url": "z_721810ebc2c600de_exceptions_py.html", "file": "src\\validation\\exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 99, "n_excluded": 0, "n_missing": 99, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_721810ebc2c600de_factory_py": {"hash": "651c4b6130679e9862050dd207f01ced", "index": {"url": "z_721810ebc2c600de_factory_py.html", "file": "src\\validation\\factory.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 105, "n_excluded": 0, "n_missing": 105, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_721810ebc2c600de_install_py": {"hash": "765e50e008079944e1c5a175f4d951d8", "index": {"url": "z_721810ebc2c600de_install_py.html", "file": "src\\validation\\install.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 423, "n_excluded": 3, "n_missing": 423, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_721810ebc2c600de_monitoring_py": {"hash": "2adff2a6bdac16a490b3d3f374830112", "index": {"url": "z_721810ebc2c600de_monitoring_py.html", "file": "src\\validation\\monitoring.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 302, "n_excluded": 0, "n_missing": 302, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_721810ebc2c600de_rules_py": {"hash": "935aefd9378d6b620ed1de61e3936f86", "index": {"url": "z_721810ebc2c600de_rules_py.html", "file": "src\\validation\\rules.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 90, "n_excluded": 0, "n_missing": 90, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_721810ebc2c600de_utils_py": {"hash": "bd47e19b25aa254c351bf7a2aa7b0eef", "index": {"url": "z_721810ebc2c600de_utils_py.html", "file": "src\\validation\\utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 314, "n_excluded": 0, "n_missing": 314, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_721810ebc2c600de_validators_py": {"hash": "e413e09b5f46144e3a28869148f195d8", "index": {"url": "z_721810ebc2c600de_validators_py.html", "file": "src\\validation\\validators.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 219, "n_excluded": 0, "n_missing": 219, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}