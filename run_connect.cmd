@echo off
REM Connect命令的一劳永逸解决方案 - Windows CMD版本
REM 确保使用正确的Poetry环境执行connect命令
REM 解决 ModuleNotFoundError: No module named 'ijson' 问题

setlocal enabledelayedexpansion

REM 切换到项目根目录
cd /d "%~dp0"

REM Check if Poetry is available
poetry --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Poetry not installed or not in PATH
    echo Please install Poetry: https://python-poetry.org/docs/#installation
    exit /b 1
)

REM Check if pyproject.toml exists
if not exist "pyproject.toml" (
    echo [ERROR] pyproject.toml file not found
    echo Please ensure you are running this script from the project root directory
    exit /b 1
)

REM Display execution information
echo [INFO] Executing with Poetry environment: connect %*
echo [INFO] Project directory: %CD%
echo.

REM 执行connect命令
poetry run connect %*
set exit_code=!errorlevel!

REM If command failed, provide help information
if !exit_code! neq 0 (
    echo.
    echo [TIPS] If you encounter module import errors, try:
    echo   1. poetry install  # Reinstall dependencies
    echo   2. poetry env remove python ^&^& poetry install  # Rebuild environment
    echo   3. poetry run python -c "import ijson; print('i<PERSON><PERSON> OK')"  # Verify ijson
)

exit /b !exit_code!