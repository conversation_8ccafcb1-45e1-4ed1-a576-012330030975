#!/usr/bin/env python3
"""
Test database connection and NLG schema verification
"""
import asyncio
import asyncpg
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def test_db_connection():
    """Test database connection and verify NLG schema."""
    try:
        print("🔍 Testing database connection...")
        
        # Test connection with default parameters from config
        conn = await asyncpg.connect(
            host='localhost',
            port=5432,
            database='connect',
            user='to2',
            password='TO2'
        )
        print('✅ Database connection successful!')
        
        # Check if nlg_to2 schema exists
        schema_exists = await conn.fetchval(
            "SELECT EXISTS(SELECT 1 FROM information_schema.schemata WHERE schema_name = 'nlg_to2')"
        )
        print(f'✅ nlg_to2 schema exists: {schema_exists}')
        
        if not schema_exists:
            print("❌ nlg_to2 schema does not exist! Creating it...")
            await conn.execute("CREATE SCHEMA IF NOT EXISTS nlg_to2")
            print("✅ nlg_to2 schema created successfully")
        
        # List tables in nlg_to2 schema
        tables = await conn.fetch(
            "SELECT table_name FROM information_schema.tables WHERE table_schema = 'nlg_to2' ORDER BY table_name"
        )
        print(f'📊 Tables in nlg_to2 schema: {len(tables)} tables')
        for table in tables:
            table_name = table['table_name']
            # Get row count for each table
            try:
                row_count = await conn.fetchval(f'SELECT COUNT(*) FROM nlg_to2."{table_name}"')
                print(f'  - {table_name} ({row_count} rows)')
            except Exception as e:
                print(f'  - {table_name} (error getting count: {e})')
        
        # Check for tables with nlg_cube_aktuell pattern
        nlg_tables = await conn.fetch(
            "SELECT table_name FROM information_schema.tables WHERE table_schema = 'nlg_to2' AND table_name LIKE 'nlg_cube_aktuell_%'"
        )
        print(f'📊 NLG cube tables: {len(nlg_tables)} tables')
        for table in nlg_tables:
            table_name = table['table_name']
            try:
                row_count = await conn.fetchval(f'SELECT COUNT(*) FROM nlg_to2."{table_name}"')
                print(f'  - {table_name} ({row_count} rows)')
            except Exception as e:
                print(f'  - {table_name} (error: {e})')
        
        # Test permissions
        try:
            await conn.execute("SELECT 1")
            print("✅ Basic SELECT permission verified")
        except Exception as e:
            print(f"❌ SELECT permission issue: {e}")
        
        try:
            # Test if we can create a test table
            await conn.execute("CREATE TABLE IF NOT EXISTS nlg_to2.test_table (id SERIAL PRIMARY KEY)")
            await conn.execute("DROP TABLE IF EXISTS nlg_to2.test_table")
            print("✅ CREATE/DROP table permissions verified")
        except Exception as e:
            print(f"❌ CREATE/DROP permission issue: {e}")
        
        await conn.close()
        print("✅ Database connection test completed successfully")
        
    except Exception as e:
        print(f'❌ Database connection failed: {e}')
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = asyncio.run(test_db_connection())
    sys.exit(0 if success else 1)
