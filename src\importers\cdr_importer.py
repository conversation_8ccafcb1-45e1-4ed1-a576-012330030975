"""CDR (Call Detail Record) data importer.

This module provides functionality for importing and processing CDR data.
"""

import logging
import time
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

import pandas as pd
from shapely.geometry import Point

from .base import AbstractImporter, TelecomImportError, ImportResult, ImportStatus, ImportMetrics

# Monkey patch pandas.read_csv to debug calls
_original_read_csv = pd.read_csv

def debug_read_csv(*args, **kwargs):
    import traceback
    print(f"DEBUG: pandas.read_csv called with args={args[:1]}, kwargs keys={list(kwargs.keys())}")
    print(f"DEBUG: Call stack:")
    for line in traceback.format_stack()[-3:-1]:
        print(f"  {line.strip()}")
    return _original_read_csv(*args, **kwargs)

pd.read_csv = debug_read_csv

# Configure logging
logger = logging.getLogger(__name__)


class CDRImporter(AbstractImporter):
    """Enhanced Call Detail Record data importer with multi-operator support."""

    def __init__(
        self,
        source_path: Union[str, Path] = None,
        config: Optional[Dict[str, Any]] = None,
        db_session=None,
        performance_logger=None,
        operator: str = None,
        batch_size: int = 10000,
        **kwargs,
    ):
        """Initialize CDR importer.

        Args:
            source_path: Path to source data file
            config: Configuration dictionary
            db_session: Database session for direct database operations
            performance_logger: Logger for performance metrics
            operator: Operator name (telefonica, vodafone, telekom). If None, auto-detect from filename
            batch_size: Batch size for processing
            **kwargs: Additional configuration options
        """
        self.name = "CDRImporter"
        self.supported_formats = ["csv", "xlsx", "xls"]
        self.source_path = source_path

        # Auto-detect operator if not provided
        if operator is None and source_path:
            operator = self._detect_operator_from_path(source_path)

        self.operator = operator or 'telefonica'  # Default fallback
        self.batch_size = batch_size

        # Operator-specific configurations
        self.operator_configs = {
            'telefonica': {
                'schema': 'cdr_to2',
                'optional_columns': ["CALLED_NUMBER", "CELL_ID", "SUCCESS_FLAG", "BYTES_UP", "BYTES_DOWN"]
            },
            'vodafone': {
                'schema': 'cdr_vdf',
                'optional_columns': ["MSISDN_B", "LAC_CI", "CALL_RESULT", "DATA_VOL_UP", "DATA_VOL_DOWN"]
            },
            'telekom': {
                'schema': 'cdr_tdg',
                'optional_columns': ["B_NUMBER", "CELL_IDENTITY", "TERMINATION_CAUSE", "VOLUME_UP", "VOLUME_DOWN"]
            }
        }

        # Get operator-specific configuration
        self.operator_config = self.operator_configs.get(operator, self.operator_configs['telefonica'])

        self.config = config or {}
        self.db_session = db_session
        self.performance_logger = performance_logger

        # Initialize database components if session provided
        if db_session:
            from src.database.operations.bulk_operations import BulkOperations
            from src.database.schema import SchemaManager

            self.schema_manager = SchemaManager(db_session)
            self.bulk_operations = BulkOperations(db_session)

        # Database context components (for unified manager integration)
        self.pool_manager = None
        self.db_manager = None

        # Prepare config for AbstractImporter
        importer_config = {
            'name': self.name,
            'data_type': 'cdr',
            'supported_formats': self.supported_formats,
            'batch_size': self.config.get('batch_size', 10000),
            **kwargs
        }
        super().__init__(config=importer_config)

    def _detect_operator_from_path(self, file_path: Union[str, Path]) -> str:
        """Auto-detect operator from file path or filename using OperatorDetector.

        Args:
            file_path: Path to the CDR file

        Returns:
            str: Detected operator name (telefonica, vodafone, telekom)
        """
        from src.database.utils.table_naming import OperatorDetector
        
        detector = OperatorDetector(self.config)
        
        # Try to detect from filename first
        filename = Path(file_path).name
        operator = detector.detect_operator_from_sheet(filename)
        
        if operator:
            logger.info(f"Detected {operator} operator from filename: {filename}")
            return operator
        
        # Try to detect from directory structure
        path_parts = Path(file_path).parts
        for part in path_parts:
            operator = detector.detect_operator_from_sheet(part)
            if operator:
                logger.info(f"Detected {operator} operator from directory: {part}")
                return operator

        logger.warning(f"Could not detect operator from path: {file_path}, defaulting to telefonica")
        return 'telefonica'

    def get_operator_schema(self) -> str:
        """Get the database schema for the current operator.

        Returns:
            str: Schema name for the operator
        """
        return self.operator_configs.get(self.operator, {}).get('schema', 'cdr_to2')

    def get_table_name(self, file_path: Union[str, Path], sheet_name: str = None) -> str:
        """Generate table name for CDR data following pattern: cdr_{year}Q{quarter}_{service_type}.

        Args:
            file_path: Path to the source file
            sheet_name: Optional sheet name for Excel files

        Returns:
            str: Generated table name
        """
        from src.database.utils.table_naming import TableNamingManager
        
        # Create a basic config for CDR table naming
        config = {
            'telecom_data_sources': {
                'cdr': {
                    'table_name_pattern': 'cdr_{year}Q{quarter}_{service_type}'
                }
            }
        }
        
        naming_manager = TableNamingManager(config)
        return naming_manager.generate_table_name(
            data_type='cdr',
            file_path=Path(file_path),
            operator=None,  # Don't include operator in table name
            sheet_name=sheet_name
        )
    
    def get_schema_for_sheet(self, sheet_name: str) -> str:
        """Get database schema based on Excel sheet name for CDR data.
        
        Args:
            sheet_name: Name of the Excel sheet
            
        Returns:
            str: Database schema name
        """
        from src.database.utils.table_naming import OperatorDetector
        
        # Create detector with config
        detector = OperatorDetector(self.config)
        operator = detector.detect_operator_from_sheet(sheet_name)
        return detector.get_schema_for_operator(operator, 'cdr')

    def detect_operator_from_content(self, data: pd.DataFrame) -> str:
        """Detect operator from data content (column names, values) using OperatorDetector.

        Args:
            data: DataFrame with CDR data

        Returns:
            str: Detected operator name
        """
        from src.database.utils.table_naming import OperatorDetector
        
        detector = OperatorDetector(self.config)
        columns_lower = [col.lower() for col in data.columns]

        # Define operator-specific column patterns
        operator_patterns = {
            'telefonica': ['numero_origen', 'numero_destino', 'celda_id', 'latitud', 'longitud'],
            'vodafone': ['a_number', 'b_number', 'cell_id', 'lat', 'lon', 'msisdn_b'],
            'telekom': ['b_number', 'cell_identity', 'termination_cause', 'volume_up', 'volume_down']
        }

        # Check each operator's patterns
        for operator, indicators in operator_patterns.items():
            if any(indicator in columns_lower for indicator in indicators):
                logger.info(f"Detected {operator} operator from column names")
                return operator

        # If no specific patterns found, keep current operator
        logger.info(f"Could not detect operator from content, keeping current: {self.operator}")
        return self.operator

    def _clean_column_name(self, col_name: str) -> str:
        """Clean and standardize column names using unified rules.

        Args:
            col_name: Original column name

        Returns:
            str: Cleaned column name
        """
        import re
        import unicodedata

        # Convert to string and strip whitespace
        clean_name = str(col_name).strip()

        # Normalize unicode characters (handle German, Spanish, etc.)
        clean_name = unicodedata.normalize('NFKD', clean_name)
        clean_name = clean_name.encode('ascii', 'ignore').decode('ascii')

        # Convert to lowercase
        clean_name = clean_name.lower()

        # Replace spaces, hyphens, and other special chars with underscores
        clean_name = re.sub(r'[^a-zA-Z0-9_]', '_', clean_name)

        # Replace multiple underscores with single underscore
        clean_name = re.sub(r'_+', '_', clean_name)

        # Remove leading/trailing underscores
        clean_name = clean_name.strip('_')

        # Ensure it doesn't start with a number
        if clean_name and clean_name[0].isdigit():
            clean_name = f'col_{clean_name}'

        # Ensure minimum length
        if not clean_name:
            clean_name = 'unnamed_column'

        return clean_name

    def set_database_context(self, **kwargs):
        """Set database context components for unified manager integration."""
        self.pool_manager = kwargs.get('pool')
        self.db_manager = kwargs.get('db_manager')
        if 'schema_manager' in kwargs:
            self.schema_manager = kwargs['schema_manager']

    async def import_data(self, file_path=None, **kwargs) -> ImportResult:
        """Import CDR data with multi-operator Excel support."""

        import time
        start_time = time.time()
        results = []

        try:
            if file_path:
                self.source_path = Path(file_path)
            if not self.source_path:
                raise ValueError("No source path specified for CDR import")

            if self.source_path.suffix.lower() not in ['.xlsx', '.xls']:
                # For non-Excel, use existing logic
                data = await self._read_data_async()
                if data.empty:
                    return ImportResult(status=ImportStatus.FAILED, error_message="No data found")
                processed_data = self._process_operator_data(data)
                storage_result = await self._store_data(processed_data, self.get_operator_schema())
                results.append(storage_result)
            else:
                # Excel handling with multi-sheets using OperatorDetector
                from src.database.utils.table_naming import OperatorDetector
                
                excel_file = pd.ExcelFile(self.source_path)
                sheet_names = excel_file.sheet_names
                table_name = self.get_table_name(str(self.source_path))
                detector = OperatorDetector(self.config)

                for sheet in sheet_names:
                    # Use OperatorDetector to identify operator from sheet name
                    operator = detector.detect_operator_from_sheet(sheet)
                    if not operator:
                        logger.debug(f"Skipping sheet '{sheet}' - no operator detected")
                        continue

                    # Get schema for detected operator
                    schema = detector.get_schema_for_operator(operator, 'cdr')
                    
                    data = pd.read_excel(excel_file, sheet_name=sheet)
                    if data.empty:
                        logger.warning(f"Sheet '{sheet}' is empty, skipping")
                        continue

                    validation = self._validate_cdr_data(data)
                    if not validation['is_valid']:
                        logger.warning(f"Data validation failed for sheet '{sheet}', skipping")
                        continue

                    processed_data = self._process_operator_data(data)
                    storage_result = await self._store_data(processed_data, schema, table_name)
                    results.append({
                        'operator': operator, 
                        'sheet': sheet, 
                        'records': len(data), 
                        'schema': schema,
                        'table_name': table_name
                    })
                    logger.info(f"Processed sheet '{sheet}' for operator '{operator}' into schema '{schema}'")

            end_time = time.time()
            total_records = sum(r['records'] for r in results if 'records' in r)
            return ImportResult(
                status=ImportStatus.COMPLETED,
                metadata={'results': results, 'message': "Multi-operator CDR import completed", 'records_imported': total_records}
            )

        except Exception as e:
            return ImportResult(status=ImportStatus.FAILED, error_message=str(e))

    def _detect_csv_structure(self, file_path: Path) -> dict:
        """Detect CSV file structure including comments, headers, and delimiters."""
        import csv

        structure = {
            'skip_rows': 0,
            'header_row': 0,
            'delimiter': ',',
            'encoding': 'utf-8',
            'comment_char': None
        }

        # Try different encodings
        encodings = ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252', 'iso-8859-1']

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    lines = []
                    for i, line in enumerate(f):
                        lines.append(line.strip())
                        if i >= 20:  # Read first 20 lines for analysis
                            break

                structure['encoding'] = encoding
                break
            except UnicodeDecodeError:
                continue

        if not lines:
            return structure

        # Detect comment lines and skip rows
        skip_rows = 0
        for i, line in enumerate(lines):
            if line.startswith('#') or line.startswith('//') or not line.strip():
                skip_rows += 1
            else:
                break

        structure['skip_rows'] = skip_rows

        # Find header row (first non-comment, non-empty line)
        header_line = None
        for i in range(skip_rows, len(lines)):
            if lines[i].strip():
                header_line = lines[i]
                break

        if header_line:
            # Detect delimiter
            sniffer = csv.Sniffer()
            try:
                dialect = sniffer.sniff(header_line, delimiters=',;\t|:')
                structure['delimiter'] = dialect.delimiter
            except:
                # Fallback delimiter detection
                delimiters = [',', ';', '\t', '|', ':']
                delimiter_counts = {}
                for delim in delimiters:
                    delimiter_counts[delim] = header_line.count(delim)

                if delimiter_counts:
                    structure['delimiter'] = max(delimiter_counts, key=delimiter_counts.get)

        return structure

    async def _read_data_async(self) -> pd.DataFrame:
        """Read data from source file asynchronously with intelligent parsing."""

        import asyncio
        from pathlib import Path

        def read_file():
            source_path = Path(self.source_path)
            print(f"DEBUG: _read_data_async.read_file called for {source_path}")

            if source_path.suffix.lower() == '.csv':
                # Use configuration-driven CSV structure detection
                structure = self._detect_csv_structure_with_config(source_path, 'cdr')
                self.logger.info(f"Using CDR CSV structure: {structure}")

                # Enhanced CSV reading with configuration-based structure detection
                import csv
                try:
                    result = pd.read_csv(
                        source_path,
                        encoding=structure['encoding'],
                        delimiter=structure['delimiter'],
                        skiprows=structure['skip_rows'],
                        header=structure['header_row'],  # Use configured header row
                        engine='python',  # More flexible parser
                        on_bad_lines='skip',  # Skip problematic lines
                        quoting=csv.QUOTE_MINIMAL,
                        skipinitialspace=True,
                        comment='#',  # Skip lines starting with #
                        dtype=str,              # Read all columns as strings initially
                        na_values=['', 'NULL', 'null', 'N/A', 'n/a', 'NA', 'na'],
                        keep_default_na=True
                    )
                    self.logger.info(f"Successfully read {len(result)} rows from CDR CSV")
                    return result
                except (UnicodeDecodeError, pd.errors.ParserError) as e:
                    logger.warning(f"Standard CSV reading failed: {e}, trying fallback options")
                    # Fallback with more lenient parsing
                    encodings = ['utf-8-sig', 'latin-1', 'cp1252', 'iso-8859-1']
                    for encoding in encodings:
                        try:
                            return pd.read_csv(
                                source_path,
                                encoding=encoding,
                                delimiter=structure['delimiter'],
                                skiprows=structure['skip_rows'],
                                engine='python',
                                on_bad_lines='skip',
                                quoting=csv.QUOTE_NONE,
                                skipinitialspace=True,
                                low_memory=False,
                                comment='#',
                                dtype=str,              # Read all as strings
                                na_values=['', 'NULL', 'null', 'N/A', 'n/a', 'NA', 'na'],
                                keep_default_na=True
                            )
                        except Exception:
                            continue
                    raise ValueError(f"Could not read CSV file with any supported encoding: {source_path}")
            elif source_path.suffix.lower() in ['.xlsx', '.xls']:
                # For Excel files, use enhanced sheet detection
                excel_file = pd.ExcelFile(source_path)
                sheet_names = excel_file.sheet_names

                # Use enhanced sheet detection
                target_sheet, detected_operator = self._detect_operator_sheet(sheet_names, self.operator)

                # Update operator if detected from sheet analysis
                if detected_operator and detected_operator != self.operator:
                    self.logger.info(f"Updated operator from sheet analysis: {self.operator} -> {detected_operator}")
                    self.operator = detected_operator

                self.logger.info(f"Selected sheet: {target_sheet} for operator: {self.operator}")
                return pd.read_excel(source_path, sheet_name=target_sheet)
            else:
                raise ValueError(f"Unsupported file format: {source_path.suffix}")

        # Run file reading in thread pool to avoid blocking
        loop = asyncio.get_event_loop()
        data = await loop.run_in_executor(None, read_file)

        logger.info(f"Read {len(data)} records from {self.source_path}")
        return data

    def _detect_operator_sheet(self, sheet_names: List[str], current_operator: str) -> Tuple[str, str]:
        """
        Enhanced operator and sheet detection for CDR Excel files using OperatorDetector.

        Args:
            sheet_names: List of sheet names in the Excel file
            current_operator: Currently detected operator

        Returns:
            Tuple of (selected_sheet_name, detected_operator)
        """
        from ..database.utils.table_naming import OperatorDetector
        
        detector = OperatorDetector(self.config)
        
        # Score each sheet for operator matching
        sheet_scores = {}
        detected_operator = current_operator

        for sheet_name in sheet_names:
            sheet_lower = sheet_name.lower()
            
            # Skip obviously non-data sheets
            if any(skip_word in sheet_lower for skip_word in ['summary', 'statistics', 'info', 'readme', 'legend']):
                continue

            # Use OperatorDetector to detect operator from sheet name
            sheet_operator = detector.detect_operator_from_sheet(sheet_name)
            
            if sheet_operator:
                # Score based on operator detection confidence
                score = 10 if sheet_operator in sheet_lower else 5
                sheet_scores[sheet_name] = (score, sheet_operator)
            else:
                sheet_scores[sheet_name] = (0, None)

        # Find the best matching sheet
        best_sheet = None
        best_score = 0

        for sheet_name, (score, operator) in sheet_scores.items():
            if score > best_score:
                best_score = score
                best_sheet = sheet_name
                if operator:
                    detected_operator = operator

        # If no operator-specific sheet found, try current operator
        if not best_sheet and current_operator:
            for sheet_name in sheet_names:
                sheet_operator = detector.detect_operator_from_sheet(sheet_name)
                if sheet_operator == current_operator:
                    best_sheet = sheet_name
                    break

        # Fallback to first data sheet (non-summary)
        if not best_sheet:
            for sheet_name in sheet_names:
                sheet_lower = sheet_name.lower()
                if not any(skip_word in sheet_lower for skip_word in ['summary', 'statistics', 'info', 'readme']):
                    best_sheet = sheet_name
                    break

        # Final fallback to first sheet
        if not best_sheet:
            best_sheet = sheet_names[0]

        self.logger.info(f"Sheet detection results: {dict(sheet_scores)}")
        self.logger.info(f"Selected sheet: {best_sheet}, detected operator: {detected_operator}")

        return best_sheet, detected_operator

    def detect_all_operator_sheets(self, sheet_names: List[str]) -> Dict[str, str]:
        """
        Detect all operator sheets in an Excel file for multi-operator processing using OperatorDetector.

        Args:
            sheet_names: List of sheet names in the Excel file

        Returns:
            Dictionary mapping operator to sheet name
        """
        from ..database.utils.table_naming import OperatorDetector
        
        detector = OperatorDetector(self.config)
        detected_sheets = {}

        for sheet_name in sheet_names:
            sheet_lower = sheet_name.lower()

            # Skip obviously non-data sheets
            if any(skip_word in sheet_lower for skip_word in ['summary', 'statistics', 'info', 'readme', 'legend']):
                continue

            # Use OperatorDetector to detect operator from sheet name
            operator = detector.detect_operator_from_sheet(sheet_name)
            
            if operator:
                detected_sheets[operator] = sheet_name
                self.logger.info(f"Detected {operator} sheet: {sheet_name}")

        return detected_sheets

    def _validate_cdr_data(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Validate CDR data quality and completeness."""

        validation_result = {
            'is_valid': True,
            'errors': [],
            'warnings': []
        }

        # Skip required columns validation - accept any column structure
        # Data completeness check is optional and informational only

        return validation_result

    def _process_operator_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Process data with operator-specific transformations."""

        processed_data = data.copy()

        # Operator-specific column mapping
        if self.operator == 'vodafone':
            column_mapping = {
                'RECORD_ID': 'CALL_ID',
                'MSISDN_A': 'CALLER_NUMBER',
                'MSISDN_B': 'CALLED_NUMBER',
                'CALL_START': 'CALL_START_TIME',
                'CALL_DURATION': 'DURATION'
            }
            processed_data = processed_data.rename(columns=column_mapping)

        elif self.operator == 'telekom':
            column_mapping = {
                'CDR_ID': 'CALL_ID',
                'A_NUMBER': 'CALLER_NUMBER',
                'B_NUMBER': 'CALLED_NUMBER',
                'TIMESTAMP_START': 'CALL_START_TIME',
                'CALL_LENGTH': 'DURATION'
            }
            processed_data = processed_data.rename(columns=column_mapping)

        # Add operator metadata
        processed_data['OPERATOR'] = self.operator
        processed_data['IMPORT_TIMESTAMP'] = pd.Timestamp.now()

        return processed_data

    async def _mock_store_data(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Mock data storage for testing purposes."""

        # Simulate storage delay
        import asyncio
        await asyncio.sleep(0.1)

        return {
            'records_stored': len(data),
            'target_schema': self.operator_config['schema'],
            'storage_method': 'mock',
            'batch_count': (len(data) + self.batch_size - 1) // self.batch_size
        }

        super().__init__(source_path=kwargs.get("source_path", ""), **kwargs)

    def validate_file(self, file_path: Union[str, Path]) -> bool:
        """Validate CDR data file.

        Args:
            file_path: Path to the file

        Returns:
            bool: True if file is valid

        Raises:
            ImportError: If file is invalid
        """
        path = Path(file_path)

        if not path.exists():
            raise ImportError(f"File does not exist: {path}")

        if path.suffix.lower() not in [".csv", ".xlsx", ".xls"]:
            raise ImportError(f"Unsupported file format: {path.suffix}")

        return True

    def validate_data_structure(self, data: pd.DataFrame) -> Tuple[bool, List[str]]:
        """Validate CDR data structure.

        Args:
            data: DataFrame to validate

        Returns:
            Tuple[bool, List[str]]: Validation result and error messages
        """
        errors = []

        # Accept any column structure - no required columns validation
        if data.empty:
            errors.append("Data file is empty")

        return len(errors) == 0, errors

    def validate_data_values(self, data: pd.DataFrame) -> Tuple[bool, List[str]]:
        """Validate CDR data values.

        Args:
            data: DataFrame to validate

        Returns:
            Tuple[bool, List[str]]: Validation result and error messages
        """
        errors = []

        # Skip required columns validation - accept any data structure

        # Validate call duration if present
        if "CALL_DURATION" in data.columns:
            negative_duration = (data["CALL_DURATION"] < 0).sum()
            if negative_duration > 0:
                errors.append(
                    f"Found {negative_duration} negative call duration values"
                )

        # Validate call times if both start and end times are present
        if "CALL_START_TIME" in data.columns and "CALL_END_TIME" in data.columns:
            # Convert to datetime if not already
            if not pd.api.types.is_datetime64_dtype(data["CALL_START_TIME"]):
                data["CALL_START_TIME"] = pd.to_datetime(
                    data["CALL_START_TIME"], errors="coerce"
                )

            if not pd.api.types.is_datetime64_dtype(data["CALL_END_TIME"]):
                data["CALL_END_TIME"] = pd.to_datetime(
                    data["CALL_END_TIME"], errors="coerce"
                )

            # Check for end time before start time
            invalid_times = (
                (data["CALL_END_TIME"] < data["CALL_START_TIME"])
                & data["CALL_START_TIME"].notnull()
                & data["CALL_END_TIME"].notnull()
            ).sum()

            if invalid_times > 0:
                errors.append(
                    f"Found {invalid_times} records where call end time is before start time"
                )

        return len(errors) == 0, errors

    def transform_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Transform CDR data.

        Args:
            data: DataFrame to transform

        Returns:
            pd.DataFrame: Transformed data
        """
        # Create a copy to avoid modifying the original
        transformed = data.copy()

        # Remove duplicate columns and standardize column names
        from src.utils.column_deduplicator import ColumnDeduplicator
        
        # Remove duplicate columns, keeping the most complete one
        transformed, dedup_report = ColumnDeduplicator.remove_duplicate_columns(
            transformed, keep_strategy='best'
        )
        
        if dedup_report['total_removed'] > 0:
            logger.info(f"Removed {dedup_report['total_removed']} duplicate columns in CDR data")
        
        # Standardize column names to lowercase
        new_columns = []
        for col in transformed.columns:
            clean_col = col.lower() if isinstance(col, str) else str(col).lower()
            new_columns.append(clean_col)
        
        transformed.columns = new_columns

        # Convert call times to datetime if needed
        datetime_columns = ["call_start_time", "call_end_time"]
        for col in datetime_columns:
            if col in transformed.columns and not pd.api.types.is_datetime64_dtype(
                transformed[col]
            ):
                transformed[col] = pd.to_datetime(transformed[col], errors="coerce")

        # Calculate call duration if not present but start and end times are available
        if (
            "call_duration" not in transformed.columns
            and "call_start_time" in transformed.columns
            and "call_end_time" in transformed.columns
        ):
            transformed["call_duration"] = (
                transformed["call_end_time"] - transformed["call_start_time"]
            ).dt.total_seconds()

        # Add geometry column if cell tower coordinates are available
        if (
            "cell_tower_lat" in transformed.columns
            and "cell_tower_lon" in transformed.columns
        ):
            transformed["geometry"] = transformed.apply(
                lambda row: Point(row["cell_tower_lon"], row["cell_tower_lat"])
                if pd.notnull(row["cell_tower_lon"])
                and pd.notnull(row["cell_tower_lat"])
                else None,
                axis=1,
            )

        # Add import timestamp
        transformed["import_timestamp"] = pd.Timestamp.now()

        return transformed

    async def _validate_cdr_data_async(self, data: pd.DataFrame) -> pd.DataFrame:
        """Asynchronously validate CDR data with telecommunications-specific checks."""
        # Remove duplicates based on call_id
        if 'call_id' in data.columns:
            data = data.drop_duplicates(subset=['call_id'])

        # Validate call duration
        if 'call_duration' in data.columns:
            data = data[data['call_duration'] >= 0]

        # Validate phone numbers format (basic check)
        if 'caller_number' in data.columns:
            data = data[data['caller_number'].str.len() >= 7]

        return data

    async def _transform_data_async(self, data: pd.DataFrame) -> pd.DataFrame:
        """Asynchronously transform CDR data with geospatial enhancement."""
        transformed = self.transform_data(data)

        # Add geospatial enhancements if coordinates are available
        if 'cell_tower_lat' in transformed.columns and 'cell_tower_lon' in transformed.columns:
            from shapely.geometry import Point
            transformed['geometry'] = transformed.apply(
                lambda row: Point(row['cell_tower_lon'], row['cell_tower_lat'])
                if pd.notna(row['cell_tower_lat']) and pd.notna(row['cell_tower_lon'])
                else None, axis=1
            )

        return transformed

    async def _calculate_cdr_kpis(self, data: pd.DataFrame) -> Optional[pd.DataFrame]:
        """Calculate KPIs from CDR data."""
        if data.empty:
            return None

        try:
            # Calculate basic KPIs
            kpis = []

            # Call success rate by cell
            if 'cell_id' in data.columns and 'call_status' in data.columns:
                cell_kpis = data.groupby('cell_id').agg({
                    'call_id': 'count',
                    'call_status': lambda x: (x == 'completed').sum(),
                    'call_duration': 'mean'
                }).reset_index()

                cell_kpis['success_rate'] = (cell_kpis['call_status'] / cell_kpis['call_id']) * 100
                cell_kpis['kpi_type'] = 'call_success_rate'
                cell_kpis['calculation_time'] = pd.Timestamp.now()

                kpis.append(cell_kpis)

            if kpis:
                return pd.concat(kpis, ignore_index=True)

        except Exception as e:
            logger.warning(f"KPI calculation failed: {e}")

        return None

    async def _ensure_database_connection(self):
        """Ensure database connection is available."""
        if not hasattr(self, 'bulk_operations') or not self.bulk_operations:
            try:
                from src.database.bulk_operations import BulkOperations
                from src.database.connection import DatabaseConnection
                from src.database.schema_manager import SchemaManager
                
                # Initialize database connection
                db_connection = DatabaseConnection()
                self.bulk_operations = BulkOperations(db_connection)
                
                # Initialize schema manager if not available
                if not hasattr(self, 'schema_manager') or not self.schema_manager:
                    self.schema_manager = SchemaManager(db_connection)
                
                logger.info("Database connection and schema manager initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize database connection: {e}")
                # Create mock operations for testing
                self.bulk_operations = None
                self.schema_manager = None
    
    async def _ensure_table_exists(self, table_name: str, schema: str, columns: list):
        """Ensure table exists in database."""
        try:
            # Always create table using create_cdr_table method
            await self.create_cdr_table(table_name, columns, schema)
            logger.info(f"Table connect.{schema}.{table_name} ready")
        except Exception as e:
            logger.error(f"Failed to create table connect.{schema}.{table_name}: {e}")
            raise
    
    async def _process_batch_with_db_insert(self, batch: pd.DataFrame, schema: str, table_name: str) -> ImportResult:
        """Process batch with actual database insertion."""
        import time
        start_time = time.time()
        
        try:
            # Transform data
            transformed_data = self.transform_data(batch)
            
            # Insert into database
            if hasattr(self, 'bulk_operations') and self.bulk_operations:
                await self._bulk_insert_async(transformed_data, table_name, schema)
                logger.debug(f"Inserted {len(transformed_data)} records to {schema}.{table_name}")
            else:
                # Mock insertion for testing
                logger.info(f"Mock insert: {len(transformed_data)} records to {schema}.{table_name}")
            
            processing_time = time.time() - start_time
            
            # Create metrics
            metrics = ImportMetrics(
                records_processed=len(transformed_data),
                records_failed=0,
                processing_time_seconds=processing_time
            )
            
            return ImportResult(
                status=ImportStatus.COMPLETED,
                metrics=metrics
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"Batch processing failed: {e}")
            
            metrics = ImportMetrics(
                records_processed=0,
                records_failed=len(batch),
                processing_time_seconds=processing_time
            )
            
            return ImportResult(
                status=ImportStatus.FAILED,
                error_message=str(e),
                metrics=metrics
            )
    
    async def _bulk_insert_async(self, data: pd.DataFrame, table_name: str, schema: str):
        """Asynchronously insert data using bulk operations."""
        if hasattr(self, "bulk_operations") and self.bulk_operations:
            try:
                # Use existing bulk operations with correct schema
                self.bulk_operations.bulk_insert_dataframe(data, table_name, schema=schema)
                logger.info(f"Successfully inserted {len(data)} records to connect.{schema}.{table_name}")
            except Exception as e:
                logger.error(f"Failed to insert data to connect.{schema}.{table_name}: {e}")
                raise
        else:
            logger.warning(f"Bulk operations not available for table connect.{schema}.{table_name}")

    async def _create_cdr_table_async(self, table_name: str, data_columns: List[str] = None):
        """Asynchronously create CDR table."""
        # Use the updated async method
        await self.create_cdr_table(table_name, data_columns)

    async def process_batch_async(self, data: pd.DataFrame, batch_id: str = None) -> ImportResult:
        """Process a batch of CDR data asynchronously with telecommunications optimizations.

        Args:
            data: DataFrame containing CDR data to process
            batch_id: Optional batch identifier for tracking

        Returns:
            ImportResult: Result of the batch processing
        """
        import time
        start_time = time.time()

        try:
            logger.info(f"Processing CDR batch {batch_id}: {len(data)} records")

            # Enhanced data validation for telecommunications
            validated_data = await self._validate_cdr_data_async(data)

            # Transform data with geospatial enhancement
            transformed_data = await self._transform_data_async(validated_data)

            # Calculate telecommunications-specific KPIs
            kpi_data = await self._calculate_cdr_kpis(transformed_data)

            # Insert into database if session available
            if hasattr(self, "bulk_operations"):
                table_name = self.config.get("cdr_table_name", "call_detail_records")

                # Create table if it doesn't exist
                target_schema = self.operator_configs.get(self.operator, {}).get('schema', 'cdr_to2')
                if not await self.schema_manager.table_exists(table_name, target_schema):
                    # Ensure schema exists first
                    await self.schema_manager.ensure_schema_exists(target_schema)
                    await self.create_cdr_table(table_name, list(transformed_data.columns), target_schema)

                # Insert data using async bulk operations
                await self._bulk_insert_async(transformed_data, table_name, batch_id)

                # Insert KPI data if available
                if kpi_data is not None:
                    kpi_table = self.config.get("cdr_kpi_table", "cdr_kpis")
                    await self._bulk_insert_async(kpi_data, kpi_table, batch_id)

            processing_time = time.time() - start_time

            # Create metrics object
            metrics = ImportMetrics(
                start_time=datetime.fromtimestamp(start_time),
                end_time=datetime.now(),
                records_processed=len(transformed_data),
                processing_time_seconds=processing_time,
                throughput_records_per_second=len(transformed_data) / processing_time if processing_time > 0 else 0
            )

            return ImportResult(
                status=ImportStatus.COMPLETED,
                data=transformed_data,
                metrics=metrics,
                metadata={
                    "batch_id": batch_id,
                    "columns": list(transformed_data.columns),
                    "has_geometry": "geometry" in transformed_data.columns,
                    "kpi_calculated": kpi_data is not None,
                    "processing_time_seconds": processing_time,
                },
            )

        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"CDR batch processing failed for {batch_id}: {e}")

            # Create metrics object for failed operation
            metrics = ImportMetrics(
                start_time=datetime.fromtimestamp(start_time),
                end_time=datetime.now(),
                records_processed=0,
                processing_time_seconds=processing_time,
                errors=[str(e)]
            )

            return ImportResult(
                status=ImportStatus.FAILED,
                error_message=str(e),
                metrics=metrics,
                metadata={
                    "batch_id": batch_id,
                    "processing_time_seconds": processing_time,
                }
            )

    def process_batch(self, data: pd.DataFrame) -> ImportResult:
        """Process a batch of CDR data (synchronous wrapper for backward compatibility).

        Args:
            data: DataFrame to process

        Returns:
            ImportResult: Result of the batch processing
        """
        import asyncio

        # Run async version for enhanced processing
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If we're already in an event loop, create a task
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, self.process_batch_async(data))
                    return future.result()
            else:
                return loop.run_until_complete(self.process_batch_async(data))
        except RuntimeError:
            # No event loop running, create new one
            return asyncio.run(self.process_batch_async(data))

    async def create_cdr_table(self, schema_or_table: str, data_columns: List[str] = None, target_schema: str = None) -> None:
        """Create CDR table in database with dynamic schema.

        Args:
            schema_or_table: Schema name (legacy) or table name
            data_columns: List of data column names from the file
            target_schema: Target schema name (e.g., cdr_to2, cdr_vdf, cdr_tdg)
        """
        from src.database.schema import ColumnSchema, TableSchema
        import re

        # Handle legacy usage where first parameter was schema
        if target_schema:
            # New usage: schema_or_table is table name, target_schema is schema
            table_name = schema_or_table
            schema_name = target_schema
        else:
            # Legacy usage: schema_or_table is schema name
            table_name = "call_detail_records"
            schema_name = schema_or_table

        # Base columns with primary key and timestamp
        columns = [
            ColumnSchema(name="id", data_type="BIGSERIAL", primary_key=True),
            ColumnSchema(name="created_at", data_type="TIMESTAMP", default="CURRENT_TIMESTAMP"),
        ]

        # Add data columns as TEXT type (following PRD requirements)
        if data_columns:
            for col_name in data_columns:
                # Use unified column name cleaning
                clean_name = self._clean_column_name(col_name)

                if clean_name and clean_name not in ['id', 'created_at']:
                    columns.append(ColumnSchema(name=clean_name, data_type="TEXT"))

        # Define table schema
        table_schema = TableSchema(
            name=table_name,
            schema=schema_name,  # Set the schema name
            columns=columns,
            indexes=[],  # Simplified - no indexes for now to avoid complexity
        )

        # Create table (would use schema manager if available)
        if hasattr(self, 'schema_manager') and self.schema_manager:
            await self.schema_manager.create_table(table_schema)
        else:
            logger.info(f"Table schema created for {table_name}: {len(table_schema.columns)} columns")

    async def import_data(self, file_path=None, **kwargs) -> ImportResult:
        """Import CDR data from file with multi-sheet support and progress tracking.

        Args:
            file_path: Path to the file (can be positional or keyword argument)
            **kwargs: Additional import options
                - batch_size: Number of records per batch

        Returns:
            ImportResult: Result of the import operation
        """
        file_path = file_path or kwargs.get("file_path") or self.source_path
        batch_size = kwargs.get("batch_size", 10000)
        
        import time
        start_time = time.time()

        try:
            # Validate file
            self.validate_file(file_path)
            path = Path(file_path)
            
            print(f"\nImporting: {path.name} → connect database")
            print("="*50)

            # Start performance monitoring if available
            if self.performance_logger:
                self.performance_logger.start_operation("cdr_import")

            # Handle different file formats
            if path.suffix.lower() == ".csv":
                return await self._import_csv_data(file_path, batch_size, start_time)
            elif path.suffix.lower() in [".xlsx", ".xls"]:
                return await self._import_excel_multi_sheets(file_path, batch_size, start_time)
            else:
                raise ImportError(f"Unsupported file format: {path.suffix}")

        except Exception as e:
            logger.error(f"CDR import error: {str(e)}", exc_info=True)
            
            # Create metrics for failed import
            metrics = ImportMetrics(
                records_processed=0,
                records_failed=0,
                processing_time_seconds=time.time() - start_time
            )
            
            return ImportResult(
                status=ImportStatus.FAILED,
                source_info={'source_path': str(Path(file_path)) if file_path else None},
                error_message=f"Import failed: {str(e)}",
                metrics=metrics
            )
    
    async def _import_csv_data(self, file_path: str, batch_size: int, start_time: float) -> ImportResult:
        """Import data from CSV file."""
        path = Path(file_path)
        
        # Use configuration-driven CSV structure detection
        structure = self._detect_csv_structure_with_config(path, 'cdr')

        # Enhanced CSV reading with configuration-based structure detection
        import csv
        try:
            df = pd.read_csv(
                file_path,
                encoding=structure['encoding'],
                delimiter=structure['delimiter'],
                skiprows=structure['skip_rows'],
                header=structure['header_row'],
                engine='python',
                on_bad_lines='skip',
                quoting=csv.QUOTE_MINIMAL,
                skipinitialspace=True,
                comment='#',
                dtype=str,
                na_values=['', 'NULL', 'null', 'N/A', 'n/a', 'NA', 'na'],
                keep_default_na=True
            )
        except (UnicodeDecodeError, pd.errors.ParserError) as e:
            self.logger.warning(f"Standard CSV reading failed: {e}, trying with fallback options")
            # Fallback with different encodings
            encodings = ['utf-8-sig', 'latin-1', 'cp1252', 'iso-8859-1']
            df = None
            for encoding in encodings:
                try:
                    df = pd.read_csv(
                        file_path,
                        encoding=encoding,
                        delimiter=structure['delimiter'],
                        skiprows=structure['skip_rows'],
                        engine='python',
                        on_bad_lines='skip',
                        quoting=csv.QUOTE_NONE,
                        skipinitialspace=True,
                        low_memory=False,
                        comment='#',
                        dtype=str,
                        na_values=['', 'NULL', 'null', 'N/A', 'n/a', 'NA', 'na'],
                        keep_default_na=True
                    )
                    self.logger.info(f"Successfully read CSV with encoding: {encoding}")
                    break
                except Exception:
                    continue
            if df is None:
                raise ImportError(f"Could not read CSV file with any supported encoding: {file_path}")
        
        # Process single CSV file
        return await self._process_single_sheet(df, "CSV_Data", "csv", path, batch_size, start_time, "cdr_to2", "call_detail_records")
    
    async def _import_excel_multi_sheets(self, file_path: str, batch_size: int, start_time: float) -> ImportResult:
        """Import data from Excel file with multi-sheet support."""
        path = Path(file_path)
        
        # Try different engines for Excel files
        try:
            excel_file = pd.ExcelFile(file_path, engine='openpyxl')
        except Exception:
            try:
                excel_file = pd.ExcelFile(file_path, engine='xlrd')
            except Exception:
                excel_file = pd.ExcelFile(file_path)
        
        sheet_names = excel_file.sheet_names
        
        print(f"Found {len(sheet_names)} sheets: {', '.join(sheet_names)}")
        
        # Try to use OperatorDetector for operator mappings, fallback to hardcoded if not available
        try:
            from ..utils.operator_detector import OperatorDetector
            operator_detector = OperatorDetector()
            use_operator_detector = True
        except ImportError:
            use_operator_detector = False
            # Fallback to hardcoded mappings
            operator_mappings = {
                'telefonica': {'schema': 'cdr_to2', 'keywords': ['telefonica', 'client11']},
                'vodafone': {'schema': 'cdr_vdf', 'keywords': ['vodafone', 'client13']},
                'telekom': {'schema': 'cdr_tdg', 'keywords': ['telekom', 'client12']}
            }
        
        total_imported = 0
        import_results = []
        processed_sheets = []
        
        # Process each sheet
        for sheet_idx, sheet_name in enumerate(sheet_names, 1):
            sheet_lower = sheet_name.lower()
            
            # Skip statistics or summary sheets
            if any(skip_word in sheet_lower for skip_word in ['statistics', 'summary', 'overview', 'info']):
                print(f"Skip statistics sheet: {sheet_name}")
                continue
            
            # Determine operator based on sheet name
            detected_operator = None
            target_schema = None
            target_table = None
            
            if use_operator_detector:
                try:
                    # Use OperatorDetector to detect operator from sheet name
                    detected_operator = operator_detector.detect_from_filename(sheet_name)
                    if detected_operator:
                        target_schema = operator_detector.get_schema(detected_operator)
                        # Generate table name using the new method
                        target_table = self.get_table_name(file_path, sheet_name)
                except Exception as e:
                    logger.warning(f"OperatorDetector failed for sheet {sheet_name}: {e}")
                    detected_operator = None
            
            # Fallback to hardcoded logic if OperatorDetector is not available or failed
            if not detected_operator and not use_operator_detector:
                for operator, config in operator_mappings.items():
                    if any(keyword in sheet_lower for keyword in config['keywords']):
                        detected_operator = operator
                        target_schema = config['schema']
                        # Generate table name using the new method
                        target_table = self.get_table_name(file_path, sheet_name)
                        break
            
            if not detected_operator:
                print(f"Unknown operator: {sheet_name} - Skip")
                continue
            
            print(f"\nSheet {sheet_idx}/{len(sheet_names)}: {sheet_name} → connect.{target_schema}.{target_table}")
            
            try:
                # Read sheet data with engine specification
                try:
                    df = pd.read_excel(file_path, sheet_name=sheet_name, engine='openpyxl')
                except Exception:
                    try:
                        df = pd.read_excel(file_path, sheet_name=sheet_name, engine='xlrd')
                    except Exception:
                        df = pd.read_excel(file_path, sheet_name=sheet_name)
                
                if df.empty:
                    print(f"Empty sheet - Skip")
                    continue
                
                # Temporarily set operator for this sheet
                original_operator = self.operator
                self.operator = detected_operator
                
                # Process this sheet
                sheet_result = await self._process_single_sheet(
                    df, sheet_name, detected_operator, path, batch_size, start_time, target_schema, target_table
                )
                
                # Restore original operator
                self.operator = original_operator
                
                if sheet_result.status == ImportStatus.COMPLETED:
                    records_imported = sheet_result.metrics.records_processed if sheet_result.metrics else len(df)
                    total_imported += records_imported
                    processed_sheets.append({
                        'sheet_name': sheet_name,
                        'operator': detected_operator,
                        'schema': target_schema,
                        'table': target_table,
                        'records': records_imported
                    })
                    print(f"Completed: {records_imported:,} records")
                else:
                    print(f"Failed: {sheet_result.error_message}")
                
                import_results.append(sheet_result)
                
            except Exception as e:
                print(f"Error: {str(e)}")
                logger.error(f"Error processing sheet {sheet_name}: {e}")
                continue
        
        # Stop performance monitoring
        if self.performance_logger:
            self.performance_logger.end_operation("cdr_import")
        
        # Create summary result
        total_time = time.time() - start_time
        
        print(f"\n" + "="*60)
        print(f"Import Summary:")
        print(f"File: {path.name} | Sheets: {len(processed_sheets)} | Records: {total_imported:,} | Time: {total_time:.2f}s")
        
        for sheet_info in processed_sheets:
            print(f"  {sheet_info['sheet_name']} → connect.{sheet_info['schema']}.{sheet_info['table']}: {sheet_info['records']:,}")
        
        if total_imported > 0:
            metrics = ImportMetrics(
                records_processed=total_imported,
                records_failed=0,
                processing_time_seconds=total_time
            )
            
            return ImportResult(
                status=ImportStatus.COMPLETED,
                source_info={
                    'source_path': str(path),
                    'file_size_bytes': path.stat().st_size,
                    'sheets_processed': processed_sheets
                },
                metrics=metrics,
                metadata={
                    "file_type": path.suffix,
                    "total_sheets": len(sheet_names),
                    "processed_sheets": len(processed_sheets),
                    "operators": list(set(sheet['operator'] for sheet in processed_sheets))
                }
            )
        else:
            return ImportResult(
                status=ImportStatus.FAILED,
                source_info={'source_path': str(path)},
                error_message="No valid data sheets found or processed",
                metrics=ImportMetrics(records_processed=0, records_failed=0, processing_time_seconds=total_time)
            )
    
    async def _process_single_sheet(self, df: pd.DataFrame, sheet_name: str, operator: str, 
                                  path: Path, batch_size: int, start_time: float, 
                                  target_schema: str = None, target_table: str = None) -> ImportResult:
        """Process a single sheet or CSV data."""
        
        # Validate data
        is_valid_structure, structure_errors = self.validate_data_structure(df)
        if not is_valid_structure:
            return ImportResult(
                status=ImportStatus.FAILED,
                source_info={'source_path': str(path), 'sheet_name': sheet_name},
                error_message=f"Data structure validation failed: {', '.join(structure_errors)}"
            )

        is_valid_values, value_errors = self.validate_data_values(df)
        if not is_valid_values:
            return ImportResult(
                status=ImportStatus.FAILED,
                source_info={'source_path': str(path), 'sheet_name': sheet_name},
                error_message=f"Data value validation failed: {', '.join(value_errors)}"
            )

        # Process data in batches with progress tracking
        total_records = len(df)
        processed_records = 0
        
        if not target_schema:
            target_schema = self.operator_configs.get(operator, {}).get('schema', 'cdr_to2')
        if not target_table:
            # Generate table name using the new method
            target_table = self.get_table_name(str(path), sheet_name)
        
        print(f"Target: connect.{target_schema}.{target_table} | Records: {total_records:,}")
        
        # Initialize database connection if not available
        await self._ensure_database_connection()
        
        # Create table if needed
        await self._ensure_table_exists(target_table, target_schema, list(df.columns))
        
        # Calculate total batches
        total_batches = (total_records + batch_size - 1) // batch_size
        
        for batch_idx, i in enumerate(range(0, total_records, batch_size), 1):
            batch = df.iloc[i : i + batch_size]
            batch_size_actual = len(batch)
            
            # Process batch with actual database insertion
            batch_result = await self._process_batch_with_db_insert(batch, target_schema, target_table)

            if batch_result.status != ImportStatus.COMPLETED:
                return ImportResult(
                    status=ImportStatus.FAILED,
                    source_info={'source_path': str(path), 'sheet_name': sheet_name},
                    error_message=f"Failed at batch {batch_idx}: {batch_result.error_message}",
                    metadata={'records_imported': processed_records}
                )

            processed_records += batch_size_actual
            
            # Show simplified progress
            progress_percent = (processed_records / total_records) * 100
            print(f"Progress: {progress_percent:.1f}% ({processed_records:,}/{total_records:,})")
        
        # Create metrics for successful import
        processing_time = time.time() - start_time
        metrics = ImportMetrics(
            records_processed=processed_records,
            records_failed=0,
            processing_time_seconds=processing_time
        )
        
        return ImportResult(
            status=ImportStatus.COMPLETED,
            source_info={
                'source_path': str(path),
                'sheet_name': sheet_name,
                'operator': operator,
                'target_schema': target_schema,
                'target_table': target_table
            },
            metrics=metrics,
            metadata={
                "file_type": path.suffix,
                "columns": list(df.columns),
                "operator": operator,
                "target_schema": target_schema,
                "target_table": target_table
            }
        )

    def import_file(self, file_path: Union[str, Path]) -> ImportResult:
        """Synchronous wrapper for import_data.

        Args:
            file_path: Path to the file

        Returns:
            ImportResult: Result of the import operation
        """
        import asyncio

        # Update source path
        self.source_path = Path(file_path)

        # Run import asynchronously
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If we're already in an event loop, create a task
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, self.import_data(file_path=file_path))
                    return future.result()
            else:
                return loop.run_until_complete(self.import_data(file_path=file_path))
        except RuntimeError:
            # No event loop running, create new one
            return asyncio.run(self.import_data(file_path=file_path))

    def get_source_info(self) -> Dict[str, Any]:
        """Get source information for the importer."""
        return {
            'source_path': str(self.source_path) if self.source_path else None,
            'data_type': 'cdr',
            'operator': self.operator,
            'supported_formats': self.supported_formats
        }

    async def validate_source(self, source_path: str = None) -> bool:
        """Validate the source file."""
        try:
            from pathlib import Path

            path = Path(source_path or self.source_path)

            # Check if file exists
            if not path.exists():
                return False

            # Check file extension
            if path.suffix.lower() not in ['.csv', '.xlsx', '.xls']:
                return False

            return True

        except Exception:
            return False
