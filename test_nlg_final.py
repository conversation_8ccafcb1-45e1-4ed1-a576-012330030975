#!/usr/bin/env python3
"""
Final test for NLG import with all fixes applied
"""
import asyncio
import sys
import traceback
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def test_nlg_final():
    """Final test of NLG import with small batch size."""
    try:
        print("🚀 Final NLG import test...")
        
        from src.importers.import_manager import ImportManager, ImportJobConfig
        
        # Create import manager
        config = {}
        import_manager = ImportManager(config)
        await import_manager.initialize()
        
        # Test file path
        test_file = Path("D:/connect/data/input/nlg/2023/NLG_CUBE_aktuell_2023-01-03.xlsb")
        if not test_file.exists():
            print(f"❌ Test file not found: {test_file}")
            return False
        
        # Create import job config with small batch size for testing
        job_config = ImportJobConfig(
            source_path=str(test_file),
            data_type='nlg',
            operator='telefonica',
            batch_size=50,  # Very small batch for testing
            validate_only=False
        )
        
        # Create and execute import job
        job_id = await import_manager.create_import_job(job_config)
        print(f"📋 Created import job: {job_id}")
        
        # Execute the job
        result = await import_manager.execute_import_job(job_id)
        print(f"✅ Import job result: {result.status}")
        print(f"📊 Records processed: {result.records_processed}")
        print(f"📊 Records imported: {result.records_imported}")
        print(f"⏱️ Processing time: {result.processing_time_seconds:.2f}s")
        
        if result.validation_errors:
            print(f"❌ Validation errors: {result.validation_errors}")
        
        if result.warnings:
            print(f"⚠️ Warnings: {result.warnings}")
        
        # Verify data in database
        async with import_manager.pool_manager.get_connection() as conn:
            tables = await conn.fetch(
                "SELECT table_name FROM information_schema.tables WHERE table_schema = 'nlg_to2' AND table_name LIKE 'nlg_cube_aktuell_%'"
            )
            print(f"📊 NLG tables found: {len(tables)}")
            total_rows = 0
            for table in tables:
                table_name = table['table_name']
                count = await conn.fetchval(f'SELECT COUNT(*) FROM nlg_to2."{table_name}"')
                total_rows += count
                print(f"  - {table_name}: {count} rows")
            
            print(f"📊 Total rows in all NLG tables: {total_rows}")
        
        await import_manager.close()
        
        # Success criteria: job completed and data actually stored
        success = (result.status == 'completed' and total_rows > 0)
        if success:
            print("✅ NLG import test PASSED - data successfully stored in database!")
        else:
            print("❌ NLG import test FAILED - no data found in database")
        
        return success
        
    except Exception as e:
        print(f"❌ Final NLG test failed: {e}")
        traceback.print_exc()
        return False

async def verify_database_state():
    """Verify the current state of the database."""
    try:
        print("\n🔍 Verifying database state...")
        
        import asyncpg
        
        # Test connection
        conn = await asyncpg.connect(
            host='localhost',
            port=5432,
            database='connect',
            user='to2',
            password='TO2'
        )
        
        # Check nlg_to2 schema
        tables = await conn.fetch(
            "SELECT table_name FROM information_schema.tables WHERE table_schema = 'nlg_to2' ORDER BY table_name"
        )
        
        print(f"📊 Tables in nlg_to2 schema: {len(tables)}")
        total_rows = 0
        for table in tables:
            table_name = table['table_name']
            try:
                count = await conn.fetchval(f'SELECT COUNT(*) FROM nlg_to2."{table_name}"')
                total_rows += count
                print(f"  - {table_name}: {count} rows")
            except Exception as e:
                print(f"  - {table_name}: Error getting count - {e}")
        
        print(f"📊 Total rows across all NLG tables: {total_rows}")
        
        await conn.close()
        return total_rows > 0
        
    except Exception as e:
        print(f"❌ Database verification failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting final NLG import test...")
    
    # Verify initial database state
    initial_success = asyncio.run(verify_database_state())
    
    # Run final test
    final_success = asyncio.run(test_nlg_final())
    
    # Verify final database state
    final_db_success = asyncio.run(verify_database_state())
    
    if final_success and final_db_success:
        print("\n🎉 ALL TESTS PASSED! NLG import is working correctly!")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)
