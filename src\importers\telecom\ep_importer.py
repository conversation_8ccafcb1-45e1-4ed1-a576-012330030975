"""EP (Engineering Parameters) importer with geospatial optimization.

This module provides specialized EP data import functionality with support for
geospatial processing, antenna pattern analysis, and network topology optimization.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""
import asyncio
import logging
from typing import Any, Dict, List, Optional, Union, Tuple
from pathlib import Path
from datetime import datetime
import math

import pandas as pd
import numpy as np
from pydantic import BaseModel, Field, ConfigDict

from ..base import (
    AbstractImporter,
    ValidationMixin,
    ProcessingMixin,
    PerformanceMixin,
    ImporterConfig,
    ImportResult,
    ImportStatus,
)


class EPConfig(BaseModel):
    """Configuration specific to EP data import."""

    # Operator-specific settings
    operator: str = Field(
        default="generic",
        description="Telecom operator (telefonica, vodafone, telekom, etc.)",
    )
    network_type: str = Field(default="4G", description="Network type (2G, 3G, 4G, 5G)")
    site_type: str = Field(
        default="all", description="Site type filter (macro, micro, pico, femto)"
    )

    # Geospatial settings
    coordinate_system: str = Field(
        default="EPSG:4326", description="Coordinate reference system"
    )
    enable_spatial_validation: bool = Field(
        default=True, description="Enable spatial coordinate validation"
    )
    enable_coverage_calculation: bool = Field(
        default=True, description="Enable coverage area calculation"
    )
    enable_interference_analysis: bool = Field(
        default=True, description="Enable interference analysis"
    )

    # Engineering parameters validation
    enable_antenna_validation: bool = Field(
        default=True, description="Enable antenna parameter validation"
    )
    enable_power_validation: bool = Field(
        default=True, description="Enable power parameter validation"
    )
    enable_frequency_validation: bool = Field(
        default=True, description="Enable frequency validation"
    )

    # Processing optimization
    enable_spatial_indexing: bool = Field(
        default=True, description="Enable spatial indexing"
    )
    enable_neighbor_detection: bool = Field(
        default=True, description="Enable neighbor cell detection"
    )
    enable_topology_analysis: bool = Field(
        default=True, description="Enable network topology analysis"
    )

    # Performance settings
    batch_size: int = Field(default=10000, description="Batch size for EP processing")
    spatial_buffer_meters: float = Field(
        default=1000.0, description="Spatial buffer for neighbor detection (meters)"
    )
    max_antenna_gain: float = Field(
        default=25.0, description="Maximum antenna gain (dBi)"
    )
    min_antenna_gain: float = Field(
        default=-10.0, description="Minimum antenna gain (dBi)"
    )
    
    # Performance optimization thresholds
    large_dataset_threshold: int = Field(
        default=100000, description="Threshold for large dataset optimizations"
    )
    enable_performance_optimizations: bool = Field(
        default=True, description="Enable performance optimizations for large datasets"
    )
    skip_analysis_for_large_datasets: bool = Field(
        default=True, description="Skip complex analysis for datasets above threshold"
    )
    max_neighbor_search_records: int = Field(
        default=200000, description="Maximum records for neighbor search before skipping"
    )
    use_optimized_algorithms: bool = Field(
        default=True, description="Use optimized algorithms (BallTree, vectorized operations)"
    )

    # Quality thresholds
    max_error_rate: float = Field(
        default=0.005, description="Maximum acceptable error rate"
    )
    min_data_quality_score: float = Field(
        default=0.99, description="Minimum data quality score"
    )

    model_config = ConfigDict(
        extra="allow"
    )
class EPImporter(AbstractImporter, ValidationMixin, ProcessingMixin, PerformanceMixin):
    """Enhanced EP importer with geospatial and engineering optimization.

    This importer provides comprehensive EP data processing with support for:
    - Multiple operator formats and network types
    - Advanced geospatial processing and validation
    - Antenna pattern analysis and coverage calculation
    - Network topology and interference analysis
    - Performance optimization for large datasets
    """

    # Standard EP column mappings for different operators
    OPERATOR_SCHEMAS = {
        "telefonica": {
            "site_id": "sitio_id",
            "cell_id": "celda_id",
            "sector_id": "sector_id",
            "latitude": "latitud",
            "longitude": "longitud",
            "height": "altura",
            "azimuth": "azimut",
            "tilt_mechanical": "inclinacion_mecanica",
            "tilt_electrical": "inclinacion_electrica",
            "antenna_gain": "ganancia_antena",
            "antenna_model": "modelo_antena",
            "frequency": "frecuencia",
            "power": "potencia",
            "bandwidth": "ancho_banda",
            "technology": "tecnologia",
        },
        "vodafone": {
            "site_id": "site_id",
            "cell_id": "cell_id",
            "sector_id": "sector",
            "latitude": "lat",
            "longitude": "lon",
            "height": "antenna_height",
            "azimuth": "azimuth_deg",
            "tilt_mechanical": "mech_tilt",
            "tilt_electrical": "elec_tilt",
            "antenna_gain": "ant_gain_dbi",
            "antenna_model": "antenna_type",
            "frequency": "freq_mhz",
            "power": "power_dbm",
            "bandwidth": "bw_mhz",
            "technology": "tech",
        },
        "telekom": {
            "site_id": "standort_id",
            "cell_id": "zellen_id",
            "sector_id": "sektor_id",
            "latitude": "breitengrad",
            "longitude": "laengengrad",
            "height": "antennenhoehe",
            "azimuth": "azimut_grad",
            "tilt_mechanical": "mechanische_neigung",
            "tilt_electrical": "elektrische_neigung",
            "antenna_gain": "antennengewinn",
            "antenna_model": "antennenmodell",
            "frequency": "frequenz",
            "power": "leistung",
            "bandwidth": "bandbreite",
            "technology": "technologie",
        },
        "generic": {
            "site_id": "site_id",
            "cell_id": "cell_id",
            "sector_id": "sector_id",
            "latitude": "latitude",
            "longitude": "longitude",
            "height": "height",
            "azimuth": "azimuth",
            "tilt_mechanical": "tilt_mechanical",
            "tilt_electrical": "tilt_electrical",
            "antenna_gain": "antenna_gain",
            "antenna_model": "antenna_model",
            "frequency": "frequency",
            "power": "power",
            "bandwidth": "bandwidth",
            "technology": "technology",
        },
        "qgis": {
            "site_id": "SITE_NAME",
            "cell_id": "CI",
            "sector_id": "TX_ID",
            "latitude": "WGS84_LATITUDE",
            "longitude": "WGS84_LONGITUDE",
            "height": "HEIGHT",
            "azimuth": "AZIMUT",
            "tilt_mechanical": "M-Tilt",
            "tilt_electrical": "E-Tilt",
            "antenna_model": "ANTENNA_NAME",
            "frequency": "BCCH",
            "power": "POWER",
            "technology": "FrequencyBand",
            "bts_name": "BTSName",
            "lac": "LAC",
            "bsc_name": "BSCName",
            # Additional column mappings for missing columns
            "mtilt": "M-Tilt",  # Alternative mechanical tilt column name
            "tilt": "E-Tilt",   # Generic tilt (usually electrical)
            "name_short": "SITE_NAME",  # Short name mapping to site name
            "site_name": "SITE_NAME",   # Alternative site name
            "cell_name": "CI",          # Alternative cell name
            "band": "FrequencyBand",     # Alternative band name
            "freq": "BCCH",             # Alternative frequency name
            "pwr": "POWER",             # Alternative power name
            "ant_height": "HEIGHT",     # Alternative height name
            "antenna_height": "HEIGHT", # Another height variant
            "lat": "WGS84_LATITUDE",    # Alternative latitude
            "lon": "WGS84_LONGITUDE",   # Alternative longitude
            "tx_id": "TX_ID",           # Alternative transmitter ID
            "transmitter_id": "TX_ID",  # Another TX ID variant
            "sector": "TX_ID",          # Sector as TX ID
            "bts_id": "BTSName",        # Alternative BTS identifier
            "base_station": "BTSName",  # Base station name
            "node_b": "BTSName",        # Node B name
            "antenna_type": "ANTENNA_NAME",  # Alternative antenna name
            "ant_model": "ANTENNA_NAME",     # Antenna model
            "frequency_mhz": "BCCH",         # Frequency in MHz
            "freq_mhz": "BCCH",             # Freq in MHz
            "channel": "BCCH",              # Channel number
            "power_dbm": "POWER",           # Power in dBm
            "tx_power": "POWER",            # Transmit power
            "output_power": "POWER",        # Output power
            "technology_type": "FrequencyBand",  # Technology type
            "network_type": "FrequencyBand",     # Network type
        },
    }

    # Engineering parameter ranges by network type
    NETWORK_PARAMETERS = {
        "2G": {
            "frequency_range": (850, 1900),  # MHz
            "power_range": (20, 50),  # dBm
            "bandwidth_range": (0.2, 0.2),  # MHz
            "typical_height": (15, 50),  # meters
            "coverage_radius": (1000, 35000),  # meters
        },
        "3G": {
            "frequency_range": (850, 2100),
            "power_range": (20, 46),
            "bandwidth_range": (5, 5),
            "typical_height": (15, 50),
            "coverage_radius": (500, 20000),
        },
        "4G": {
            "frequency_range": (700, 2600),
            "power_range": (23, 46),
            "bandwidth_range": (1.4, 20),
            "typical_height": (15, 60),
            "coverage_radius": (200, 15000),
        },
        "5G": {
            "frequency_range": (600, 39000),
            "power_range": (23, 50),
            "bandwidth_range": (5, 400),
            "typical_height": (10, 100),
            "coverage_radius": (50, 10000),
        },
    }

    def __init__(
        self, config: Optional[Union[ImporterConfig, Dict[str, Any]]] = None, **kwargs
    ):
        """Initialize EP importer.

        Args:
            config: Importer configuration
            **kwargs: Additional configuration parameters
        """
        super().__init__(config=config, **kwargs)

        # EP-specific configuration
        ep_config_dict = kwargs.get("ep_config", {})
        if isinstance(ep_config_dict, EPConfig):
            self.ep_config = ep_config_dict
        else:
            self.ep_config = EPConfig(**ep_config_dict)

        # Set data type
        self.data_type = "EP"
        self.name = "EP Importer"
        self.supported_formats = [".csv", ".xlsx", ".xls", ".json", ".kml", ".shp"]

        # Required columns (will be mapped based on operator) - REMOVED ALL RESTRICTIONS
        # Allow any column structure to support flexible EP data formats
        self.required_columns = []

        # Configure processing for EP data
        self.configure_processing(
            {
                "batch_size": self.ep_config.batch_size,
                "enable_parallel": True,
                "memory_limit_mb": 4096,  # Increased from 1024MB to match NLG importer
                "use_polars": False,  # Pandas for better geospatial handling
            }
        )

        # Configure validation for EP data - RELAXED VALIDATION
        self.configure_validation(
            {
                "enable_strict_validation": False,
                "max_error_rate": self.ep_config.max_error_rate,
                "enable_spatial_validation": self.ep_config.enable_spatial_validation,
                "coordinate_system": self.ep_config.coordinate_system,
            }
        )

        # Configure performance monitoring
        self.configure_performance(
            {
                "enable_monitoring": True,
                "enable_telecom_optimizations": True,
                "ep_batch_size": self.ep_config.batch_size,
            }
        )

        self.logger.info(
            f"EP Importer initialized for operator: {self.ep_config.operator}, network: {self.ep_config.network_type}"
        )

    def get_table_name(self, filename: str) -> str:
        """Generate table name for EP data based on filename.

        Uses the standardized table naming utility to ensure consistent
        table naming across the system.

        Args:
            filename: Name of the source file (can be full path)

        Returns:
            Generated table name following ep_{cell_type}_{year}_cw{week} pattern
        """
        try:
            # Use absolute import to avoid relative import issues
            import sys
            from pathlib import Path
            
            # Add src to path if not already there
            src_path = Path(__file__).parent.parent.parent
            if str(src_path) not in sys.path:
                sys.path.insert(0, str(src_path))
            
            from ...database.utils.table_naming import TableNamingManager
            import yaml
            
            # Load the proper database configuration
            config_path = Path("config/database.yaml")
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
            else:
                # Fallback to instance config if database.yaml not found
                config = getattr(self, 'config', {}) or {}
            
            naming_manager = TableNamingManager(config)
            
            # Convert filename to Path object
            file_path = Path(filename)
            
            # Generate table name using standardized logic
            # Pattern: ep_{cell_type}_{year}_cw{week}
            table_name = naming_manager.generate_table_name(
                data_type="ep",
                file_path=file_path
            )
            
            self.logger.debug(f"Generated table name '{table_name}' for file '{filename}'")
            return table_name
            
        except Exception as e:
            self.logger.warning(f"Failed to generate table name using pattern, falling back to simple naming: {e}")
            
            # Fallback to original logic if table naming manager fails
            base_name = Path(filename).stem.lower()
            parts = base_name.split("_")

            if len(parts) >= 2:
                cell_type = parts[0].replace("cell", "").lower()
                week_part = parts[1] if parts[1].startswith("cw") else "unknown"

                # Try to extract year from path with improved logic
                year = str(datetime.datetime.now().year)  # Default to current year
                
                # Look for 4-digit year in path segments
                path_parts = filename.replace("\\", "/").split("/")
                for part in path_parts:
                    if part.isdigit() and len(part) == 4:
                        year_int = int(part)
                        # Validate year range (reasonable for telecommunications data)
                        if 2020 <= year_int <= 2030:
                            year = part
                            break

                return f"ep_{cell_type}_{year}_{week_part}"
            else:
                return f"ep_{base_name}"

    async def import_data(self, source: Union[str, Path], **kwargs) -> ImportResult:
        """Import EP data with comprehensive processing.

        Args:
            source: Data source (file path or connection string)
            **kwargs: Additional import parameters

        Returns:
            Import result with metrics and status
        """
        start_time = datetime.now()
        
        # Initialize metrics
        self.metrics.start_time = start_time
        self.metrics.records_processed = 0

        try:
            # Start performance monitoring
            await self.start_performance_monitoring()

            with self.track_operation("ep_import", source=str(source)) as op_metrics:
                # Validate source
                if not await self.validate_source(source):
                    return ImportResult(
                        status=ImportStatus.FAILED,
                        error_message="Source validation failed",
                        metadata={
                            "errors": ["Invalid or inaccessible data source"],
                            "records_imported": 0,
                            "records_failed": 0,
                            "source_path": str(source),
                            "processing_time": 0,
                            "file_size_bytes": 0
                        }
                    )

                # Load and process data
                # Add source_path to kwargs so _process_ep_batch can extract year/week
                kwargs['source_path'] = str(source)
                
                # First load data to check size
                initial_data = await self.load_data(source, **kwargs)
                self.logger.info(f"Loaded initial data: {len(initial_data) if initial_data is not None else 0} records")
                if initial_data is None or len(initial_data) == 0:
                    return ImportResult(
                        status=ImportStatus.FAILED,
                        error_message="No data loaded from source",
                        metadata={
                            "records_imported": 0,
                            "records_failed": 0,
                            "source_path": str(source),
                            "processing_time": 0,
                            "file_size_bytes": 0
                        }
                    )
                
                # Check system resources and adjust processing strategy
                resource_info = self._check_system_resources(len(initial_data))
                self.logger.info(f"Resource check for {len(initial_data)} records: {resource_info}")
                
                # Store original config values
                original_neighbor_detection = self.ep_config.enable_neighbor_detection
                original_interference_analysis = self.ep_config.enable_interference_analysis
                original_topology_analysis = self.ep_config.enable_topology_analysis
                original_batch_size = self.ep_config.batch_size
                
                try:
                    # Temporarily adjust config based on resource availability
                    if resource_info.get("skip_complex_analysis", False):
                        self.ep_config.enable_neighbor_detection = False
                        self.ep_config.enable_interference_analysis = False
                        self.ep_config.enable_topology_analysis = False
                        self.logger.warning("Disabled complex analysis due to resource constraints")
                    
                    # Adjust batch size if recommended
                    recommended_batch_size = resource_info.get("recommended_batch_size", original_batch_size)
                    if recommended_batch_size != original_batch_size:
                        self.ep_config.batch_size = recommended_batch_size
                        self.logger.info(f"Adjusted batch size from {original_batch_size} to {recommended_batch_size}")
                    
                    # Process the data with optimized settings
                    self.logger.info(f"Processing batch with {len(initial_data)} records")
                    data = await self._process_ep_batch(initial_data, **kwargs)
                    self.logger.info(f"Processed batch result: {len(data) if data is not None else 0} records")
                    
                finally:
                    # Restore original config values
                    self.ep_config.enable_neighbor_detection = original_neighbor_detection
                    self.ep_config.enable_interference_analysis = original_interference_analysis
                    self.ep_config.enable_topology_analysis = original_topology_analysis
                    self.ep_config.batch_size = original_batch_size

                if data is None or len(data) == 0:
                    return ImportResult(
                        status=ImportStatus.FAILED,
                        error_message="No data processed",
                        metadata={
                            "records_imported": 0,
                            "records_failed": 0,
                            "source_path": str(source),
                            "processing_time": 0,
                            "file_size_bytes": 0
                        }
                    )

                # Update operation metrics
                op_metrics.records_processed = len(data)
                op_metrics.bytes_processed = data.memory_usage(deep=True).sum()

                # Validate processed data
                try:
                    validation_result = await self.validate_telecom_data(data, "EP")
                    # Ensure validation_result is a dictionary
                    if not isinstance(validation_result, dict):
                        self.logger.warning(f"Validation returned unexpected type: {type(validation_result)}")
                        validation_result = {
                            'valid': True,
                            'has_warnings': False,
                            'summary': {'errors': 0, 'warnings': 0},
                            'results': {'errors': [], 'warnings': []}
                        }
                except Exception as e:
                    self.logger.error(f"Validation failed: {e}")
                    validation_result = {
                        'valid': False,
                        'has_warnings': True,
                        'summary': {'errors': 1, 'warnings': 0},
                        'results': {'errors': [{'message': f'Validation error: {e}'}], 'warnings': []}
                    }

                if not validation_result.get('valid', True):
                    error_count = validation_result.get('summary', {}).get('errors', 0)
                    total_records = len(data)
                    error_rate = error_count / total_records if total_records > 0 else 0
                    
                    if error_rate > getattr(self.ep_config, 'max_error_rate', 0.1):
                        return ImportResult(
                            status=ImportStatus.FAILED,
                            error_message=f"Data quality below threshold: {error_rate:.2%}",
                            metadata={
                                "errors": [e.get('message', '') for e in validation_result.get('results', {}).get('errors', [])][:10],
                                "records_imported": 0,
                                "records_failed": len(data),
                                "source_path": str(source),
                                "processing_time": (datetime.now() - start_time).total_seconds(),
                                "file_size_bytes": 0
                            }
                        )
                    else:
                        self.logger.warning(
                            f"Data quality issues detected: {error_rate:.2%}"
                        )

                # Perform EP-specific analysis
                analysis_results = await self._perform_ep_analysis(data)

                # Store data if database operations are available
                if hasattr(self, "db_ops") and self.db_ops:
                    try:
                        await self._store_ep_data(data)
                    except Exception as e:
                        self.logger.error(f"Database storage failed: {e}")
                        return ImportResult(
                            status=ImportStatus.FAILED,
                            error_message=f"Database storage failed: {e}",
                            metadata={"errors": [str(e)], "records_imported": 0, "records_failed": len(data)},
                        )

                # Calculate processing time
                processing_time = (datetime.now() - start_time).total_seconds()
                
                # Update final metrics
                self.metrics.records_processed = len(data)
                self.metrics.processing_time_seconds = processing_time
                self.metrics.end_time = datetime.now()
                self.metrics.calculate_throughput()

                return ImportResult(
                    status=ImportStatus.COMPLETED,
                    metrics=self.metrics,
                    metadata={
                        "records_imported": len(data),
                        "records_failed": validation_result.get('summary', {}).get('errors', 0),
                        "processing_time": processing_time,
                        "data_quality_score": 1.0 - (validation_result.get('summary', {}).get('errors', 0) / len(data)) if len(data) > 0 else 1.0,
                        "warnings": [w.get('message', '') for w in validation_result.get('results', {}).get('warnings', [])][:5],
                        "analysis_results": analysis_results,
                    },
                )

        except Exception as e:
            self.logger.error(f"EP import failed: {e}")
            return ImportResult(
                status=ImportStatus.FAILED,
                error_message=f"Import failed: {e}",
                metadata={"errors": [str(e)], "records_imported": 0, "records_failed": 0},
            )

        finally:
            await self.stop_performance_monitoring()
            # Force garbage collection to free memory
            import gc
            gc.collect()

    async def load_data(self, source: Union[str, Path], **kwargs) -> pd.DataFrame:
        """Load EP data from file.
        
        Args:
            source: Path to EP data file
            **kwargs: Additional loading parameters
            
        Returns:
            Loaded DataFrame
            
        Raises:
            ImportError: If file cannot be loaded
            ValueError: If data format is invalid
        """
        source_path = Path(source)
        
        if not source_path.exists():
            raise FileNotFoundError(f"EP data file not found: {source_path}")
            
        self.logger.info(f"Loading EP data from: {source_path}")
        
        try:
            # Load database configuration to get skip_rows and header_row
            import yaml
            config_path = Path("config/database.yaml")
            ep_config = {}
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                    ep_config = config.get('telecom_data_sources', {}).get('ep', {})
                    self.logger.info(f"Loaded EP config from database.yaml: skip_rows={ep_config.get('skip_rows', 'not set')}, header_row={ep_config.get('header_row', 'not set')}")
            else:
                self.logger.warning("database.yaml not found, using default parameters")
            
            # Apply configuration parameters to kwargs if not already provided
            if 'skiprows' not in kwargs and 'skip_rows' in ep_config:
                kwargs['skiprows'] = ep_config['skip_rows']
                self.logger.info(f"Applied skip_rows={ep_config['skip_rows']} from config")
            
            if 'header' not in kwargs and 'header_row' in ep_config:
                # Adjust header row based on skip_rows
                skip_rows = kwargs.get('skiprows', ep_config.get('skip_rows', 0))
                if skip_rows > 0:
                    kwargs['header'] = 0  # First row after skipped rows becomes header
                else:
                    kwargs['header'] = ep_config['header_row']
                self.logger.info(f"Applied header={kwargs['header']} (adjusted for skip_rows={skip_rows})")
            
            # Detect file format and load accordingly
            file_extension = source_path.suffix.lower()
            
            if file_extension == '.csv':
                data = await self._load_csv_data(source_path, **kwargs)
            elif file_extension in ['.xlsx', '.xls']:
                data = await self._load_excel_data(source_path, **kwargs)
            elif file_extension == '.json':
                data = await self._load_json_data(source_path, **kwargs)
            else:
                # Default to CSV format
                self.logger.warning(f"Unknown file extension {file_extension}, treating as CSV")
                data = await self._load_csv_data(source_path, **kwargs)
                
            if data is None or len(data) == 0:
                raise ValueError("No data loaded from EP file")
                
            self.logger.info(f"Successfully loaded {len(data)} rows from EP file")
            return data
            
        except Exception as e:
            self.logger.error(f"Failed to load EP data: {e}")
            raise ImportError(f"Failed to load EP data from {source_path}: {e}")
    
    async def _load_csv_data(self, file_path: Path, **kwargs) -> pd.DataFrame:
        """Load EP data from CSV file.
        
        Args:
            file_path: Path to CSV file
            **kwargs: Additional parameters
            
        Returns:
            Loaded DataFrame
        """
        try:
            # Basic CSV reading with error handling
            read_params = {
                'filepath_or_buffer': file_path,
                'encoding': 'utf-8',
                'low_memory': False,
                'na_values': ['', 'NULL', 'null', 'N/A', 'n/a'],
                'keep_default_na': True
            }
            
            # Filter out unsupported parameters for pandas read_csv
            supported_params = {
                'sep', 'delimiter', 'header', 'names', 'index_col', 'usecols', 'squeeze',
                'prefix', 'mangle_dupe_cols', 'dtype', 'engine', 'converters', 'true_values',
                'false_values', 'skipinitialspace', 'skiprows', 'skipfooter', 'nrows',
                'na_values', 'keep_default_na', 'na_filter', 'verbose', 'skip_blank_lines',
                'parse_dates', 'infer_datetime_format', 'keep_date_col', 'date_parser',
                'dayfirst', 'cache_dates', 'iterator', 'chunksize', 'compression',
                'thousands', 'decimal', 'lineterminator', 'quotechar', 'quoting',
                'doublequote', 'escapechar', 'comment', 'encoding', 'dialect',
                'error_bad_lines', 'warn_bad_lines', 'delim_whitespace', 'low_memory',
                'memory_map', 'float_precision', 'storage_options'
            }
            
            # Add only supported parameters from kwargs
            for key, value in kwargs.items():
                if key in supported_params:
                    read_params[key] = value
                else:
                    self.logger.debug(f"Ignoring unsupported CSV parameter: {key}")
            
            # Try UTF-8 first, fallback to other encodings
            try:
                data = pd.read_csv(**read_params)
            except UnicodeDecodeError:
                self.logger.warning("UTF-8 decoding failed, trying other encodings")
                for encoding in ['gbk', 'gb2312', 'latin1']:
                    try:
                        read_params['encoding'] = encoding
                        data = pd.read_csv(**read_params)
                        self.logger.info(f"Successfully loaded with {encoding} encoding")
                        break
                    except UnicodeDecodeError:
                        continue
                else:
                    raise ValueError("Could not decode file with any supported encoding")
            
            return data
            
        except Exception as e:
            self.logger.error(f"Failed to load CSV data: {e}")
            raise
    
    async def _load_excel_data(self, file_path: Path, **kwargs) -> pd.DataFrame:
        """Load EP data from Excel file.
        
        Args:
            file_path: Path to Excel file
            **kwargs: Additional parameters
            
        Returns:
            Loaded DataFrame
        """
        try:
            # Read Excel file
            read_params = {
                'io': file_path,
                'engine': 'openpyxl' if file_path.suffix == '.xlsx' else 'xlrd'
            }
            
            # Filter out unsupported parameters for pandas read_excel
            supported_params = {
                'sheet_name', 'header', 'names', 'index_col', 'usecols', 'squeeze',
                'dtype', 'engine', 'converters', 'true_values', 'false_values',
                'skiprows', 'nrows', 'na_values', 'keep_default_na', 'na_filter',
                'verbose', 'parse_dates', 'date_parser', 'thousands', 'comment',
                'skipfooter', 'convert_float', 'mangle_dupe_cols', 'storage_options'
            }
            
            # Add only supported parameters from kwargs
            for key, value in kwargs.items():
                if key in supported_params:
                    read_params[key] = value
                else:
                    self.logger.debug(f"Ignoring unsupported Excel parameter: {key}")
            
            # If sheet_name not specified, read the first sheet
            if 'sheet_name' not in read_params:
                read_params['sheet_name'] = 0
            
            data = pd.read_excel(**read_params)
            return data
            
        except Exception as e:
            self.logger.error(f"Failed to load Excel data: {e}")
            raise
    
    async def _load_json_data(self, file_path: Path, **kwargs) -> pd.DataFrame:
        """Load EP data from JSON file.
        
        Args:
            file_path: Path to JSON file
            **kwargs: Additional parameters
            
        Returns:
            Loaded DataFrame
        """
        try:
            # Read JSON file
            import json
            with open(file_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            
            # Convert to DataFrame
            if isinstance(json_data, list):
                data = pd.DataFrame(json_data)
            elif isinstance(json_data, dict):
                # If it's a dict, try to find the data array
                if 'data' in json_data:
                    data = pd.DataFrame(json_data['data'])
                else:
                    # Convert dict to single-row DataFrame
                    data = pd.DataFrame([json_data])
            else:
                raise ValueError("Unsupported JSON structure")
            
            return data
            
        except Exception as e:
            self.logger.error(f"Failed to load JSON data: {e}")
            raise

    async def _process_ep_batch(self, batch: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """Process a batch of EP data.

        Args:
            batch: Batch of EP data
            **kwargs: Additional processing parameters

        Returns:
            Processed batch
        """
        try:
            # Apply operator-specific column mapping
            batch = self._map_operator_columns(batch)

            # Standardize data types
            batch = self._standardize_data_types(batch)

            # Process geospatial data
            batch = self._process_geospatial_data(batch)

            # Validate and normalize engineering parameters
            batch = self._process_engineering_parameters(batch)

            # Calculate coverage areas if enabled
            if self.ep_config.enable_coverage_calculation:
                batch = self._calculate_coverage_areas(batch)

            # Detect neighbors if enabled and performance allows
            if (self.ep_config.enable_neighbor_detection and 
                self.ep_config.enable_performance_optimizations):
                try:
                    batch = await self._detect_neighbors(batch)
                except Exception as e:
                    self.logger.warning(f"Neighbor detection failed, continuing without: {e}")
                    batch["neighbor_cells"] = [[] for _ in range(len(batch))]
                    batch["neighbor_count"] = 0

            # Analyze topology if enabled
            if self.ep_config.enable_topology_analysis:
                try:
                    batch = self._analyze_topology(batch)
                except Exception as e:
                    self.logger.warning(f"Topology analysis failed, continuing without: {e}")

            # Perform interference analysis if enabled and performance allows
            if (self.ep_config.enable_interference_analysis and 
                self.ep_config.enable_performance_optimizations):
                try:
                    batch = self._analyze_interference(batch)
                except Exception as e:
                    self.logger.warning(f"Interference analysis failed, continuing without: {e}")
                    batch["interference_score"] = 0.0
                    batch["interference_level"] = "low"

            # Extract year and week from file path if available
            source_path = kwargs.get('source_path', '')
            if source_path:
                try:
                    from ...database.utils.table_naming import TableNamingManager
                    # Create table naming manager with config
                    config = getattr(self, 'config', {}) or {}
                    naming_manager = TableNamingManager(config)
                    
                    # Extract year and week from filename/path
                    import os
                    filename = os.path.basename(source_path)
                    year_week = naming_manager._extract_year_week_from_path(source_path, filename)
                    if year_week:
                        batch["year"] = year_week[0]
                        batch["cw"] = year_week[1]
                    else:
                        # Try to extract from filename only
                        year_week = naming_manager._extract_year_week(filename)
                        if year_week:
                            batch["year"] = year_week[0]
                            batch["cw"] = year_week[1]
                        else:
                            # Set default values if extraction fails
                            batch["year"] = None
                            batch["cw"] = None
                            self.logger.warning(f"Could not extract year/week from path: {source_path}")
                except Exception as e:
                    self.logger.warning(f"Error extracting year/week from path {source_path}: {e}")
                    batch["year"] = None
                    batch["cw"] = None
            else:
                batch["year"] = None
                batch["cw"] = None

            # Add metadata
            batch["import_timestamp"] = datetime.now()
            batch["operator"] = self.ep_config.operator
            batch["network_type"] = self.ep_config.network_type
            batch["data_source"] = "ep_import"

            return batch

        except Exception as e:
            self.logger.error(f"EP batch processing failed: {e}")
            raise

    def _map_operator_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """Map operator-specific column names to standard names with enhanced logic."""
        schema = self.OPERATOR_SCHEMAS.get(
            self.ep_config.operator, self.OPERATOR_SCHEMAS["generic"]
        )

        # Create reverse mapping (file columns to standard columns)
        # Handle case-insensitive matching for better compatibility
        column_mapping = {}
        # Ensure all column names are strings
        df.columns = [str(col) for col in df.columns]
        df_columns_lower = {str(col).lower(): str(col) for col in df.columns}
        
        for standard_col, file_col in schema.items():
            # Try exact match first
            if file_col in df.columns:
                column_mapping[file_col] = standard_col
            # Try case-insensitive match
            elif file_col.lower() in df_columns_lower:
                actual_col = df_columns_lower[file_col.lower()]
                column_mapping[actual_col] = standard_col
            # Try partial matching for common variations
            else:
                for df_col in df.columns:
                    # Handle common column name variations
                    if self._is_column_match(df_col, file_col):
                        column_mapping[df_col] = standard_col
                        break

        # Apply column mapping
        if column_mapping:
            df = df.rename(columns=column_mapping)
            self.logger.info(
                f"Mapped {len(column_mapping)} columns for operator {self.ep_config.operator}: {column_mapping}"
            )
        else:
            self.logger.warning(
                f"No column mappings found for operator {self.ep_config.operator}. "
                f"Available columns: {list(df.columns)}"
            )

        # Handle duplicate column names by adding counter suffix
        clean_columns = []
        for col in df.columns:
            clean_name = str(col).strip().lower()
            
            # Ensure uniqueness by adding counter for duplicates
            original_clean_name = clean_name
            counter = 1
            while clean_name in clean_columns:
                clean_name = f'{original_clean_name}_{counter}'
                counter += 1
            
            clean_columns.append(clean_name)
        
        df.columns = clean_columns
        
        return df
    
    def _is_column_match(self, df_col: str, schema_col: str) -> bool:
        """Check if a dataframe column matches a schema column with fuzzy logic.
        
        Args:
            df_col: Column name from the dataframe
            schema_col: Expected column name from schema
            
        Returns:
            True if columns are considered a match
        """
        # Ensure inputs are strings
        df_col = str(df_col)
        schema_col = str(schema_col)
        
        df_col_clean = df_col.lower().strip()
        schema_col_clean = schema_col.lower().strip()
        
        # Exact match
        if df_col_clean == schema_col_clean:
            return True
            
        # Remove common separators and try again
        df_col_normalized = df_col_clean.replace('-', '').replace('_', '').replace(' ', '')
        schema_col_normalized = schema_col_clean.replace('-', '').replace('_', '').replace(' ', '')
        
        if df_col_normalized == schema_col_normalized:
            return True
            
        # Handle common abbreviations and variations
        common_mappings = {
            'mtilt': ['m-tilt', 'mech_tilt', 'mechanical_tilt', 'tilt_mech'],
            'tilt': ['e-tilt', 'elec_tilt', 'electrical_tilt', 'tilt_elec', 'etilt'],
            'lat': ['latitude', 'wgs84_latitude'],
            'lon': ['longitude', 'wgs84_longitude'],
            'freq': ['frequency', 'bcch', 'channel'],
            'pwr': ['power', 'tx_power', 'output_power'],
            'ant': ['antenna', 'antenna_name', 'antenna_model'],
        }
        
        # Check if either column matches common variations
        for key, variations in common_mappings.items():
            if (key in df_col_normalized and schema_col_normalized in variations) or \
               (key in schema_col_normalized and df_col_normalized in variations):
                return True
                
        return False

    def _standardize_data_types(self, df: pd.DataFrame) -> pd.DataFrame:
        """Standardize data types for EP fields."""
        # Site and cell IDs as strings
        for col in ["site_id", "cell_id", "sector_id"]:
            if col in df.columns:
                df[col] = df[col].astype(str).str.strip()

        # Coordinates as float
        for col in ["latitude", "longitude"]:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors="coerce")

        # Engineering parameters as float
        numeric_fields = [
            "height",
            "azimuth",
            "tilt_mechanical",
            "tilt_electrical",
            "antenna_gain",
            "frequency",
            "power",
            "bandwidth",
        ]

        for field in numeric_fields:
            if field in df.columns:
                df[field] = pd.to_numeric(df[field], errors="coerce")

        return df

    def _process_geospatial_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Process geospatial data and create geometry objects."""
        if "latitude" in df.columns and "longitude" in df.columns:
            # Filter valid coordinates
            valid_coords = (
                df["latitude"].notna()
                & df["longitude"].notna()
                & (df["latitude"].between(-90, 90))
                & (df["longitude"].between(-180, 180))
            )

            # Create geometry column for valid coordinates
            if valid_coords.any():
                try:
                    from shapely.geometry import Point

                    df.loc[valid_coords, "geometry"] = df.loc[valid_coords].apply(
                        lambda row: Point(row["longitude"], row["latitude"]), axis=1
                    )

                    # Create spatial index if enabled
                    if self.ep_config.enable_spatial_indexing:
                        df.loc[valid_coords, "spatial_index"] = range(
                            valid_coords.sum()
                        )

                except ImportError:
                    self.logger.warning(
                        "Shapely not available, skipping geometry creation"
                    )

            self.logger.info(
                f"Processed geospatial data for {valid_coords.sum()} records"
            )

        return df

    def _process_engineering_parameters(self, df: pd.DataFrame) -> pd.DataFrame:
        """Process and validate engineering parameters."""
        network_params = self.NETWORK_PARAMETERS.get(
            self.ep_config.network_type, self.NETWORK_PARAMETERS["4G"]
        )

        # Validate frequency
        if "frequency" in df.columns:
            freq_min, freq_max = network_params["frequency_range"]
            df["frequency_valid"] = df["frequency"].between(freq_min, freq_max)

        # Validate power
        if "power" in df.columns:
            power_min, power_max = network_params["power_range"]
            df["power_valid"] = df["power"].between(power_min, power_max)

        # Validate antenna gain
        if "antenna_gain" in df.columns:
            df["antenna_gain_valid"] = df["antenna_gain"].between(
                self.ep_config.min_antenna_gain, self.ep_config.max_antenna_gain
            )

        # Normalize azimuth to 0-360 degrees
        if "azimuth" in df.columns:
            df["azimuth"] = df["azimuth"] % 360

        # Validate tilt values
        for tilt_col in ["tilt_mechanical", "tilt_electrical"]:
            if tilt_col in df.columns:
                df[f"{tilt_col}_valid"] = df[tilt_col].between(-90, 90)

        # Calculate total tilt
        if "tilt_mechanical" in df.columns and "tilt_electrical" in df.columns:
            df["tilt_total"] = df["tilt_mechanical"].fillna(0) + df[
                "tilt_electrical"
            ].fillna(0)

        return df

    def _calculate_coverage_areas(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate theoretical coverage areas for cells."""
        if not all(col in df.columns for col in ["latitude", "longitude", "azimuth"]):
            return df

        network_params = self.NETWORK_PARAMETERS.get(
            self.ep_config.network_type, self.NETWORK_PARAMETERS["4G"]
        )

        # Estimate coverage radius based on network type and power
        if "power" in df.columns:
            # Simplified path loss calculation
            # Coverage radius = f(power, frequency, antenna_gain, height)
            base_radius = network_params["coverage_radius"][1] / 2  # Use average

            # Adjust for power (simplified)
            power_factor = (df["power"].fillna(30) - 20) / 20  # Normalize around 30dBm

            # Adjust for antenna gain
            if "antenna_gain" in df.columns:
                gain_factor = (
                    df["antenna_gain"].fillna(15) - 10
                ) / 10  # Normalize around 15dBi
            else:
                gain_factor = 0

            # Adjust for height
            if "height" in df.columns:
                height_factor = (
                    df["height"].fillna(30) - 20
                ) / 20  # Normalize around 30m
            else:
                height_factor = 0

            df["coverage_radius"] = base_radius * (
                1 + 0.3 * power_factor + 0.2 * gain_factor + 0.1 * height_factor
            )
            df["coverage_radius"] = df["coverage_radius"].clip(
                network_params["coverage_radius"][0],
                network_params["coverage_radius"][1],
            )
        else:
            df["coverage_radius"] = network_params["coverage_radius"][1] / 2

        # Calculate coverage area (simplified circular model)
        df["coverage_area_km2"] = np.pi * (df["coverage_radius"] / 1000) ** 2

        return df

    async def _detect_neighbors(self, df: pd.DataFrame) -> pd.DataFrame:
        """Detect neighboring cells based on spatial proximity with optimized performance."""
        # Check for required columns
        required_cols = ["latitude", "longitude"]
        if not all(col in df.columns for col in required_cols):
            self.logger.warning(f"Missing required columns for neighbor detection: {[col for col in required_cols if col not in df.columns]}")
            return df

        # Force enable neighbor detection - skip_analysis_for_large_datasets disabled
        # if (self.ep_config.skip_analysis_for_large_datasets and 
        #     len(df) > self.ep_config.large_dataset_threshold):
        #     self.logger.warning(f"Skipping neighbor detection for large dataset ({len(df)} records) to prevent performance issues")
        #     df["neighbor_cells"] = [[] for _ in range(len(df))]
        #     df["neighbor_count"] = 0
        #     return df
            
        # Hard limit for very large datasets
        if len(df) > self.ep_config.max_neighbor_search_records:
            self.logger.warning(f"Dataset too large ({len(df)} records) for neighbor detection, skipping")
            df["neighbor_cells"] = [[] for _ in range(len(df))]
            df["neighbor_count"] = 0
            return df

        try:
            from shapely.geometry import Point
            import geopandas as gpd
            from sklearn.neighbors import BallTree
            import numpy as np

            # Create geometry column if it doesn't exist
            if "geometry" not in df.columns:
                df["geometry"] = df.apply(
                    lambda row: Point(row["longitude"], row["latitude"]) 
                    if pd.notna(row["longitude"]) and pd.notna(row["latitude"]) 
                    else None, 
                    axis=1
                )

            # Filter out rows with invalid geometry
            valid_geom_mask = df["geometry"].notna()
            if not valid_geom_mask.any():
                self.logger.warning("No valid geometries found for neighbor detection")
                df["neighbor_cells"] = [[] for _ in range(len(df))]
                df["neighbor_count"] = 0
                return df

            # Use BallTree for efficient neighbor search (much faster than GeoPandas for large datasets)
            valid_df = df[valid_geom_mask].copy()
            coords = np.array([[row.geometry.y, row.geometry.x] for _, row in valid_df.iterrows()])
            
            # Convert buffer from meters to radians (approximate)
            buffer_radians = self.ep_config.spatial_buffer_meters / 6371000.0  # Earth radius in meters
            
            # Build BallTree for efficient neighbor search
            tree = BallTree(np.radians(coords), metric='haversine')
            
            # Find neighbors for all points at once
            neighbor_indices = tree.query_radius(np.radians(coords), r=buffer_radians)
            
            # Initialize neighbor lists for all rows
            neighbors_list = [[] for _ in range(len(df))]
            neighbor_counts = [0 for _ in range(len(df))]
            
            # Map back to original dataframe indices
            valid_indices = valid_df.index.tolist()
            
            for i, neighbors_idx in enumerate(neighbor_indices):
                original_idx = valid_indices[i]
                
                # Get neighbor cell IDs (excluding self)
                neighbor_original_indices = [valid_indices[j] for j in neighbors_idx if j != i]
                
                if "cell_id" in df.columns:
                    neighbor_ids = df.loc[neighbor_original_indices, "cell_id"].dropna().tolist()
                else:
                    neighbor_ids = neighbor_original_indices
                
                neighbors_list[original_idx] = neighbor_ids
                neighbor_counts[original_idx] = len(neighbor_ids)

            df["neighbor_cells"] = neighbors_list
            df["neighbor_count"] = neighbor_counts

            self.logger.info(f"Detected neighbors for {len(df)} cells using optimized BallTree algorithm")

        except ImportError as e:
            self.logger.warning(f"Required libraries not available for neighbor detection: {e}")
            df["neighbor_cells"] = [[] for _ in range(len(df))]
            df["neighbor_count"] = 0
        except Exception as e:
            self.logger.warning(f"Neighbor detection failed: {e}")
            df["neighbor_cells"] = [[] for _ in range(len(df))]
            df["neighbor_count"] = 0

        return df

    def _analyze_topology(self, df: pd.DataFrame) -> pd.DataFrame:
        """Analyze network topology characteristics."""
        # Site-level analysis
        if "site_id" in df.columns:
            site_stats = (
                df.groupby("site_id")
                .agg(
                    {
                        "cell_id": "count",
                        "sector_id": "nunique",
                        "frequency": ["mean", "nunique"],
                        "power": "mean",
                        "coverage_radius": "mean",
                    }
                )
                .round(2)
            )

            # Flatten column names
            site_stats.columns = ["_".join(col).strip() for col in site_stats.columns]
            site_stats = site_stats.reset_index()
            site_stats.rename(columns={"cell_id_count": "cells_per_site"}, inplace=True)

            # Merge back to main dataframe
            df = df.merge(site_stats, on="site_id", how="left", suffixes=("", "_site"))

        # Technology distribution
        if "technology" in df.columns:
            tech_distribution = df["technology"].value_counts(normalize=True)
            df["technology_prevalence"] = df["technology"].map(tech_distribution)

        # Density analysis
        if "neighbor_count" in df.columns:
            df["network_density"] = pd.cut(
                df["neighbor_count"],
                bins=[0, 2, 5, 10, float("inf")],
                labels=["sparse", "low", "medium", "dense"],
            )

        return df

    def _analyze_interference(self, df: pd.DataFrame) -> pd.DataFrame:
        """Analyze potential interference between cells with optimized performance."""
        if not all(
            col in df.columns for col in ["frequency", "azimuth", "neighbor_cells"]
        ):
            df["interference_score"] = 0.0
            df["interference_level"] = "low"
            return df

        # Force enable interference analysis - skip_analysis_for_large_datasets disabled
        # if (self.ep_config.skip_analysis_for_large_datasets and 
        #     len(df) > self.ep_config.large_dataset_threshold):
        #     self.logger.warning(f"Skipping interference analysis for large dataset ({len(df)} records) to prevent performance issues")
        #     df["interference_score"] = 0.0
        #     df["interference_level"] = "low"
        #     return df
            
        # Hard limit for very large datasets
        if len(df) > self.ep_config.max_neighbor_search_records:
            self.logger.warning(f"Dataset too large ({len(df)} records) for interference analysis, skipping")
            df["interference_score"] = 0.0
            df["interference_level"] = "low"
            return df

        try:
            import numpy as np
            
            # Initialize interference scores
            interference_scores = np.zeros(len(df))
            
            # Create lookup dictionaries for faster access
            if "cell_id" not in df.columns:
                df["interference_score"] = interference_scores
                df["interference_level"] = "low"
                return df
                
            cell_id_to_idx = {cell_id: idx for idx, cell_id in enumerate(df["cell_id"])}
            
            # Vectorized calculations where possible
            frequencies = df["frequency"].fillna(0).values
            azimuths = df["azimuth"].fillna(180).values  # Default to 180 for unknown
            powers = df["power"].fillna(30).values  # Default to 30 dBm
            
            # Process each cell's interference
            for idx, row in df.iterrows():
                if not row["neighbor_cells"] or pd.isna(row["frequency"]):
                    continue
                    
                # Get neighbor indices efficiently
                neighbor_indices = []
                for neighbor_id in row["neighbor_cells"]:
                    if neighbor_id in cell_id_to_idx:
                        neighbor_indices.append(cell_id_to_idx[neighbor_id])
                        
                if not neighbor_indices:
                    continue
                    
                # Vectorized interference calculations
                neighbor_indices = np.array(neighbor_indices)
                
                # Frequency interference
                freq_diffs = np.abs(frequencies[neighbor_indices] - row["frequency"])
                freq_interference = np.maximum(0, 1 - freq_diffs / 100)
                
                # Azimuth interference
                if pd.notna(row["azimuth"]):
                    azimuth_diffs = np.abs(azimuths[neighbor_indices] - row["azimuth"])
                    azimuth_diffs = np.minimum(azimuth_diffs, 360 - azimuth_diffs)
                    azimuth_interference = 1 - azimuth_diffs / 180
                else:
                    azimuth_interference = np.full(len(neighbor_indices), 0.5)
                
                # Power interference
                if pd.notna(row["power"]):
                    power_factors = (powers[neighbor_indices] - 20) / 30
                    power_factors = np.clip(power_factors, 0, 1)
                else:
                    power_factors = np.full(len(neighbor_indices), 0.5)
                
                # Combined interference score
                cell_interferences = (
                    freq_interference * 0.5 +
                    azimuth_interference * 0.3 +
                    power_factors * 0.2
                )
                
                # Average interference score
                interference_scores[idx] = np.mean(cell_interferences)

            df["interference_score"] = interference_scores

            # Categorize interference levels
            df["interference_level"] = pd.cut(
                df["interference_score"],
                bins=[0, 0.2, 0.5, 0.8, 1.0],
                labels=["low", "medium", "high", "critical"],
            )
            
            self.logger.info(f"Completed interference analysis for {len(df)} cells using optimized vectorized calculations")

        except Exception as e:
            self.logger.warning(f"Interference analysis failed: {e}")
            df["interference_score"] = 0.0
            df["interference_level"] = "low"

        return df

    def _check_system_resources(self, data_size: int) -> Dict[str, Any]:
        """Check system resources and recommend processing strategy."""
        try:
            import psutil
            
            # Get system memory info
            memory = psutil.virtual_memory()
            available_memory_gb = memory.available / (1024**3)
            
            # Estimate memory usage (rough calculation)
            estimated_memory_gb = (data_size * 200) / (1024**3)  # ~200 bytes per record
            
            recommendations = {
                "available_memory_gb": available_memory_gb,
                "estimated_memory_gb": estimated_memory_gb,
                "memory_sufficient": available_memory_gb > estimated_memory_gb * 2,
                "recommended_batch_size": min(
                    self.ep_config.batch_size,
                    max(1000, int(available_memory_gb * 1000))  # Adjust based on available memory
                ),
                "skip_complex_analysis": False  # Force enable complex analysis
            }
            
            if not recommendations["memory_sufficient"]:
                self.logger.warning(
                    f"Limited memory available ({available_memory_gb:.1f}GB) for dataset size ({data_size} records). "
                    f"Consider processing in smaller batches or skipping complex analysis."
                )
                
            return recommendations
            
        except ImportError:
            self.logger.warning("psutil not available, using default resource settings")
            return {
                "memory_sufficient": True,
                "recommended_batch_size": self.ep_config.batch_size,
                "skip_complex_analysis": False  # Force enable complex analysis
            }
        except Exception as e:
            self.logger.warning(f"Resource check failed: {e}")
            return {
                "memory_sufficient": True,
                "recommended_batch_size": self.ep_config.batch_size,
                "skip_complex_analysis": False  # Force enable complex analysis
            }

    async def _perform_ep_analysis(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Perform comprehensive EP analysis."""
        analysis = {
            "total_records": len(data),
            "unique_sites": data["site_id"].nunique()
            if "site_id" in data.columns
            else 0,
            "unique_cells": data["cell_id"].nunique()
            if "cell_id" in data.columns
            else 0,
            "spatial_coverage": {},
            "engineering_summary": {},
            "topology_metrics": {},
            "quality_metrics": {},
        }

        # Spatial coverage analysis
        if "latitude" in data.columns and "longitude" in data.columns:
            valid_coords = data["latitude"].notna() & data["longitude"].notna()
            if valid_coords.any():
                analysis["spatial_coverage"] = {
                    "lat_range": [
                        float(data["latitude"].min()),
                        float(data["latitude"].max()),
                    ],
                    "lon_range": [
                        float(data["longitude"].min()),
                        float(data["longitude"].max()),
                    ],
                    "coverage_area_km2": float(data["coverage_area_km2"].sum())
                    if "coverage_area_km2" in data.columns
                    else None,
                    "valid_coordinates_pct": float(valid_coords.mean() * 100),
                }

        # Engineering parameters summary
        engineering_fields = ["frequency", "power", "antenna_gain", "height", "azimuth"]
        for field in engineering_fields:
            if field in data.columns:
                analysis["engineering_summary"][field] = {
                    "mean": float(data[field].mean())
                    if data[field].notna().any()
                    else None,
                    "std": float(data[field].std())
                    if data[field].notna().any()
                    else None,
                    "min": float(data[field].min())
                    if data[field].notna().any()
                    else None,
                    "max": float(data[field].max())
                    if data[field].notna().any()
                    else None,
                    "valid_pct": float(data[field].notna().mean() * 100),
                }

        # Topology metrics
        if "neighbor_count" in data.columns:
            analysis["topology_metrics"] = {
                "avg_neighbors": float(data["neighbor_count"].mean()),
                "max_neighbors": int(data["neighbor_count"].max()),
                "network_density_distribution": data["network_density"]
                .value_counts()
                .to_dict()
                if "network_density" in data.columns
                else {},
            }

        # Quality metrics
        validation_fields = [col for col in data.columns if col.endswith("_valid")]
        if validation_fields:
            quality_scores = []
            for field in validation_fields:
                if data[field].notna().any():
                    quality_scores.append(data[field].mean())

            if quality_scores:
                analysis["quality_metrics"] = {
                    "overall_quality_score": float(np.mean(quality_scores) * 100),
                    "parameter_validity": {
                        field: float(data[field].mean() * 100)
                        for field in validation_fields
                        if data[field].notna().any()
                    },
                }

        # Interference analysis
        if "interference_score" in data.columns:
            analysis["interference_analysis"] = {
                "avg_interference": float(data["interference_score"].mean()),
                "high_interference_cells": int(
                    (data["interference_score"] > 0.7).sum()
                ),
                "interference_distribution": data["interference_level"]
                .value_counts()
                .to_dict()
                if "interference_level" in data.columns
                else {},
            }

        return analysis

    def set_database_context(self, pool=None, db_manager=None, db_ops=None, schema_manager=None):
        """Set database context for the importer.

        Args:
            pool: Database connection pool
            db_manager: Database manager instance
            db_ops: Database operations wrapper
            schema_manager: Schema manager instance
        """
        self.pool = pool
        self.db_manager = db_manager
        self.db_ops = db_ops
        self.schema_manager = schema_manager

        # Log successful database context setup
        self.logger.info("Database context set for EP importer")

    async def _store_ep_data(self, data: pd.DataFrame) -> None:
        """Store EP data in database with spatial indexing."""
        if not hasattr(self, "db_ops") or not self.db_ops:
            self.logger.warning("Database operations not configured, skipping data storage")
            return

        # Generate table name based on source file and data type
        source_path = getattr(self, 'source_path', None)
        if source_path:
            from pathlib import Path
            source_file = Path(source_path)
            # Extract year and week from filename or path
            filename = source_file.stem.upper()

            # Determine cell type and generate appropriate table name
            if 'GSMCELL' in filename:
                table_name = self._generate_table_name_from_path(source_path, 'gsm')
            elif 'LTECELL' in filename:
                table_name = self._generate_table_name_from_path(source_path, 'lte')
            elif 'NRCELL' in filename:
                table_name = self._generate_table_name_from_path(source_path, 'nr')
            elif 'TEF_SITE' in filename or 'TEF_Sites' in filename:
                table_name = self._generate_table_name_from_path(source_path, 'site')
            else:
                table_name = f"ep_data_{datetime.now().strftime('%Y_cw%U')}"
        else:
            table_name = f"ep_data_{datetime.now().strftime('%Y_cw%U')}"

        try:
            # Store in database with spatial support
            await self.db_ops.store_dataframe(
                data,
                table_name,
                if_exists="append",
                spatial_column="geometry" if "geometry" in data.columns else None,
            )

            self.logger.info(f"Successfully stored {len(data)} records to table {table_name}")

            # Create spatial index if supported
            if hasattr(self.db_ops, "create_spatial_index") and "geometry" in data.columns:
                await self.db_ops.create_spatial_index(table_name, "geometry")

        except Exception as e:
            self.logger.error(f"Failed to store EP data: {e}")
            raise

    def _generate_table_name_from_path(self, source_path: str, cell_type: str) -> str:
        """Generate table name based on source path and cell type.

        Args:
            source_path: Path to source file
            cell_type: Type of cell (gsm, lte, nr, site)

        Returns:
            Generated table name
        """
        from pathlib import Path
        import re

        path = Path(source_path)

        # Extract year from path (e.g., 2024)
        year_match = re.search(r'20\d{2}', str(path))
        year = year_match.group() if year_match else datetime.now().strftime('%Y')

        # Extract week from path (e.g., CW01)
        week_match = re.search(r'CW(\d{2})', str(path), re.IGNORECASE)
        if week_match:
            week = f"cw{week_match.group(1)}"
        else:
            week = f"cw{datetime.now().strftime('%U').zfill(2)}"

        return f"ep_{cell_type}_{year}_{week}"

    async def validate_source(self, source: Union[str, Path]) -> bool:
        """Validate EP data source.

        Args:
            source: Data source to validate

        Returns:
            True if source is valid
        """
        try:
            source_path = Path(source)

            # Check if file exists
            if not source_path.exists():
                self.logger.error(f"Source file does not exist: {source}")
                return False

            # Check file format
            if source_path.suffix.lower() not in self.supported_formats:
                self.logger.error(f"Unsupported file format: {source_path.suffix}")
                return False

            # Check file size (basic validation)
            file_size_mb = source_path.stat().st_size / 1024 / 1024
            if file_size_mb > 1000:  # 1GB limit for EP files
                self.logger.warning(f"Large EP file detected: {file_size_mb:.1f}MB")

            return True

        except Exception as e:
            self.logger.error(f"Source validation failed: {e}")
            return False

    async def get_source_info(self, source: Union[str, Path]) -> Dict[str, Any]:
        """Get information about EP data source.

        Args:
            source: Data source

        Returns:
            Source information dictionary
        """
        try:
            source_path = Path(source)
            stat = source_path.stat()

            return {
                "file_path": str(source_path),
                "file_name": source_path.name,
                "file_size_bytes": stat.st_size,
                "file_size_mb": stat.st_size / 1024 / 1024,
                "file_format": source_path.suffix.lower(),
                "modified_time": datetime.fromtimestamp(stat.st_mtime),
                "is_supported": source_path.suffix.lower() in self.supported_formats,
                "estimated_records": self._estimate_record_count(source_path),
                "network_type": self.ep_config.network_type,
                "operator": self.ep_config.operator,
                "is_spatial": source_path.suffix.lower() in [".kml", ".shp"],
            }

        except Exception as e:
            self.logger.error(f"Failed to get source info: {e}")
            return {"error": str(e)}

    def _estimate_record_count(self, file_path: Path) -> Optional[int]:
        """Estimate number of records in file."""
        try:
            file_size_mb = file_path.stat().st_size / 1024 / 1024

            # Rough estimation based on file size and format
            if file_path.suffix.lower() == ".csv":
                # Assume ~300 bytes per EP record on average (more fields than CDR/KPI)
                return int(file_size_mb * 1024 * 1024 / 300)
            elif file_path.suffix.lower() in [".xlsx", ".xls"]:
                # Excel files are more compressed
                return int(file_size_mb * 1024 * 1024 / 250)
            elif file_path.suffix.lower() in [".kml", ".shp"]:
                # Spatial files vary widely
                return int(file_size_mb * 1024 * 1024 / 500)
            else:
                return None

        except Exception:
            return None

    def get_operator_schema(self, operator: str) -> Dict[str, str]:
        """Get column schema for specific operator.

        Args:
            operator: Operator name

        Returns:
            Column mapping dictionary
        """
        return self.OPERATOR_SCHEMAS.get(operator, self.OPERATOR_SCHEMAS["generic"])

    def get_supported_operators(self) -> List[str]:
        """Get list of supported operators.

        Returns:
            List of supported operator names
        """
        return list(self.OPERATOR_SCHEMAS.keys())

    def get_network_parameters(
        self, network_type: str
    ) -> Dict[str, Tuple[float, float]]:
        """Get engineering parameter ranges for specific network type.

        Args:
            network_type: Network type (2G, 3G, 4G, 5G)

        Returns:
            Parameter ranges dictionary
        """
        return self.NETWORK_PARAMETERS.get(network_type, self.NETWORK_PARAMETERS["4G"])

    def calculate_distance(
        self, lat1: float, lon1: float, lat2: float, lon2: float
    ) -> float:
        """Calculate distance between two points using Haversine formula.

        Args:
            lat1, lon1: First point coordinates
            lat2, lon2: Second point coordinates

        Returns:
            Distance in meters
        """
        # Convert to radians
        lat1, lon1, lat2, lon2 = map(math.radians, [lat1, lon1, lat2, lon2])

        # Haversine formula
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = (
            math.sin(dlat / 2) ** 2
            + math.cos(lat1) * math.cos(lat2) * math.sin(dlon / 2) ** 2
        )
        c = 2 * math.asin(math.sqrt(a))

        # Earth radius in meters
        r = 6371000

        return c * r


if __name__ == "__main__":
    import argparse
    import sys
    import asyncio
    
    async def main():
        parser = argparse.ArgumentParser(description="EP Importer CLI")
        parser.add_argument("file_path", help="Path to EP data file to import")
        parser.add_argument("--operator", default="generic", help="Operator name (default: generic)")
        parser.add_argument("--network-type", default="4G", help="Network type (default: 4G)")
        parser.add_argument("--version", action="version", version="EP Importer 1.0.0")
        
        args = parser.parse_args()
        
        print(f"EP Importer CLI - Starting import of {args.file_path}")
        
        try:
            # Add the project root to Python path
            import os
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
            if project_root not in sys.path:
                sys.path.insert(0, project_root)
            
            # Create EP importer instance
            from src.config.models import EPConfig
            from src.importers.base.abstract_importer import ImporterConfig
            
            # Create importer configuration
            importer_config = ImporterConfig(
                name='ep_importer',
                data_type='ep',
                supported_formats=['csv', 'xlsx'],
                batch_size=5000,
                geospatial_enabled=True
            )
            
            # Create EP-specific configuration
            ep_config = EPConfig(
                batch_size=5000,
                table_prefix="ep_",
                signal_analysis=True,
                coverage_analysis=True
            )
            
            importer = EPImporter(config=importer_config, ep_config=ep_config.model_dump())
            
            # Import the file
            result = await importer.import_data(args.file_path)
            
            # Print results
            print(f"\nImport Result:")
            print(f"Status: {result.status}")
            if result.error_message:
                print(f"Error: {result.error_message}")
            
            if result.metadata:
                metadata = result.metadata
                print(f"Records imported: {metadata.get('records_imported', 0)}")
                print(f"Records failed: {metadata.get('records_failed', 0)}")
                print(f"Processing time: {metadata.get('processing_time', 0):.2f}s")
                print(f"Data quality score: {metadata.get('data_quality_score', 0):.2%}")
                
                if metadata.get('warnings'):
                    print(f"\nWarnings:")
                    for warning in metadata['warnings']:
                        print(f"  - {warning}")
                        
                if metadata.get('errors'):
                    print(f"\nErrors:")
                    for error in metadata['errors']:
                        print(f"  - {error}")
            
            print(f"\nImport completed successfully!")
            
        except Exception as e:
            print(f"\nImport failed: {e}")
            sys.exit(1)
    
    # Run the async main function
    asyncio.run(main())
