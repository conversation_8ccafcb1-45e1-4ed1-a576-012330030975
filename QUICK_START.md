# Connect命令快速使用指南

## 问题背景
如果你遇到 `ModuleNotFoundError: No module named 'ijson'` 错误，这是因为环境隔离问题导致的。

## 解决方案（推荐使用顺序）

### 1. 使用PowerShell脚本（最推荐）
```powershell
# 基本用法
.\run_connect.ps1 --help
.\run_connect.ps1 import data/input/ep
.\run_connect.ps1 import data/input/kpi
.\run_connect.ps1 export --format csv
```

### 2. 使用CMD脚本
```cmd
# 基本用法
.\run_connect.cmd --help
.\run_connect.cmd import data/input/ep
.\run_connect.cmd import data/input/kpi
```

### 3. 直接使用Poetry（备选）
```bash
poetry run connect --help
poetry run connect import data/input/ep
```

## 脚本特性
- ✅ 自动环境验证
- ✅ 智能错误处理
- ✅ 详细执行信息
- ✅ 故障排除提示
- ✅ 完整参数传递

## 快速验证
```powershell
# 验证ijson模块
poetry run python -c "import ijson; print(f'ijson version: {ijson.__version__}')"

# 验证connect命令
.\run_connect.ps1 --help
```

## 常见问题
1. **Poetry未安装**: 安装Poetry后重试
2. **权限问题**: 以管理员身份运行PowerShell
3. **环境损坏**: 运行 `poetry env remove python && poetry install`

## 最佳实践
- 始终使用便捷脚本或 `poetry run`
- 避免直接使用 `connect` 命令
- 定期更新依赖: `poetry update`

---
**注意**: 详细的解决方案和故障排除请参考 `IJSON_SOLUTION.md`