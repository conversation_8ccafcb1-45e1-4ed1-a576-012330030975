"""NLG (Network Location Geography) importer with advanced geospatial capabilities.

This module provides specialized NLG data import functionality with support for
geospatial analysis, coverage optimization, and network planning.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""
import asyncio
import logging
from typing import Any, Dict, List, Optional, Union, Tuple
from pathlib import Path
from datetime import datetime
import math

import pandas as pd
import numpy as np
from pydantic import BaseModel, Field, ConfigDict

from ..base import (
    AbstractImporter,
    ValidationMixin,
    ProcessingMixin,
    PerformanceMixin,
    ImporterConfig
)
from ..base.abstract_importer import ImportResult, ImportStatus, ImportMetrics


class NLGConfig(BaseModel):
    """Configuration specific to NLG data import."""
    # Operator-specific settings
    operator: str = Field(default="generic", description="Telecom operator (telefonica, vodafone, telekom, etc.)")
    network_type: str = Field(default="4G", description="Network type (2G, 3G, 4G, 5G)")
    location_type: str = Field(default="all", description="Location type filter (outdoor, indoor, mixed)")
    
    # Geospatial settings
    coordinate_system: str = Field(default="EPSG:4326", description="Coordinate reference system")
    enable_spatial_validation: bool = Field(default=True, description="Enable spatial coordinate validation")
    enable_coverage_analysis: bool = Field(default=True, description="Enable coverage analysis")
    enable_quality_mapping: bool = Field(default=True, description="Enable signal quality mapping")
    
    # Location validation
    enable_location_validation: bool = Field(default=True, description="Enable location parameter validation")
    enable_signal_validation: bool = Field(default=True, description="Enable signal quality validation")
    enable_mobility_analysis: bool = Field(default=True, description="Enable mobility pattern analysis")
    
    # Processing optimization
    enable_spatial_clustering: bool = Field(default=True, description="Enable spatial clustering")
    enable_hotspot_detection: bool = Field(default=True, description="Enable hotspot detection")
    enable_coverage_gaps: bool = Field(default=True, description="Enable coverage gap analysis")
    
    # Performance settings
    batch_size: int = Field(default=1000, description="Batch size for NLG processing (optimized for memory efficiency)")
    clustering_radius_meters: float = Field(default=100.0, description="Clustering radius for location grouping (meters)")
    hotspot_threshold: float = Field(default=0.8, description="Signal quality threshold for hotspot detection")
    coverage_threshold: float = Field(default=-100.0, description="Minimum signal strength for coverage (dBm)")
    
    # Memory and disk management
    max_memory_usage_mb: int = Field(default=4096, description="Maximum memory usage in MB")
    min_disk_space_gb: float = Field(default=2.0, description="Minimum required disk space in GB")
    enable_memory_monitoring: bool = Field(default=False, description="Enable memory usage monitoring")
    enable_disk_monitoring: bool = Field(default=True, description="Enable disk space monitoring")
    memory_cleanup_threshold_mb: int = Field(default=3072, description="Memory cleanup threshold in MB (3GB)")
    memory_critical_threshold_mb: int = Field(default=6144, description="Critical memory threshold in MB (6GB)")
    
    # Quality thresholds
    max_error_rate: float = Field(default=0.01, description="Maximum acceptable error rate")
    min_data_quality_score: float = Field(default=0.95, description="Minimum data quality score")
    
    model_config = ConfigDict(
        extra="allow"
    )
class NLGImporter(AbstractImporter, ValidationMixin, ProcessingMixin, PerformanceMixin):
    """Enhanced NLG importer with geospatial and network analysis.
    
    This importer provides comprehensive NLG data processing with support for:
    - Multiple operator formats and network types
    - Advanced geospatial processing and validation
    - Signal quality analysis and coverage mapping
    - Mobility pattern analysis and hotspot detection
    - Performance optimization for large location datasets
    """
    
    # Standard NLG column mappings for different operators
    OPERATOR_SCHEMAS = {
        'telefonica': {
            'location_id': 'ubicacion_id',
            'latitude': 'latitud',
            'longitude': 'longitud',
            'altitude': 'altitud',
            'timestamp': 'fecha_hora',
            'signal_strength': 'intensidad_senal',
            'signal_quality': 'calidad_senal',
            'cell_id': 'celda_id',
            'sector_id': 'sector_id',
            'technology': 'tecnologia',
            'operator_name': 'operador',
            'location_type': 'tipo_ubicacion',
            'mobility_state': 'estado_movilidad',
            'speed': 'velocidad',
            'direction': 'direccion'
        },
        'vodafone': {
            'location_id': 'loc_id',
            'latitude': 'lat',
            'longitude': 'lon',
            'altitude': 'alt',
            'timestamp': 'timestamp',
            'signal_strength': 'rssi',
            'signal_quality': 'rsrq',
            'cell_id': 'cell_id',
            'sector_id': 'sector',
            'technology': 'tech',
            'operator_name': 'operator',
            'location_type': 'loc_type',
            'mobility_state': 'mobility',
            'speed': 'speed_kmh',
            'direction': 'bearing'
        },
        'telekom': {
            'location_id': 'standort_id',
            'latitude': 'breitengrad',
            'longitude': 'laengengrad',
            'altitude': 'hoehe',
            'timestamp': 'zeitstempel',
            'signal_strength': 'signalstaerke',
            'signal_quality': 'signalqualitaet',
            'cell_id': 'zellen_id',
            'sector_id': 'sektor_id',
            'technology': 'technologie',
            'operator_name': 'betreiber',
            'location_type': 'standorttyp',
            'mobility_state': 'mobilitaetszustand',
            'speed': 'geschwindigkeit',
            'direction': 'richtung'
        },
        'generic': {
            'location_id': 'location_id',
            'latitude': 'latitude',
            'longitude': 'longitude',
            'altitude': 'altitude',
            'timestamp': 'timestamp',
            'signal_strength': 'signal_strength',
            'signal_quality': 'signal_quality',
            'cell_id': 'cell_id',
            'sector_id': 'sector_id',
            'technology': 'technology',
            'operator_name': 'operator_name',
            'location_type': 'location_type',
            'mobility_state': 'mobility_state',
            'speed': 'speed',
            'direction': 'direction'
        }
    }
    
    # Signal quality thresholds by network type
    SIGNAL_THRESHOLDS = {
        '2G': {
            'signal_strength_range': (-110, -50),  # dBm
            'excellent_threshold': -70,
            'good_threshold': -85,
            'fair_threshold': -95,
            'poor_threshold': -105
        },
        '3G': {
            'signal_strength_range': (-120, -60),
            'excellent_threshold': -80,
            'good_threshold': -90,
            'fair_threshold': -100,
            'poor_threshold': -110
        },
        '4G': {
            'signal_strength_range': (-140, -70),
            'excellent_threshold': -90,
            'good_threshold': -105,
            'fair_threshold': -115,
            'poor_threshold': -125
        },
        '5G': {
            'signal_strength_range': (-140, -60),
            'excellent_threshold': -80,
            'good_threshold': -95,
            'fair_threshold': -110,
            'poor_threshold': -120
        }
    }
    
    # Mobility state classifications
    MOBILITY_STATES = {
        'stationary': {'speed_range': (0, 3), 'description': 'Not moving or very slow'},
        'pedestrian': {'speed_range': (3, 10), 'description': 'Walking speed'},
        'cycling': {'speed_range': (10, 25), 'description': 'Bicycle speed'},
        'automotive': {'speed_range': (25, 120), 'description': 'Vehicle speed'},
        'high_speed': {'speed_range': (120, 300), 'description': 'High-speed transport'}
    }
    
    def __init__(self, config: Optional[Union[ImporterConfig, Dict[str, Any]]] = None, **kwargs):
        """Initialize NLG importer.
        
        Args:
            config: Importer configuration
            **kwargs: Additional configuration parameters
        """
        super().__init__(config=config, **kwargs)
        
        # NLG-specific configuration
        nlg_config_dict = kwargs.get('nlg_config', {})
        if isinstance(nlg_config_dict, NLGConfig):
            self.nlg_config = nlg_config_dict
        else:
            self.nlg_config = NLGConfig(**nlg_config_dict)
            
        # Set data type
        self.data_type = 'NLG'
        self.name = 'NLG Importer'
        self.supported_formats = ['.csv', '.xlsx', '.xls', '.xlsb', '.json', '.gpx', '.kml']
        
        # Required columns (will be mapped based on operator)
        self.required_columns = [
            'location_id',
            'latitude',
            'longitude',
            'timestamp'
        ]
        
        # Configure processing for NLG data with memory optimization
        self.configure_processing({
            'batch_size': self.nlg_config.batch_size,
            'enable_parallel': True,
            'memory_limit_mb': self.nlg_config.max_memory_usage_mb,
            'use_polars': False  # Pandas for better geospatial handling
        })
        
        # Configure validation for NLG data
        self.configure_validation({
            'enable_strict_validation': True,
            'max_error_rate': self.nlg_config.max_error_rate,
            'enable_spatial_validation': self.nlg_config.enable_spatial_validation,
            'coordinate_system': self.nlg_config.coordinate_system
        })
        
        # Configure performance monitoring
        self.configure_performance({
            'enable_monitoring': True,
            'enable_telecom_optimizations': True,
            'nlg_batch_size': self.nlg_config.batch_size
        })

    def set_database_context(self, pool=None, db_manager=None, db_ops=None, schema_manager=None):
        """Set database context for the importer.

        This method is called by ImportManager to provide database operations.
        """
        if pool:
            self.pool_manager = pool
        if db_manager:
            self.db_manager = db_manager
        if db_ops:
            self.db_ops = db_ops
        if schema_manager:
            self.schema_manager = schema_manager

        self.logger.info("Database context set for NLG importer")

        # Verify db_ops is properly set
        if self.db_ops:
            self.logger.info("✅ Database operations (db_ops) successfully configured")
        else:
            self.logger.warning("❌ Database operations (db_ops) not configured")

    def get_table_name(self, filename: str) -> str:
        """Generate table name for NLG data using enhanced naming conventions.

        Args:
            filename: Name of the source file or full path

        Returns:
            str: Table name following pattern nlg_cube_aktuell_{date} (YYYY-MM-DD format)
        """
        try:
            from src.database.utils.table_naming import TableNamingManager

            # Get configuration
            config = getattr(self, 'config', {})

            naming_manager = TableNamingManager(config)

            # Convert filename to Path object (handle both filename and full path)
            file_path = Path(filename)

            # Generate table name using standardized logic
            # Pattern: nlg_cube_aktuell_{date}
            table_name = naming_manager.generate_table_name(
                data_type="nlg",
                file_path=file_path
            )

            self.logger.info(f"Generated NLG table name '{table_name}' for file '{filename}'")
            return table_name

        except Exception as e:
            self.logger.warning(f"Failed to generate table name using pattern, falling back to simple naming: {e}")
            return self._generate_nlg_table_name_fallback(filename)

    def _generate_nlg_table_name_fallback(self, filename: str) -> str:
        """Fallback NLG table name generation with enhanced date parsing."""
        import re
        from pathlib import Path
        from datetime import datetime

        file_path = Path(filename)
        filename_only = file_path.stem

        self.logger.debug(f"Extracting date from NLG filename: {filename_only}")

        # Pattern 1: YYYY-MM-DD format (already correct)
        pattern1 = r'(\d{4}-\d{2}-\d{2})'
        match1 = re.search(pattern1, filename_only)
        if match1:
            date_str = match1.group(1)
            self.logger.debug(f"Found YYYY-MM-DD format date: {date_str}")
            table_name = f"nlg_cube_aktuell_{date_str}"
            self.logger.info(f"Using fallback NLG table name: '{table_name}'")
            return table_name

        # Pattern 2: YYYY_MM_DD format (underscores)
        pattern2 = r'(\d{4})_(\d{2})_(\d{2})'
        match2 = re.search(pattern2, filename_only)
        if match2:
            year, month, day = match2.groups()
            date_str = f"{year}-{month}-{day}"
            table_name = f"nlg_cube_aktuell_{date_str}"
            self.logger.info(f"Using fallback NLG table name: '{table_name}'")
            return table_name

        # Pattern 3: YYYY MM DD format (spaces)
        pattern3 = r'(\d{4})\s+(\d{2})\s+(\d{2})'
        match3 = re.search(pattern3, filename_only)
        if match3:
            year, month, day = match3.groups()
            date_str = f"{year}-{month}-{day}"
            table_name = f"nlg_cube_aktuell_{date_str}"
            self.logger.info(f"Using fallback NLG table name: '{table_name}'")
            return table_name

        # Pattern 4: YYYYMMDD format (compact)
        pattern4 = r'(\d{4})(\d{2})(\d{2})'
        match4 = re.search(pattern4, filename_only)
        if match4:
            year, month, day = match4.groups()
            date_str = f"{year}-{month}-{day}"
            table_name = f"nlg_cube_aktuell_{date_str}"
            self.logger.info(f"Using fallback NLG table name: '{table_name}'")
            return table_name

        # Pattern 5: Handle hyphen-separated YYYYMMDD (e.g., -20250506)
        pattern5 = r'-(\d{4})(\d{2})(\d{2})'
        match5 = re.search(pattern5, filename_only)
        if match5:
            year, month, day = match5.groups()
            date_str = f"{year}-{month}-{day}"
            table_name = f"nlg_cube_aktuell_{date_str}"
            self.logger.info(f"Using fallback NLG table name: '{table_name}'")
            return table_name

        # Fallback: use current date
        current_date = datetime.now().strftime('%Y-%m-%d')
        table_name = f"nlg_cube_aktuell_{current_date}"
        self.logger.warning(f"No date pattern found in filename '{filename_only}', using current date: {table_name}")
        return table_name
        
        self.logger.info(f"NLG Importer initialized for operator: {self.nlg_config.operator}, network: {self.nlg_config.network_type}")
        
    async def import_data(self, source: Union[str, Path], **kwargs) -> ImportResult:
        """Import NLG data with comprehensive processing.
        
        Args:
            source: Data source (file path or connection string)
            **kwargs: Additional import parameters
            
        Returns:
            Import result with metrics and status
        """
        start_time = datetime.now()
        
        try:
            # Check system resources before starting
            resource_check = await self._check_system_resources()
            if not resource_check['sufficient']:
                return ImportResult(
                    success=False,
                    records_imported=0,
                    records_failed=0,
                    source_path=str(source),
                    error_message=f"Insufficient system resources: {resource_check['message']}",
                    processing_time=0.0,
                    file_size_bytes=0,
                    metadata={"errors": [resource_check['message']]}
                )
            
            # Start performance monitoring
            await self.start_performance_monitoring()
            
            with self.track_operation("nlg_import", source=str(source)) as op_metrics:
                # Validate source
                if not await self.validate_source(source):
                    metrics = ImportMetrics()
                    metrics.records_processed = 0
                    metrics.records_failed = 0
                    metrics.processing_time = 0.0
                    return ImportResult(
                        status=ImportStatus.FAILED,
                        metrics=metrics,
                        error_message="Source validation failed",
                        metadata={"errors": ["Invalid or inaccessible data source"]},
                        source_info={"path": str(source)}
                    )
                    
                # Load and process data
                data = await self.process_data_async(
                    source,
                    self._process_nlg_batch,
                    **kwargs
                )

                # Handle duplicate column names immediately after loading
                if data is not None and hasattr(data, 'columns') and data.columns.duplicated().any():
                    self.logger.warning("Duplicate column names detected after data loading, renaming...")
                    # Create unique column names
                    cols = data.columns.tolist()
                    seen = {}
                    for i, col in enumerate(cols):
                        if col in seen:
                            seen[col] += 1
                            cols[i] = f"{col}_{seen[col]}"
                        else:
                            seen[col] = 0
                    data.columns = cols
                    self.logger.info(f"Renamed duplicate columns: {len([c for c in cols if '_' in c and c.split('_')[-1].isdigit()])} duplicates found")
                
                if data is None or len(data) == 0:
                    metrics = ImportMetrics()
                    metrics.records_processed = 0
                    metrics.records_failed = 0
                    metrics.processing_time = (datetime.now() - start_time).total_seconds()
                    return ImportResult(
                        status=ImportStatus.FAILED,
                        metrics=metrics,
                        error_message="No data loaded or processed",
                        metadata={},
                        source_info={"path": str(source)}
                    )
                    
                # Update operation metrics
                op_metrics.records_processed = len(data)
                op_metrics.bytes_processed = data.memory_usage(deep=True).sum()
                
                # Validate processed data
                validation_result = await self.validate_telecom_data(data, 'NLG')

                # Handle validation result (dict format from validation_mixin)
                is_valid = validation_result.get('valid', True)
                has_warnings = validation_result.get('has_warnings', False)
                summary = validation_result.get('summary', {})
                results = validation_result.get('results', {})
                
                # Calculate error rate from validation results
                total_errors = summary.get('errors', 0)
                total_warnings = summary.get('warnings', 0)
                error_rate = (total_errors + total_warnings) / len(data) if len(data) > 0 else 0.0
                
                # Extract errors list
                errors = results.get('errors', []) + results.get('warnings', [])
                
                # Calculate quality score (1.0 - error_rate)
                quality_score = max(0.0, 1.0 - error_rate)

                if not is_valid:
                    if error_rate > self.nlg_config.max_error_rate:
                        metrics = ImportMetrics()
                        metrics.records_processed = len(data)
                        metrics.records_failed = len(data)
                        metrics.processing_time = 0
                        return ImportResult(
                            status=ImportStatus.FAILED,
                            metrics=metrics,
                            error_message=f"Data quality below threshold: {error_rate:.2%}",
                            metadata={"errors": errors[:10]},
                            source_info={"path": str(source)}
                        )
                    else:
                        self.logger.warning(f"Data quality issues detected: {error_rate:.2%}")
                        
                # Perform NLG-specific analysis
                analysis_results = await self._perform_nlg_analysis(data)
                
                # Store data if database operations are available
                if hasattr(self, 'db_ops') and self.db_ops:
                    try:
                        # Set current source path for table naming
                        self.current_source_path = source
                        await self._store_nlg_data(data)
                        self.logger.info(f"Successfully stored {len(data)} NLG records to database")
                    except Exception as e:
                        self.logger.error(f"Database storage failed: {e}")
                        import traceback
                        traceback.print_exc()
                        metrics = ImportMetrics()
                        metrics.records_processed = len(data)
                        metrics.records_failed = len(data)
                        metrics.processing_time = (datetime.now() - start_time).total_seconds()
                        return ImportResult(
                            status=ImportStatus.FAILED,
                            metrics=metrics,
                            error_message=f"Database storage failed: {e}",
                            metadata={"errors": [str(e)]},
                            source_info={"path": str(source)}
                        )
                else:
                    self.logger.warning("Database operations not available - data not stored to database")
                        
                # Calculate processing time
                processing_time = (datetime.now() - start_time).total_seconds()

                # Create successful import result
                metrics = ImportMetrics()
                metrics.records_processed = len(data)
                metrics.records_imported = len(data)
                metrics.records_failed = 0
                metrics.processing_time = processing_time

                return ImportResult(
                    status=ImportStatus.COMPLETED,
                    metrics=metrics,
                    error_message=None,
                    metadata={
                        'data_quality_score': quality_score,
                        'warnings': results.get('warnings', [])[:5],
                        'analysis_results': analysis_results
                    },
                    source_info={"path": str(source)}
                )
                
        except Exception as e:
            self.logger.error(f"NLG import failed: {e}")
            metrics = ImportMetrics()
            metrics.records_processed = 0
            metrics.records_failed = 0
            metrics.processing_time = 0
            return ImportResult(
                status=ImportStatus.FAILED,
                metrics=metrics,
                error_message=f"Import failed: {e}",
                metadata={"errors": [str(e)]},
                source_info={"path": str(source) if 'source' in locals() else ''}
            )
            
        finally:
            await self.stop_performance_monitoring()
            
    async def _process_nlg_batch(self, batch: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """Process a batch of NLG data with enhanced memory management.
        
        Args:
            batch: Batch of NLG data
            **kwargs: Additional processing parameters
            
        Returns:
            Processed batch
        """
        import gc
        
        try:
            # Check system resources before processing
            if self.nlg_config.enable_disk_monitoring:
                try:
                    import psutil
                    import os
                    # Use absolute path to avoid Windows path format issues
                    current_dir = os.path.abspath(os.getcwd())
                    disk = psutil.disk_usage(current_dir)
                    free_gb = float(disk.free) / (1024**3)
                    if free_gb < 0.5:  # Less than 500MB free
                        raise Exception(f"Insufficient disk space: {free_gb:.2f}GB free")
                except (ImportError, Exception) as e:
                    if isinstance(e, ImportError):
                        pass
                    else:
                        self.logger.warning(f"Could not check disk space: {e}")
            
            # Monitor memory usage before processing
            if self.nlg_config.enable_memory_monitoring:
                current_memory = self._monitor_memory_usage()
                if current_memory > self.nlg_config.memory_critical_threshold_mb:
                    raise MemoryError(f"Critical memory usage: {current_memory:.0f}MB exceeds {self.nlg_config.memory_critical_threshold_mb}MB threshold")
                elif current_memory > self.nlg_config.memory_cleanup_threshold_mb:
                    self.logger.warning(f"High memory usage detected: {current_memory:.0f}MB, performing cleanup")
                    self._cleanup_memory()
                    current_memory = self._monitor_memory_usage()
                    if current_memory > self.nlg_config.memory_critical_threshold_mb:
                        raise MemoryError(f"Memory usage still critical after cleanup: {current_memory:.0f}MB")
            
            # Apply operator-specific column mapping
            batch = self._map_operator_columns(batch)
            self._check_memory_and_cleanup("after column mapping")
            
            # Standardize data types
            batch = self._standardize_data_types(batch)
            self._check_memory_and_cleanup("after type conversion")
            
            # Process timestamps
            batch = self._process_timestamps(batch)
            self._check_memory_and_cleanup("after timestamp processing")
            
            # Process geospatial data
            batch = self._process_geospatial_data(batch)
            self._check_memory_and_cleanup("after geospatial processing")
            
            # Process signal quality data
            batch = self._process_signal_data(batch)
            self._check_memory_and_cleanup("after signal processing")
            
            # Analyze mobility patterns
            if self.nlg_config.enable_mobility_analysis:
                batch = self._analyze_mobility_patterns(batch)
                self._check_memory_and_cleanup("after mobility analysis")
                
            # Perform spatial clustering if enabled
            if self.nlg_config.enable_spatial_clustering:
                batch = await self._perform_spatial_clustering(batch)
                self._check_memory_and_cleanup("after spatial clustering")
                
            # Detect hotspots if enabled
            if self.nlg_config.enable_hotspot_detection:
                batch = self._detect_hotspots(batch)
                self._check_memory_and_cleanup("after hotspot detection")
                
            # Analyze coverage gaps if enabled
            if self.nlg_config.enable_coverage_gaps:
                batch = self._analyze_coverage_gaps(batch)
                self._check_memory_and_cleanup("after coverage analysis")
                
            # Handle duplicate column names before adding metadata
            if batch.columns.duplicated().any():
                self.logger.warning("Duplicate column names detected, renaming...")
                # Create unique column names
                cols = batch.columns.tolist()
                seen = {}
                for i, col in enumerate(cols):
                    if col in seen:
                        seen[col] += 1
                        cols[i] = f"{col}_{seen[col]}"
                    else:
                        seen[col] = 0
                batch.columns = cols
                self.logger.info(f"Renamed duplicate columns: {batch.columns.tolist()}")

            # Add metadata
            batch = batch.copy()  # Create explicit copy to avoid SettingWithCopyWarning
            batch['import_timestamp'] = datetime.now()
            batch['operator'] = self.nlg_config.operator
            batch['network_type'] = self.nlg_config.network_type
            batch['data_source'] = 'nlg_import'
            
            # Final cleanup
            gc.collect()
            
            return batch
            
        except MemoryError as e:
            self.logger.error(f"Memory error processing NLG batch: {str(e)}")
            self._cleanup_memory()
            raise
        except Exception as e:
            self.logger.error(f"NLG batch processing failed: {e}")
            # Clean up on error
            gc.collect()
            raise
            
    def _map_operator_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """Map operator-specific column names to standard names."""
        schema = self.OPERATOR_SCHEMAS.get(self.nlg_config.operator, self.OPERATOR_SCHEMAS['generic'])
        
        # Create reverse mapping (operator columns to standard columns)
        column_mapping = {v: k for k, v in schema.items() if v in df.columns}
        
        if column_mapping:
            df = df.rename(columns=column_mapping)
            self.logger.info(f"Mapped {len(column_mapping)} columns for operator {self.nlg_config.operator}")
            
        # Handle duplicate column names by adding counter suffix
        clean_columns = []
        for col in df.columns:
            clean_name = str(col).strip().lower()
            
            # Ensure uniqueness by adding counter for duplicates
            original_clean_name = clean_name
            counter = 1
            while clean_name in clean_columns:
                clean_name = f'{original_clean_name}_{counter}'
                counter += 1
            
            clean_columns.append(clean_name)
        
        df.columns = clean_columns
        
        return df
        
    def _standardize_data_types(self, df: pd.DataFrame) -> pd.DataFrame:
        """Standardize data types for NLG fields."""
        # Location and cell IDs as strings
        for col in ['location_id', 'cell_id', 'sector_id']:
            if col in df.columns:
                df[col] = df[col].astype(str).str.strip()
                
        # Coordinates as float
        for col in ['latitude', 'longitude', 'altitude']:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
                
        # Signal measurements as float
        signal_fields = ['signal_strength', 'signal_quality', 'speed']
        for field in signal_fields:
            if field in df.columns:
                df[field] = pd.to_numeric(df[field], errors='coerce')
                
        # Direction as float (0-360 degrees)
        if 'direction' in df.columns:
            df['direction'] = pd.to_numeric(df['direction'], errors='coerce')
            df['direction'] = df['direction'] % 360  # Normalize to 0-360
            
        return df
        
    def _process_timestamps(self, df: pd.DataFrame) -> pd.DataFrame:
        """Process timestamp data."""
        if 'timestamp' in df.columns:
            # Convert to datetime
            df['timestamp'] = pd.to_datetime(df['timestamp'], errors='coerce')
            
            # Extract time components for analysis
            df['hour'] = df['timestamp'].dt.hour
            df['day_of_week'] = df['timestamp'].dt.dayofweek
            df['is_weekend'] = df['day_of_week'].isin([5, 6])
            
            # Time period classification
            df['time_period'] = pd.cut(
                df['hour'],
                bins=[0, 6, 12, 18, 24],
                labels=['night', 'morning', 'afternoon', 'evening'],
                include_lowest=True
            )
            
        return df
        
    def _process_geospatial_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Process geospatial data and create geometry objects."""
        if 'latitude' in df.columns and 'longitude' in df.columns:
            # Filter valid coordinates
            valid_coords = (
                df['latitude'].notna() & 
                df['longitude'].notna() &
                (df['latitude'].between(-90, 90)) &
                (df['longitude'].between(-180, 180))
            )
            
            # Create geometry column for valid coordinates
            if valid_coords.any():
                try:
                    from shapely.geometry import Point
                    df.loc[valid_coords, 'geometry'] = df.loc[valid_coords].apply(
                        lambda row: Point(row['longitude'], row['latitude']), axis=1
                    )
                    
                    # Calculate coordinate precision
                    df.loc[valid_coords, 'coord_precision'] = df.loc[valid_coords].apply(
                        lambda row: self._calculate_coordinate_precision(row['latitude'], row['longitude']), axis=1
                    )
                    
                except ImportError:
                    self.logger.warning("Shapely not available, skipping geometry creation")
                    
            self.logger.info(f"Processed geospatial data for {valid_coords.sum()} records")
            
        return df
        
    def _process_signal_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Process signal strength and quality data."""
        signal_thresholds = self.SIGNAL_THRESHOLDS.get(self.nlg_config.network_type, self.SIGNAL_THRESHOLDS['4G'])
        
        # Validate signal strength
        if 'signal_strength' in df.columns:
            strength_min, strength_max = signal_thresholds['signal_strength_range']
            df['signal_strength_valid'] = df['signal_strength'].between(strength_min, strength_max)
            
            # Classify signal quality based on strength
            df['signal_category'] = pd.cut(
                df['signal_strength'],
                bins=[
                    strength_min,
                    signal_thresholds['poor_threshold'],
                    signal_thresholds['fair_threshold'],
                    signal_thresholds['good_threshold'],
                    signal_thresholds['excellent_threshold'],
                    strength_max
                ],
                labels=['no_signal', 'poor', 'fair', 'good', 'excellent'],
                include_lowest=True
            )
            
            # Calculate signal quality score (0-1)
            df['signal_score'] = (
                (df['signal_strength'] - strength_min) / (strength_max - strength_min)
            ).clip(0, 1)
            
        # Process signal quality if available
        if 'signal_quality' in df.columns:
            # Normalize signal quality to 0-1 scale
            df['signal_quality_normalized'] = (
                (df['signal_quality'] - df['signal_quality'].min()) / 
                (df['signal_quality'].max() - df['signal_quality'].min())
            ).fillna(0)
            
        # Combine signal strength and quality for overall score
        if 'signal_score' in df.columns and 'signal_quality_normalized' in df.columns:
            df['overall_signal_score'] = (
                0.7 * df['signal_score'] + 0.3 * df['signal_quality_normalized']
            )
        elif 'signal_score' in df.columns:
            df['overall_signal_score'] = df['signal_score']
        else:
            df = df.copy()  # Create explicit copy to avoid SettingWithCopyWarning
            df['overall_signal_score'] = 0.5  # Default neutral score
            
        return df
        
    def _analyze_mobility_patterns(self, df: pd.DataFrame) -> pd.DataFrame:
        """Analyze mobility patterns from location and speed data."""
        # Classify mobility state based on speed
        if 'speed' in df.columns:
            mobility_conditions = []
            mobility_labels = []
            
            for state, config in self.MOBILITY_STATES.items():
                speed_min, speed_max = config['speed_range']
                mobility_conditions.append(df['speed'].between(speed_min, speed_max))
                mobility_labels.append(state)
                
            df['mobility_classification'] = np.select(
                mobility_conditions,
                mobility_labels,
                default='unknown'
            )
            
        # Calculate movement patterns if we have sequential data
        if 'timestamp' in df.columns and 'location_id' in df.columns:
            # Sort by location and timestamp
            df_sorted = df.sort_values(['location_id', 'timestamp'])
            
            # Calculate time differences
            df_sorted['time_diff'] = df_sorted.groupby('location_id')['timestamp'].diff()
            
            # Calculate distance traveled (if we have coordinates)
            if all(col in df.columns for col in ['latitude', 'longitude']):
                df_sorted['distance_traveled'] = df_sorted.groupby('location_id').apply(
                    lambda group: self._calculate_distance_series(group)
                ).reset_index(level=0, drop=True)
                
                # Calculate actual speed from distance and time
                df_sorted['calculated_speed'] = (
                    df_sorted['distance_traveled'] / 
                    df_sorted['time_diff'].dt.total_seconds() * 3.6  # Convert m/s to km/h
                ).fillna(0)
                
            # Merge back to original dataframe
            df = df.merge(
                df_sorted[['location_id', 'timestamp', 'time_diff', 'distance_traveled', 'calculated_speed']],
                on=['location_id', 'timestamp'],
                how='left'
            )
            
        return df
        
    async def _perform_spatial_clustering(self, df: pd.DataFrame) -> pd.DataFrame:
        """Perform spatial clustering to identify location groups."""
        if not all(col in df.columns for col in ['latitude', 'longitude', 'geometry']):
            return df
            
        try:
            from sklearn.cluster import DBSCAN
            import geopandas as gpd
            
            # Convert to GeoDataFrame
            gdf = gpd.GeoDataFrame(df, geometry='geometry')
            
            # Filter valid geometries
            valid_geom = gdf['geometry'].notna()
            
            if valid_geom.sum() < 2:
                return df
                
            # Extract coordinates for clustering
            coords = np.array([
                [point.x, point.y] for point in gdf.loc[valid_geom, 'geometry']
            ])
            
            # Convert clustering radius from meters to degrees (rough approximation)
            eps_degrees = self.nlg_config.clustering_radius_meters / 111000
            
            # Perform DBSCAN clustering
            clustering = DBSCAN(eps=eps_degrees, min_samples=3)
            cluster_labels = clustering.fit_predict(coords)
            
            # Add cluster labels to dataframe
            df.loc[valid_geom, 'spatial_cluster'] = cluster_labels
            df['spatial_cluster'] = df['spatial_cluster'].fillna(-1).astype(int)
            
            # Calculate cluster statistics
            cluster_stats = df[df['spatial_cluster'] >= 0].groupby('spatial_cluster').agg({
                'location_id': 'count',
                'overall_signal_score': 'mean',
                'latitude': ['mean', 'std'],
                'longitude': ['mean', 'std']
            }).round(4)
            
            # Flatten column names
            cluster_stats.columns = ['_'.join(col).strip() for col in cluster_stats.columns]
            cluster_stats = cluster_stats.reset_index()
            
            # Merge cluster statistics back
            df = df.merge(
                cluster_stats[['spatial_cluster', 'location_id_count', 'overall_signal_score_mean']],
                on='spatial_cluster',
                how='left',
                suffixes=('', '_cluster')
            )
            
            self.logger.info(f"Identified {len(cluster_stats)} spatial clusters")
            
        except ImportError:
            self.logger.warning("Scikit-learn or GeoPandas not available, skipping spatial clustering")
        except Exception as e:
            self.logger.warning(f"Spatial clustering failed: {e}")
            
        return df
        
    def _detect_hotspots(self, df: pd.DataFrame) -> pd.DataFrame:
        """Detect signal quality hotspots and cold spots."""
        if 'overall_signal_score' not in df.columns:
            return df
            
        # Define hotspot and coldspot thresholds
        hotspot_threshold = self.nlg_config.hotspot_threshold
        coldspot_threshold = 1 - hotspot_threshold
        
        # Classify locations
        df = df.copy()  # Create explicit copy to avoid SettingWithCopyWarning
        df['location_quality'] = pd.cut(
            df['overall_signal_score'],
            bins=[0, coldspot_threshold, hotspot_threshold, 1],
            labels=['coldspot', 'normal', 'hotspot'],
            include_lowest=True
        )
        
        # Calculate local signal quality statistics
        if 'spatial_cluster' in df.columns:
            # Use spatial clusters for local statistics
            cluster_quality = df.groupby('spatial_cluster')['overall_signal_score'].agg([
                'mean', 'std', 'count'
            ]).reset_index()
            
            df = df.merge(
                cluster_quality,
                on='spatial_cluster',
                how='left',
                suffixes=('', '_local')
            )
            
            # Identify anomalous locations within clusters
            df = df.copy()  # Create explicit copy to avoid SettingWithCopyWarning
            df['is_quality_anomaly'] = (
                abs(df['overall_signal_score'] - df['mean']) > 2 * df['std']
            ).fillna(False)
        else:
            # Use global statistics
            global_mean = df['overall_signal_score'].mean()
            global_std = df['overall_signal_score'].std()
            
            df['is_quality_anomaly'] = (
                abs(df['overall_signal_score'] - global_mean) > 2 * global_std
            )
            
        return df
        
    def _analyze_coverage_gaps(self, df: pd.DataFrame) -> pd.DataFrame:
        """Analyze coverage gaps and weak signal areas."""
        if 'signal_strength' not in df.columns:
            return df
            
        # Identify coverage gaps
        df['has_coverage'] = df['signal_strength'] >= self.nlg_config.coverage_threshold
        
        # Calculate coverage statistics by area
        if 'spatial_cluster' in df.columns:
            coverage_stats = df.groupby('spatial_cluster').agg({
                'has_coverage': ['mean', 'count'],
                'signal_strength': ['mean', 'min', 'max']
            }).round(3)
            
            # Flatten column names
            coverage_stats.columns = ['_'.join(col).strip() for col in coverage_stats.columns]
            coverage_stats = coverage_stats.reset_index()
            
            # Identify areas with poor coverage
            coverage_stats['poor_coverage_area'] = coverage_stats['has_coverage_mean'] < 0.8
            
            # Merge back to main dataframe
            df = df.merge(
                coverage_stats[['spatial_cluster', 'has_coverage_mean', 'poor_coverage_area']],
                on='spatial_cluster',
                how='left',
                suffixes=('', '_area')
            )
            
        # Time-based coverage analysis
        if 'time_period' in df.columns:
            time_coverage = df.groupby('time_period')['has_coverage'].mean()
            df['time_period_coverage'] = df['time_period'].map(time_coverage)
            
        return df
        
    async def _perform_nlg_analysis(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Perform comprehensive NLG analysis."""
        analysis = {
            'total_records': len(data),
            'unique_locations': data['location_id'].nunique() if 'location_id' in data.columns else 0,
            'time_span': {},
            'spatial_coverage': {},
            'signal_analysis': {},
            'mobility_analysis': {},
            'coverage_analysis': {},
            'quality_metrics': {}
        }
        
        # Time span analysis
        if 'timestamp' in data.columns:
            valid_timestamps = data['timestamp'].dropna()
            if len(valid_timestamps) > 0:
                analysis['time_span'] = {
                    'start_time': valid_timestamps.min().isoformat(),
                    'end_time': valid_timestamps.max().isoformat(),
                    'duration_hours': (valid_timestamps.max() - valid_timestamps.min()).total_seconds() / 3600,
                    'time_periods': data['time_period'].value_counts().to_dict() if 'time_period' in data.columns else {}
                }
                
        # Spatial coverage analysis
        if 'latitude' in data.columns and 'longitude' in data.columns:
            valid_coords = data['latitude'].notna() & data['longitude'].notna()
            if valid_coords.any():
                analysis['spatial_coverage'] = {
                    'lat_range': [float(data['latitude'].min()), float(data['latitude'].max())],
                    'lon_range': [float(data['longitude'].min()), float(data['longitude'].max())],
                    'valid_coordinates_pct': float(valid_coords.mean() * 100),
                    'spatial_clusters': int(data['spatial_cluster'].nunique()) if 'spatial_cluster' in data.columns else 0
                }
                
        # Signal analysis
        if 'signal_strength' in data.columns:
            analysis['signal_analysis'] = {
                'avg_signal_strength': float(data['signal_strength'].mean()) if data['signal_strength'].notna().any() else None,
                'signal_strength_std': float(data['signal_strength'].std()) if data['signal_strength'].notna().any() else None,
                'signal_categories': data['signal_category'].value_counts().to_dict() if 'signal_category' in data.columns else {},
                'avg_signal_score': float(data['overall_signal_score'].mean()) if 'overall_signal_score' in data.columns else None
            }
            
        # Mobility analysis
        if 'speed' in data.columns:
            analysis['mobility_analysis'] = {
                'avg_speed': float(data['speed'].mean()) if data['speed'].notna().any() else None,
                'max_speed': float(data['speed'].max()) if data['speed'].notna().any() else None,
                'mobility_states': data['mobility_classification'].value_counts().to_dict() if 'mobility_classification' in data.columns else {}
            }
            
        # Coverage analysis
        if 'has_coverage' in data.columns:
            analysis['coverage_analysis'] = {
                'overall_coverage_pct': float(data['has_coverage'].mean() * 100),
                'coverage_by_time': data.groupby('time_period')['has_coverage'].mean().to_dict() if 'time_period' in data.columns else {},
                'poor_coverage_areas': int(data['poor_coverage_area'].sum()) if 'poor_coverage_area' in data.columns else 0
            }
            
        # Quality metrics
        quality_fields = [col for col in data.columns if col.endswith('_valid')]
        if quality_fields:
            quality_scores = []
            for field in quality_fields:
                if data[field].notna().any():
                    quality_scores.append(data[field].mean())
                    
            if quality_scores:
                analysis['quality_metrics'] = {
                    'overall_quality_score': float(np.mean(quality_scores) * 100),
                    'parameter_validity': {field: float(data[field].mean() * 100) for field in quality_fields if data[field].notna().any()}
                }
                
        # Hotspot analysis
        if 'location_quality' in data.columns:
            analysis['hotspot_analysis'] = {
                'hotspots': int((data['location_quality'] == 'hotspot').sum()),
                'coldspots': int((data['location_quality'] == 'coldspot').sum()),
                'quality_distribution': data['location_quality'].value_counts().to_dict()
            }
            
        return analysis
        
    async def _store_nlg_data(self, data: pd.DataFrame) -> None:
        """Store NLG data in database with spatial indexing."""
        if not hasattr(self, 'db_ops') or not self.db_ops:
            raise ValueError("Database operations not configured")

        # Handle duplicate column names before storing
        if data.columns.duplicated().any():
            self.logger.warning("Duplicate column names detected in data, renaming...")
            # Create unique column names
            cols = data.columns.tolist()
            seen = {}
            for i, col in enumerate(cols):
                if col in seen:
                    seen[col] += 1
                    cols[i] = f"{col}_{seen[col]}"
                else:
                    seen[col] = 0
            data.columns = cols
            self.logger.info(f"Renamed duplicate columns: {data.columns.tolist()}")

        # Generate table name using enhanced naming logic
        # Try to get source path from context or use current timestamp
        source_path = getattr(self, 'current_source_path', None)
        if source_path:
            table_name = self.get_table_name(str(source_path))
        else:
            # Fallback: calculate date from data or current date
            if 'timestamp' in data.columns and not data['timestamp'].isna().all():
                max_date = data['timestamp'].max().strftime('%Y-%m-%d')
            else:
                max_date = datetime.now().strftime('%Y-%m-%d')
            table_name = f'nlg_cube_aktuell_{max_date}'

        # Set target schema to nlg_to2
        target_schema = 'nlg_to2'

        self.logger.info(f"Storing NLG data to {target_schema}.{table_name} with {len(data)} rows and {len(data.columns)} columns")

        # Store in database with spatial support
        await self.db_ops.store_dataframe(
            data,
            table_name,
            schema=target_schema,
            if_exists='append',
            spatial_column='geometry' if 'geometry' in data.columns else None
        )
        
        # Create spatial index if supported
        if hasattr(self.db_ops, 'create_spatial_index') and 'geometry' in data.columns:
            await self.db_ops.create_spatial_index(table_name, 'geometry')
            
        # Create time-based index for efficient time-series queries
        if hasattr(self.db_ops, 'create_index') and 'timestamp' in data.columns:
            await self.db_ops.create_index(table_name, 'timestamp')
            
    async def validate_source(self, source: Union[str, Path]) -> bool:
        """Validate NLG data source.
        
        Args:
            source: Data source to validate
            
        Returns:
            True if source is valid
        """
        try:
            source_path = Path(source)
            
            # Check if file exists
            if not source_path.exists():
                self.logger.error(f"Source file does not exist: {source}")
                return False
                
            # Check file format
            if source_path.suffix.lower() not in self.supported_formats:
                self.logger.error(f"Unsupported file format: {source_path.suffix}")
                return False
                
            # Check file size (basic validation)
            file_size_mb = source_path.stat().st_size / 1024 / 1024
            if file_size_mb > 2000:  # 2GB limit for NLG files
                self.logger.warning(f"Large NLG file detected: {file_size_mb:.1f}MB")
                
            return True
            
        except Exception as e:
            self.logger.error(f"Source validation failed: {e}")
            return False
            
    async def get_source_info(self, source: Union[str, Path]) -> Dict[str, Any]:
        """Get information about NLG data source.
        
        Args:
            source: Data source
            
        Returns:
            Source information dictionary
        """
        try:
            source_path = Path(source)
            stat = source_path.stat()
            
            return {
                'file_path': str(source_path),
                'file_name': source_path.name,
                'file_size_bytes': stat.st_size,
                'file_size_mb': stat.st_size / 1024 / 1024,
                'file_format': source_path.suffix.lower(),
                'modified_time': datetime.fromtimestamp(stat.st_mtime),
                'is_supported': source_path.suffix.lower() in self.supported_formats,
                'estimated_records': self._estimate_record_count(source_path),
                'network_type': self.nlg_config.network_type,
                'operator': self.nlg_config.operator,
                'is_spatial': source_path.suffix.lower() in ['.gpx', '.kml'],
                'is_time_series': True  # NLG data is typically time-series
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get source info: {e}")
            return {'error': str(e)}
            
    def _estimate_record_count(self, file_path: Path) -> Optional[int]:
        """Estimate number of records in file."""
        try:
            file_size_mb = file_path.stat().st_size / 1024 / 1024
            
            # Rough estimation based on file size and format
            if file_path.suffix.lower() == '.csv':
                # Assume ~200 bytes per NLG record on average
                return int(file_size_mb * 1024 * 1024 / 200)
            elif file_path.suffix.lower() in ['.xlsx', '.xls']:
                # Excel files are more compressed
                return int(file_size_mb * 1024 * 1024 / 150)
            elif file_path.suffix.lower() in ['.gpx', '.kml']:
                # GPS/spatial files vary widely
                return int(file_size_mb * 1024 * 1024 / 400)
            else:
                return None
                
        except Exception:
            return None
            
    def _calculate_coordinate_precision(self, lat: float, lon: float) -> float:
        """Calculate coordinate precision in meters."""
        # Estimate precision based on decimal places
        lat_str = str(lat)
        lon_str = str(lon)
        
        lat_decimals = len(lat_str.split('.')[-1]) if '.' in lat_str else 0
        lon_decimals = len(lon_str.split('.')[-1]) if '.' in lon_str else 0
        
        # Use minimum precision
        min_decimals = min(lat_decimals, lon_decimals)
        
        # Rough conversion: 1 decimal place ≈ 11 km, each additional decimal ≈ 1/10 of previous
        if min_decimals == 0:
            return 111000  # ~111 km
        else:
            return 111000 / (10 ** min_decimals)
            
    def _calculate_distance_series(self, group: pd.DataFrame) -> pd.Series:
        """Calculate distance traveled for a group of sequential locations."""
        if len(group) < 2:
            return pd.Series([0] * len(group), index=group.index)
            
        distances = [0]  # First point has 0 distance
        
        for i in range(1, len(group)):
            prev_row = group.iloc[i-1]
            curr_row = group.iloc[i]
            
            if (pd.notna(prev_row['latitude']) and pd.notna(prev_row['longitude']) and
                pd.notna(curr_row['latitude']) and pd.notna(curr_row['longitude'])):
                
                distance = self.calculate_distance(
                    prev_row['latitude'], prev_row['longitude'],
                    curr_row['latitude'], curr_row['longitude']
                )
                distances.append(distance)
            else:
                distances.append(0)
                
        return pd.Series(distances, index=group.index)
        
    def calculate_distance(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """Calculate distance between two points using Haversine formula.
        
        Args:
            lat1, lon1: First point coordinates
            lat2, lon2: Second point coordinates
            
        Returns:
            Distance in meters
        """
        # Convert to radians
        lat1, lon1, lat2, lon2 = map(math.radians, [lat1, lon1, lat2, lon2])
        
        # Haversine formula
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))
        
        # Earth radius in meters
        r = 6371000
        
        return c * r
        
    def get_signal_thresholds(self, network_type: str) -> Dict[str, float]:
        """Get signal quality thresholds for specific network type.
        
        Args:
            network_type: Network type (2G, 3G, 4G, 5G)
            
        Returns:
            Signal thresholds dictionary
        """
        return self.SIGNAL_THRESHOLDS.get(network_type, self.SIGNAL_THRESHOLDS['4G'])
        
    def get_mobility_states(self) -> Dict[str, Dict[str, Any]]:
        """Get mobility state classifications.
        
        Returns:
            Mobility states dictionary
        """
        return self.MOBILITY_STATES
        
    def classify_signal_quality(self, signal_strength: float, network_type: str = None) -> str:
        """Classify signal quality based on strength.
        
        Args:
            signal_strength: Signal strength in dBm
            network_type: Network type (optional, uses config if not provided)
            
        Returns:
            Signal quality category
        """
        if network_type is None:
            network_type = self.nlg_config.network_type
            
        thresholds = self.get_signal_thresholds(network_type)
        
        if signal_strength >= thresholds['excellent_threshold']:
            return 'excellent'
        elif signal_strength >= thresholds['good_threshold']:
            return 'good'
        elif signal_strength >= thresholds['fair_threshold']:
            return 'fair'
        elif signal_strength >= thresholds['poor_threshold']:
            return 'poor'
        else:
            return 'no_signal'
            
    async def _check_system_resources(self) -> Dict[str, Any]:
        """Check system resources before processing.
        
        Returns:
            Dictionary with resource check results
        """
        import psutil
        import shutil
        
        try:
            # Check available memory
            memory = psutil.virtual_memory()
            available_memory_mb = memory.available / 1024 / 1024
            
            # Check disk space
            disk_usage = shutil.disk_usage('.')
            available_disk_gb = disk_usage.free / 1024 / 1024 / 1024
            
            # Check CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Determine if resources are sufficient
            memory_sufficient = available_memory_mb >= self.nlg_config.max_memory_usage_mb
            disk_sufficient = available_disk_gb >= self.nlg_config.min_disk_space_gb
            cpu_sufficient = cpu_percent < 90.0
            
            sufficient = memory_sufficient and disk_sufficient and cpu_sufficient
            
            messages = []
            if not memory_sufficient:
                messages.append(f"Low memory: {available_memory_mb:.0f}MB available, need {self.nlg_config.max_memory_usage_mb}MB")
            if not disk_sufficient:
                messages.append(f"Low disk space: {available_disk_gb:.1f}GB available, need {self.nlg_config.min_disk_space_gb}GB")
            if not cpu_sufficient:
                messages.append(f"High CPU usage: {cpu_percent:.1f}%")
                
            return {
                'sufficient': sufficient,
                'message': '; '.join(messages) if messages else 'Resources sufficient',
                'memory_mb': available_memory_mb,
                'disk_gb': available_disk_gb,
                'cpu_percent': cpu_percent
            }
            
        except Exception as e:
            self.logger.warning(f"Resource check failed: {e}")
            return {
                'sufficient': True,  # Assume sufficient if check fails
                'message': f'Resource check failed: {e}',
                'memory_mb': 0,
                'disk_gb': 0,
                'cpu_percent': 0
            }
            
    def _cleanup_memory(self):
        """Clean up memory by forcing garbage collection and clearing caches."""
        import gc
        
        try:
            # Clear any cached data if available
            if hasattr(self, '_cached_data'):
                self._cached_data.clear()
                
            # Clear pandas caches
            try:
                import pandas as pd
                # Clear any internal pandas caches
                if hasattr(pd, '_config'):
                    pd._config.reset_option('all')
            except Exception:
                pass
                
            # Force multiple garbage collection cycles
            collected_total = 0
            for generation in range(3):
                collected = gc.collect(generation)
                collected_total += collected
                
            self.logger.info(f"Memory cleanup: collected {collected_total} objects across all generations")
            
            # Log memory usage after cleanup
            current_memory = self._monitor_memory_usage()
            self.logger.info(f"Memory usage after cleanup: {current_memory:.0f}MB")
                
        except Exception as e:
            self.logger.warning(f"Memory cleanup failed: {e}")
            
    def _check_memory_and_cleanup(self, stage: str = ""):
        """Check memory usage and perform cleanup if necessary.
        
        Args:
            stage: Description of current processing stage
        """
        import gc
        
        try:
            # Skip memory monitoring if disabled
            if not self.nlg_config.enable_memory_monitoring:
                return
                
            current_memory = self._monitor_memory_usage()
            
            # Log memory usage at each stage
            if stage:
                self.logger.debug(f"Memory usage {stage}: {current_memory:.0f}MB")
            
            # Perform cleanup if memory usage is high
            if current_memory > self.nlg_config.memory_cleanup_threshold_mb:
                self.logger.warning(f"High memory usage {stage}: {current_memory:.0f}MB, performing cleanup")
                self._cleanup_memory()
                
                # Check if cleanup was effective
                new_memory = self._monitor_memory_usage()
                if new_memory > self.nlg_config.memory_critical_threshold_mb:
                    raise MemoryError(f"Critical memory usage {stage}: {new_memory:.0f}MB after cleanup")
            else:
                # Always do light cleanup
                gc.collect()
                    
        except MemoryError:
            raise  # Re-raise memory errors
        except Exception as e:
            self.logger.warning(f"Memory check failed {stage}: {e}")
            # Fallback to basic garbage collection
            gc.collect()
            
    def _monitor_memory_usage(self) -> float:
        """Monitor current memory usage.
        
        Returns:
            Current memory usage in MB
        """
        import psutil
        
        try:
            memory = psutil.virtual_memory()
            memory_used_mb = memory.used / 1024 / 1024
            
            return memory_used_mb
            
        except Exception as e:
            self.logger.warning(f"Memory monitoring failed: {e}")
            return 0.0  # Return 0 if monitoring fails


if __name__ == "__main__":
    import argparse
    import sys
    
    parser = argparse.ArgumentParser(description="NLG Importer CLI")
    parser.add_argument("--version", action="version", version="NLG Importer 1.0.0")
    
    args = parser.parse_args()
    
    print("NLG Importer CLI")
    print("Use this module as part of the Connect framework for NLG data import.")
    print("For usage examples, see the documentation.")