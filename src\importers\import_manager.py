"""
Unified Import Manager for Connect Telecommunications System

This module provides a centralized import management system that handles
all telecommunications data types with consistent processing, validation,
and error handling.

Features:
- Unified interface for all data types (CDR, EP, NLG, KPI, CFG, SCORE)
- Intelligent batch processing with memory optimization
- Multi-operator support with automatic detection
- Schema management and table creation
- Performance monitoring and metrics collection
- Comprehensive error handling and recovery
- Web API integration ready

Author: Vincent.Li
Email: <EMAIL>
"""

import asyncio
import logging
import time
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

import pandas as pd
from pydantic import BaseModel, Field, ConfigDict

from .base import ImportStatus, ImportResult, ImportMetrics, TelecomImportError

from .base import (
    AbstractImporter,
    ImportResult,
    ImportStatus,
    ImportMetrics,
    TelecomImportError
)
from ..database.operations.database_manager import DatabaseManager
from ..database.schema.manager import SchemaManager
from ..database.connection.pool import DatabasePoolManager
from ..config import get_config
from ..database.monitoring.logger import get_logger
from ..utils.disk_space_manager import check_and_cleanup_if_needed, get_disk_manager

logger = get_logger(__name__)


class ImportJobConfig(BaseModel):
    """Configuration for import jobs."""
    
    # Source configuration
    source_path: str = Field(..., description="Path to source file or directory")
    data_type: str = Field(..., description="Type of data (cdr, ep, nlg, kpi, cfg, score)")
    
    # Processing configuration
    batch_size: Optional[int] = Field(default=None, description="Batch size for processing")
    parallel_workers: int = Field(default=3, description="Number of parallel workers")
    memory_limit_mb: int = Field(default=4096, description="Memory limit in MB")
    
    # Database configuration
    target_schema: Optional[str] = Field(default=None, description="Target database schema")
    table_name: Optional[str] = Field(default=None, description="Target table name")
    operator: Optional[str] = Field(default="telefonica", description="Operator (telefonica, vodafone, telekom)")
    
    # Validation configuration
    validate_only: bool = Field(default=False, description="Only validate without importing")
    skip_validation: bool = Field(default=False, description="Skip data validation")
    error_threshold: float = Field(default=0.05, description="Maximum error rate allowed")
    
    # Performance configuration
    enable_monitoring: bool = Field(default=True, description="Enable performance monitoring")
    enable_caching: bool = Field(default=True, description="Enable result caching")
    
    model_config = ConfigDict(
        extra="allow"
    )
class ImportJobResult(BaseModel):
    """Result of an import job."""
    
    job_id: str
    status: ImportStatus
    message: str
    
    # Processing metrics
    records_processed: int = 0
    records_imported: int = 0
    records_failed: int = 0
    
    # Performance metrics
    processing_time_seconds: float = 0.0
    throughput_records_per_second: float = 0.0
    memory_usage_mb: float = 0.0
    
    # Quality metrics
    data_quality_score: float = 0.0
    validation_errors: List[str] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)
    
    # Metadata
    source_info: Dict[str, Any] = Field(default_factory=dict)
    target_info: Dict[str, Any] = Field(default_factory=dict)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    model_config = ConfigDict(
        use_enum_values=True
    )
class ImportManager:
    """Import manager for all telecommunications data types."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the import manager."""
        
        self.config = get_config() if config is None else config
        self.logger = get_logger(__name__)
        
        # Database components
        self.pool_manager: Optional[DatabasePoolManager] = None
        self.db_manager: Optional[DatabaseManager] = None
        self.schema_manager: Optional[SchemaManager] = None
        
        # Import tracking
        self.active_jobs: Dict[str, ImportJobConfig] = {}
        self.job_results: Dict[str, ImportJobResult] = {}
        
        # Performance tracking
        self.performance_metrics = {
            'total_jobs': 0,
            'successful_jobs': 0,
            'failed_jobs': 0,
            'total_records_processed': 0,
            'total_processing_time': 0.0,
            'average_throughput': 0.0
        }
        
        # Data type configurations
        self.data_type_configs = {
            'cdr': {
                'default_schema': 'cdr_to2',
                'operator_schemas': {
                    'telefonica': 'cdr_to2',
                    'vodafone': 'cdr_vdf',
                    'telekom': 'cdr_tdg'
                },
                'default_batch_size': 10000,
                'supported_extensions': ['.csv', '.xlsx', '.xls', '.xlsb']
            },
            'ep': {
                'default_schema': 'ep_to2',
                'operator_schemas': {
                    'telefonica': 'ep_to2',
                    'qgis': 'ep_to2'
                },
                'default_batch_size': 5000,
                'supported_extensions': ['.csv', '.xlsx', '.xls', '.xlsb']
            },
            'nlg': {
                'default_schema': 'nlg_to2',
                'operator_schemas': {'telefonica': 'nlg_to2'},
                'default_batch_size': 1000,
                'supported_extensions': ['.csv', '.xlsx', '.xls', '.xlsb']
            },
            'kpi': {
                'default_schema': 'kpi_to2',
                'operator_schemas': {'telefonica': 'kpi_to2'},
                'default_batch_size': 15000,
                'supported_extensions': ['.csv', '.xlsx', '.xls', '.xlsb']
            },
            'cfg': {
                'default_schema': 'cfg_to2',
                'operator_schemas': {'telefonica': 'cfg_to2'},
                'default_batch_size': 2000,
                'supported_extensions': ['.csv', '.xlsx', '.xls', '.xlsb']
            },
            'score': {
                'default_schema': 'score_to2',
                'operator_schemas': {'telefonica': 'score_to2'},
                'default_batch_size': 8000,
                'supported_extensions': ['.csv', '.xlsx', '.xls', '.xlsb']
            }
        }
    
    async def initialize(self) -> None:
        """Initialize the import manager."""
        try:
            # Initialize database connection pool
            from ..database.connection.pool import get_pool_manager
            self.pool_manager = get_pool_manager(self.config)
            await self.pool_manager.initialize_pool()
            
            # Initialize database manager
            self.db_manager = DatabaseManager(self.pool_manager)
            
            # Initialize schema manager
            self.schema_manager = SchemaManager(self.pool_manager)
            
            # Ensure required schemas exist
            await self._ensure_schemas_exist()
            
            self.logger.info("Unified import manager initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize import manager: {e}")
            raise TelecomImportError(f"Initialization failed: {e}")
    
    async def _ensure_schemas_exist(self) -> None:
        """Ensure all required schemas exist."""
        required_schemas = set()
        
        for data_type_config in self.data_type_configs.values():
            required_schemas.add(data_type_config['default_schema'])
            required_schemas.update(data_type_config['operator_schemas'].values())
        
        for schema_name in required_schemas:
            try:
                if not await self.schema_manager.schema_exists(schema_name):
                    await self.schema_manager.create_schema(schema_name)
                    self.logger.info(f"Created schema: {schema_name}")
            except Exception as e:
                self.logger.warning(f"Failed to create schema {schema_name}: {e}")
    
    def auto_detect_data_type(self, file_path: Path) -> Optional[str]:
        """Auto-detect data type based on file path and name patterns."""
        filename_lower = file_path.name.lower()
        parent_path_lower = str(file_path.parent).lower()
        full_path_lower = str(file_path).lower()

        # Priority 1: Check by parent directory patterns (most reliable)
        directory_patterns = {
            'cdr': ['cdr', 'call_detail', 'voice_data', 'conversational'],
            'ep': ['ep', 'engineering', 'site_data', 'cell_data'],
            'nlg': ['nlg', 'network_location', 'geography', 'neighbor'],
            'kpi': ['kpi', 'performance', 'indicator', 'metrics'],
            'cfg': ['cfg', 'config', 'configuration', 'bkpfile'],
            'score': ['score', 'benchmark', 'quality', 'rating']
        }

        for data_type, dir_patterns in directory_patterns.items():
            if any(pattern in parent_path_lower for pattern in dir_patterns):
                return data_type

        # Priority 2: Check by filename patterns (specific keywords)
        filename_patterns = {
            'cdr': [
                'cdr', 'call_detail', 'voice', 'conversational', 'sms', 'data_session',
                'mms', 'gprs', 'umts', 'lte', 'nr', 'call_record', 'test_cdr'
            ],
            'ep': [
                'gsmcell', 'ltecell', 'nrcell', 'tef_site', 'engineering', 'site_',
                'cell_', 'antenna', 'sector', 'bts', 'nodeb', 'enodeb', 'gnodeb',
                'ep', 'test_ep', 'ep_data'
            ],
            'nlg': [
                'nlg_cube', 'neighbor', 'location', 'geography', 'spatial',
                'coordinate', 'lat_lon', 'geolocation'
            ],
            'kpi': [
                'kpi', 'performance', 'indicator', 'metrics', 'quality',
                'throughput', 'latency', 'availability', 'success_rate', 'test_kpi'
            ],
            'cfg': [
                'bkpfileof', 'config', 'cfg', 'configuration', 'settings',
                'parameters', 'backup'
            ],
            'score': [
                'benchmark', 'score', 'quality', 'flattable', 'rating',
                'assessment', 'evaluation', 'shared_benchmark'
            ]
        }

        for data_type, patterns in filename_patterns.items():
            if any(pattern in filename_lower for pattern in patterns):
                return data_type

        # Priority 3: Check by file extension and structure patterns
        if file_path.suffix.lower() in ['.xlsx', '.xls']:
            # Excel files often contain specific data types
            if 'cube' in filename_lower:
                return 'nlg'
            elif 'benchmark' in filename_lower or 'flattable' in filename_lower:
                return 'score'
            elif any(tech in filename_lower for tech in ['gsm', 'lte', 'nr', 'umts']):
                return 'ep'

        # Priority 4: Check by year patterns (data organization)
        # Generate year patterns dynamically from 2019 to current year + 1
        import datetime
        current_year = datetime.datetime.now().year
        year_patterns = [str(year) for year in range(2019, current_year + 2)]
        if any(year in full_path_lower for year in year_patterns):
            # Files organized by year are likely CDR or KPI data
            if 'kpi' in parent_path_lower:
                return 'kpi'
            elif 'cdr' in parent_path_lower:
                return 'cdr'

        return None
    
    def auto_detect_operator(self, file_path: Path) -> str:
        """Auto-detect operator based on file path patterns using OperatorDetector."""
        try:
            from src.database.utils.table_naming import OperatorDetector
            
            # Create detector with config
            detector = OperatorDetector(getattr(self, 'config', {}))
            
            # Try to detect from filename first
            filename = file_path.name
            operator = detector.detect_operator_from_sheet(filename)
            
            if operator:
                return operator
            
            # Try to detect from directory structure
            path_parts = file_path.parts
            for part in path_parts:
                operator = detector.detect_operator_from_sheet(part)
                if operator:
                    return operator
            
            # Fallback for special cases like QGIS data
            path_str = str(file_path).lower()
            if any(pattern in path_str for pattern in ['qgis', 'gsmcell', 'ltecell', 'nrcell', 'umtscell', 'cell_cw', '_cw', 'engineering_parameters']):
                return 'qgis'
            
            return 'telefonica'  # Default to Telefonica
            
        except ImportError:
            # Fallback to original logic if OperatorDetector is not available
            path_str = str(file_path).lower()
            
            # Basic operator detection patterns as fallback
            if any(pattern in path_str for pattern in ['vodafone', 'vdf', 'voda']):
                return 'vodafone'
            elif any(pattern in path_str for pattern in ['telekom', 'tdg', 'deutsche']):
                return 'telekom'
            elif any(pattern in path_str for pattern in ['qgis', 'gsmcell', 'ltecell']):
                return 'qgis'
            
            return 'telefonica'
    
    def get_target_schema(self, data_type: str, operator: Optional[str] = None) -> str:
        """Get target schema for data type and operator."""
        if data_type not in self.data_type_configs:
            raise ValueError(f"Unsupported data type: {data_type}")
        
        config = self.data_type_configs[data_type]
        
        if operator and operator in config['operator_schemas']:
            return config['operator_schemas'][operator]
        
        return config['default_schema']
    
    def validate_file_format(self, file_path: Path, data_type: str) -> bool:
        """Validate file format for data type."""
        if data_type not in self.data_type_configs:
            return False
        
        supported_extensions = self.data_type_configs[data_type]['supported_extensions']
        return file_path.suffix.lower() in supported_extensions
    
    async def create_import_job(self, job_config: ImportJobConfig) -> str:
        """Create a new import job."""
        
        # Generate job ID
        job_id = f"import_{int(time.time() * 1000)}"
        
        # Validate configuration
        await self._validate_job_config(job_config)
        
        # Store job configuration
        self.active_jobs[job_id] = job_config
        
        self.logger.info(f"Created import job {job_id} for {job_config.data_type} data")
        
        return job_id
    
    async def _validate_job_config(self, job_config: ImportJobConfig) -> None:
        """Validate import job configuration."""
        
        # Validate data type
        if job_config.data_type not in self.data_type_configs:
            raise ValueError(f"Unsupported data type: {job_config.data_type}")
        
        # Validate source path
        source_path = Path(job_config.source_path)
        if not source_path.exists():
            raise FileNotFoundError(f"Source path does not exist: {source_path}")
        
        # Validate file format if it's a file
        if source_path.is_file():
            if not self.validate_file_format(source_path, job_config.data_type):
                supported_exts = self.data_type_configs[job_config.data_type]['supported_extensions']
                raise ValueError(f"Unsupported file format. Supported: {supported_exts}")
        
        # Set defaults if not provided
        if job_config.batch_size is None:
            job_config.batch_size = self.data_type_configs[job_config.data_type]['default_batch_size']
        
        if job_config.target_schema is None:
            job_config.target_schema = self.get_target_schema(
                job_config.data_type, 
                job_config.operator
            )
    
    async def execute_import_job(self, job_id: str) -> ImportJobResult:
        """Execute an import job."""
        
        if job_id not in self.active_jobs:
            raise ValueError(f"Job not found: {job_id}")
        
        job_config = self.active_jobs[job_id]
        start_time = time.time()
        
        try:
            self.logger.info(f"Starting import job {job_id}")
            
            # Check disk space before starting import
            await self._check_disk_space_before_import(job_config)
            
            self.logger.info(f"Disk space check passed for job {job_id}")
            
            # Create importer instance
            importer = self._create_importer_instance(job_config)
            
            # Set database context
            if hasattr(importer, 'set_database_context'):
                # Create database operations wrapper for CDR importer
                db_ops = self._create_db_operations_wrapper()
                importer.set_database_context(
                    pool=self.pool_manager,
                    db_manager=self.db_manager,
                    db_ops=db_ops,
                    schema_manager=self.schema_manager
                )
            
            # Execute import
            import_kwargs = {
                'validate_only': job_config.validate_only,
                'batch_size': job_config.batch_size,
                'operator': job_config.operator,
                'target_schema': job_config.target_schema
            }
            import_result = await importer.import_data(job_config.source_path, **import_kwargs)
            
            # Calculate metrics
            end_time = time.time()
            processing_time = end_time - start_time
            # Get metrics from import result
            metrics = getattr(import_result, 'metrics', None)
            records_processed = metrics.records_processed if metrics else 0
            records_imported = records_processed - (metrics.records_failed if metrics else 0)
            
            throughput = records_processed / processing_time if processing_time > 0 else 0
            
            # Create job result
            job_result = ImportJobResult(
                job_id=job_id,
                status=import_result.status,
                message=getattr(import_result, 'error_message', 'Success') or 'Success',
                records_processed=records_processed,
                records_imported=records_imported,
                processing_time_seconds=processing_time,
                throughput_records_per_second=throughput,
                data_quality_score=getattr(import_result, 'data_quality_score', 0.0),
                validation_errors=getattr(import_result, 'errors', []),
                warnings=getattr(import_result, 'warnings', []),
                source_info={'path': job_config.source_path, 'type': job_config.data_type},
                target_info={'schema': job_config.target_schema},
                metadata=getattr(import_result, 'metadata', {})
            )
            
            # Update performance metrics
            self._update_performance_metrics(job_result)
            
            # Store result
            self.job_results[job_id] = job_result
            
            # Clean up active job
            del self.active_jobs[job_id]
            
            self.logger.info(f"Completed import job {job_id} in {processing_time:.2f}s")
            
            return job_result
            
        except Exception as e:
            # Handle job failure
            end_time = time.time()
            processing_time = end_time - start_time
            
            job_result = ImportJobResult(
                job_id=job_id,
                status=ImportStatus.FAILED,
                message=f"Import failed: {str(e)}",
                processing_time_seconds=processing_time,
                validation_errors=[str(e)],
                source_info={'path': job_config.source_path, 'type': job_config.data_type}
            )
            
            self.job_results[job_id] = job_result
            del self.active_jobs[job_id]
            
            self.logger.error(f"Import job {job_id} failed: {e}")
            
            return job_result
    
    def _update_performance_metrics(self, job_result: ImportJobResult) -> None:
        """Update performance metrics."""
        self.performance_metrics['total_jobs'] += 1
        
        # Handle both enum and string status values
        status_value = job_result.status
        if hasattr(status_value, 'value'):
            status_value = status_value.value
        elif hasattr(status_value, 'name'):
            status_value = status_value.name
        
        # Check for completion status
        if status_value in ['COMPLETED', 'completed', 'SUCCESS', 'success']:
            self.performance_metrics['successful_jobs'] += 1
            self.performance_metrics['total_records_processed'] += job_result.records_processed
        else:
            self.performance_metrics['failed_jobs'] += 1
        
        self.performance_metrics['total_processing_time'] += job_result.processing_time_seconds
        
        # Calculate average throughput
        if self.performance_metrics['total_processing_time'] > 0:
            self.performance_metrics['average_throughput'] = (
                self.performance_metrics['total_records_processed'] / 
                self.performance_metrics['total_processing_time']
            )
    
    async def cleanup(self) -> None:
        """Cleanup resources."""
        try:
            # Clear all active jobs (they are ImportJobConfig objects, not tasks)
            if self.active_jobs:
                self.logger.info(f"Clearing {len(self.active_jobs)} active jobs")
                self.active_jobs.clear()
            
            # Close pool manager
            if self.pool_manager:
                try:
                    await self.pool_manager.close_pool()
                except Exception as e:
                    self.logger.warning(f"Error closing pool manager: {e}")
            
            self.logger.info("Import manager cleanup completed")
        except Exception as e:
            self.logger.error(f"Error during import manager cleanup: {e}")
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics."""
        return self.performance_metrics.copy()
    
    def get_job_result(self, job_id: str) -> Optional[ImportJobResult]:
        """Get job result by ID."""
        return self.job_results.get(job_id)
    
    def list_active_jobs(self) -> List[str]:
        """List active job IDs."""
        return list(self.active_jobs.keys())
    
    def list_completed_jobs(self) -> List[str]:
        """List completed job IDs."""
        return list(self.job_results.keys())

    def _create_db_operations_wrapper(self):
        """Create a database operations wrapper with store_dataframe method."""
        from src.database.operations.bulk_operations import BulkOperations
        from src.database.schema import TableSchema, ColumnSchema
        
        class DatabaseOperationsWrapper:
            def __init__(self, config, pool_manager, schema_manager):
                self.bulk_ops = BulkOperations(config, pool_manager)
                self.schema_manager = schema_manager
                self.pool_manager = pool_manager
                self.logger = get_logger(self.__class__.__name__)
            
            async def store_dataframe(self, df, table_name, if_exists='append', schema=None, **kwargs):
                """Store dataframe using bulk operations, creating table if needed."""
                # Use provided schema or fall back to public
                if schema is None:
                    schema = 'public'

                try:
                    # Check if table exists
                    table_exists = await self._check_table_exists(table_name, schema)

                    if not table_exists:
                        self.logger.info(f"Table {schema}.{table_name} does not exist, creating it...")
                        await self._create_table_from_dataframe(df, table_name, schema)

                    # Now perform the bulk insert
                    return await self.bulk_ops.bulk_insert_dataframe_async(
                        table_name=table_name,
                        df=df,
                        schema=schema,
                        if_exists=if_exists,
                        **kwargs
                    )
                except Exception as e:
                    self.logger.error(f"Error storing dataframe to {schema}.{table_name}: {e}")
                    raise
            
            async def _check_table_exists(self, table_name, schema='public'):
                """Check if table exists in the database."""
                async with self.pool_manager.get_connection() as conn:
                    result = await conn.fetchval(
                        'SELECT EXISTS(SELECT 1 FROM information_schema.tables WHERE table_schema = $1 AND table_name = $2)',
                        schema, table_name
                    )
                    return result
            
            async def _create_table_from_dataframe(self, df, table_name, schema='public'):
                """Create table based on DataFrame structure."""
                from src.database.schema import ColumnSchema, TableSchema
                
                # Base columns with primary key and timestamp
                columns = [
                    ColumnSchema(name="id", data_type="BIGSERIAL", primary_key=True),
                    ColumnSchema(name="created_at", data_type="TIMESTAMP", default="CURRENT_TIMESTAMP"),
                ]
                
                # Add data columns based on DataFrame
                for col_name in df.columns:
                    # Skip if column already exists in base columns
                    if col_name in ['id', 'created_at']:
                        continue
                    
                    # Determine data type based on DataFrame column
                    dtype = df[col_name].dtype
                    if dtype == 'object':
                        data_type = "TEXT"
                    elif dtype in ['int64', 'int32']:
                        data_type = "BIGINT"
                    elif dtype in ['float64', 'float32']:
                        data_type = "DOUBLE PRECISION"
                    elif dtype == 'bool':
                        data_type = "BOOLEAN"
                    elif 'datetime' in str(dtype):
                        data_type = "TIMESTAMP"
                    else:
                        data_type = "TEXT"  # Default fallback
                    
                    columns.append(ColumnSchema(name=col_name, data_type=data_type))
                
                # Create table schema
                table_schema = TableSchema(
                    name=table_name,
                    schema=schema,
                    columns=columns
                )
                
                # Create the table
                await self.schema_manager.create_table(table_schema)
                self.logger.info(f"Created table {schema}.{table_name} with {len(columns)} columns")
        
        return DatabaseOperationsWrapper(self.config, self.pool_manager, self.schema_manager)
    
    def _create_importer_instance(self, job_config: ImportJobConfig):
        """Create importer instance based on job configuration."""

        # Import specific importers based on data type
        try:
            if job_config.data_type == 'cdr':
                from .cdr_importer import CDRImporter  # Use the complete implementation
                # Create proper config dict for CDRImporter
                config_dict = {
                    'name': 'cdrimporter',
                    'data_type': job_config.data_type,
                    'source_path': job_config.source_path,
                    'batch_size': job_config.batch_size,
                    'target_schema': job_config.target_schema
                }
                return CDRImporter(
                    source_path=job_config.source_path,
                    config=config_dict,
                    operator=job_config.operator,
                    batch_size=job_config.batch_size
                )
            elif job_config.data_type == 'ep':
                from .telecom.ep_importer import EPImporter
                # Create proper config dict for EPImporter
                config_dict = {
                    'name': 'ep_importer',
                    'data_type': job_config.data_type,
                    'source_path': job_config.source_path,
                    'batch_size': job_config.batch_size,
                    'target_schema': job_config.target_schema,
                    'operator': job_config.operator
                }
                return EPImporter(config=config_dict)
            elif job_config.data_type == 'nlg':
                from .telecom.nlg_importer import NLGImporter
                return NLGImporter(
                    config={
                        'name': 'nlg_importer',
                        'batch_size': job_config.batch_size,
                        'data_type': 'nlg',
                        'source_path': job_config.source_path,
                        'target_schema': job_config.target_schema
                    }
                )
            elif job_config.data_type == 'kpi':
                from .kpi_importer import KPIImporter
                importer = KPIImporter(
                    config={
                        'name': 'kpi_importer',
                        'batch_size': job_config.batch_size,
                        'data_type': 'kpi'
                    }
                )
                importer.source_path = job_config.source_path
                return importer
            elif job_config.data_type == 'cfg':
                from .cfg_importer import CFGImporter
                importer = CFGImporter(
                    config={
                        'name': 'cfg_importer',
                        'batch_size': job_config.batch_size,
                        'data_type': 'cfg'
                    }
                )
                importer.source_path = job_config.source_path
                return importer
            elif job_config.data_type == 'score':
                from .score_importer import ScoreImporter
                importer = ScoreImporter(
                    config={
                        'name': 'score_importer',
                        'batch_size': job_config.batch_size,
                        'data_type': 'score'
                    }
                )
                importer.source_path = job_config.source_path
                return importer
            else:
                # Fallback mock importer for unsupported types
                return self._create_mock_importer(job_config)

        except ImportError as e:
            self.logger.warning(f"Could not import {job_config.data_type} importer: {e}")
            return self._create_mock_importer(job_config)

    def _create_mock_importer(self, job_config: ImportJobConfig):
        """Create mock importer for testing and validation."""

        class MockImporter:
            def __init__(self, config):
                self.config = config
                self.logger = get_logger(f"MockImporter.{config.data_type}")

            def set_database_context(self, **kwargs):
                pass

            async def import_data(self, **kwargs):
                # Mock import result for validation
                from .base import ImportResult, ImportStatus

                # Simulate processing time based on file size
                import asyncio
                from pathlib import Path

                file_path = Path(self.config.source_path)
                if file_path.exists():
                    file_size_mb = file_path.stat().st_size / 1024 / 1024
                    processing_time = min(file_size_mb * 0.01, 2.0)  # Max 2 seconds
                    records_count = int(file_size_mb * 1000)  # Estimate records
                else:
                    processing_time = 0.1
                    records_count = 100

                await asyncio.sleep(processing_time)

                self.logger.info(f"Mock import completed for {self.config.data_type}: {records_count} records")

                # Return mock result
                mock_metrics = ImportMetrics()
                mock_metrics.records_processed = records_count
                mock_metrics.processing_time_seconds = processing_time

                return ImportResult(
                    status=ImportStatus.COMPLETED,
                    metrics=mock_metrics,
                    source_info={'mock': True, 'data_type': self.config.data_type}
                )

        return MockImporter(job_config)

    async def close(self):
        """Close the import manager and cleanup resources."""
        try:
            if hasattr(self, 'pool_manager') and self.pool_manager:
                await self.pool_manager.close()
                self.logger.info("Import manager closed successfully")
        except Exception as e:
            self.logger.error(f"Error closing import manager: {e}")
    
    async def _check_disk_space_before_import(self, job_config: ImportJobConfig):
        """Check disk space before starting import and cleanup if needed."""
        try:
            # Get disk manager for the source path directory
            from pathlib import Path
            source_path = Path(job_config.source_path)
            source_dir = source_path.parent
            
            # Print file path and relative path information as requested
            try:
                # Extract relative path information for ep files
                if job_config.data_type == 'ep' and source_path.exists():
                    # Get relative path from project root
                    project_root = Path.cwd()
                    try:
                        relative_path = source_path.relative_to(project_root)
                        self.logger.info(f"Processing file: {source_path.name}")
                        self.logger.info(f"Relative path: {relative_path.parent}")
                        
                        # For ep files, extract year/week info if available
                        path_parts = relative_path.parts
                        if len(path_parts) >= 3 and path_parts[0] == 'ep':
                            # Extract actual year and week from path structure
                            year_week_path = '/'.join(path_parts[1:3])  # e.g., "2024/cw05" or "2025/cw03"
                            self.logger.info(f"EP path structure: ep/{year_week_path}")
                    except ValueError:
                        # Path is not relative to project root
                        self.logger.info(f"Processing file: {source_path.name}")
                        self.logger.info(f"Full path: {source_path}")
            except Exception as path_error:
                self.logger.debug(f"Error extracting path info: {path_error}")
            
            disk_manager = get_disk_manager(source_dir)
            
            # Check current disk space
            disk_info = disk_manager.get_disk_space_info()
            usage_percent = float(disk_info['usage_percent'])  # Ensure float type
            self.logger.debug(f"Disk info: total_gb={disk_info['total_gb']}, used_gb={disk_info['used_gb']}, free_gb={disk_info['free_gb']}, usage_percent={usage_percent}")
            
            self.logger.info(
                f"Disk space check for job {job_config.data_type}: "
                f"{disk_info['free_gb']:.1f}GB free ({usage_percent:.1f}% used)"
            )
            
            # If disk usage is high, attempt cleanup
            if usage_percent > 85:  # Critical threshold
                self.logger.info("Starting emergency cleanup...")
                self.logger.warning(
                    f"Disk usage is high ({usage_percent:.1f}%), attempting cleanup..."
                )
                
                # Perform emergency cleanup
                cleanup_result = disk_manager.emergency_cleanup()
                
                if cleanup_result.freed_space_mb > 0:
                    # freed_space_mb is now guaranteed to be float by CleanupResult.__post_init__
                    freed_gb = cleanup_result.freed_space_mb / 1024
                    self.logger.info(
                        f"Emergency cleanup freed {freed_gb:.2f}GB"
                    )
                    
                    # Re-check disk space after cleanup
                    disk_info_after = disk_manager.get_disk_space_info()
                    usage_after = disk_info_after['usage_percent']  # Use the already calculated value
                    
                    if usage_after > 90:  # Still critical
                        self.logger.warning("Disk space still critical after cleanup.")
                        raise RuntimeError(
                            f"Insufficient disk space after cleanup: {disk_info_after['free_gb']:.1f}GB free "
                            f"({usage_after:.1f}% used). Please free up more space manually."
                        )
                else:
                    self.logger.warning("No files were cleaned up during emergency cleanup")
                    
                    if usage_percent > 90:  # Very critical
                        raise RuntimeError(
                            f"Insufficient disk space: {disk_info['free_gb']:.1f}GB free "
                            f"({usage_percent:.1f}% used). Please free up space manually."
                        )
            
            elif usage_percent > 75:  # Warning threshold
                self.logger.info("Disk usage warning, but proceeding with import.")
                self.logger.warning(
                    f"Disk usage is getting high ({usage_percent:.1f}%). "
                    "Consider running cleanup after import."
                )
                
        except Exception as e:
            self.logger.error(f"Error during disk space check: {e}")
            # Don't fail the import for disk space check errors, just log
            # The actual import will fail with proper error if space runs out
