"""
Table naming conventions and operator detection utilities.

This module provides functionality for generating standardized table names
and detecting operator information from telecommunications data files.
"""

import re
import logging
from pathlib import Path
from typing import Dict, Optional, Tuple, List
from datetime import datetime

logger = logging.getLogger(__name__)


class TableNamingManager:
    """Manages table naming conventions for telecommunications data."""
    
    def __init__(self, config: Dict):
        """Initialize with database configuration.
        
        Args:
            config: Database configuration containing naming patterns
        """
        self.config = config
        # Handle both dict and object config types
        if hasattr(config, 'get'):
            self.telecom_config = config.get('telecom_data_sources', {})
        elif hasattr(config, '__dict__'):
            config_dict = config.__dict__
            self.telecom_config = config_dict.get('telecom_data_sources', {})
        else:
            self.telecom_config = {}
        
    def generate_table_name(self, data_type: str, file_path: Path, 
                          operator: Optional[str] = None, 
                          sheet_name: Optional[str] = None) -> str:
        """Generate standardized table name based on data type and file information.
        
        Args:
            data_type: Type of data (ep, cdr, nlg, kpi, score, cfg)
            file_path: Path to the data file
            operator: Optional operator name for CDR data
            sheet_name: Optional sheet name for Excel files
            
        Returns:
            Standardized table name
        """
        try:
            data_config = self.telecom_config.get(data_type, {})
            pattern = data_config.get('table_name_pattern', f'{data_type}_{{filename}}')
            
            if data_type == 'cdr':
                return self._generate_cdr_table_name(file_path, pattern, operator, sheet_name)
            elif data_type == 'ep':
                return self._generate_ep_table_name(file_path, pattern)
            elif data_type == 'nlg':
                return self._generate_nlg_table_name(file_path, pattern)
            elif data_type == 'kpi':
                return self._generate_kpi_table_name(file_path, pattern)
            elif data_type == 'score':
                return self._generate_score_table_name(file_path, pattern)
            elif data_type == 'cfg':
                return self._generate_cfg_table_name(file_path, pattern)
            else:
                # Generic naming for other data types
                return self._generate_generic_table_name(file_path, pattern, data_type)
                
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            logger.error(f"Failed to generate table name using pattern for {data_type} data: {e}")
            logger.debug(f"Full error traceback: {error_details}")

            # Fallback to simple naming
            base_name = file_path.stem.lower()
            # Clean the name
            clean_name = re.sub(r'[^\w]', '_', base_name)
            clean_name = re.sub(r'_+', '_', clean_name)
            clean_name = clean_name.strip('_')

            fallback_name = f"{data_type}_{operator}_{clean_name}" if operator else f"{data_type}_{clean_name}"
            logger.warning(f"Using fallback table name: '{fallback_name}' for file: {file_path}")
            return fallback_name
    
    def _generate_cdr_table_name(self, file_path: Path, pattern: str, 
                                operator: Optional[str] = None, 
                                sheet_name: Optional[str] = None) -> str:
        """Generate CDR table name following pattern: cdr_{year}Q{quarter}_{service_type}
        
        Examples:
        - Shared_Benchmark_Q1_DE_Voice_M2M_Calls_2022-04-01_14-00-54.xlsx → cdr_2022Q1_voice_m2m_calls
        - Shared_Benchmark_Q2_DE_2024_Conversational_App_SQ_Samples_2024-05-31_13-35-42.xlsx → cdr_2024Q2_conversational_app_sq_samples
        """
        
        # Extract information from filename and path
        filename = file_path.stem
        path_str = str(file_path)
        
        # Extract year and quarter from path and filename
        year, quarter = self._extract_year_quarter_from_cdr_path(path_str, filename)
        
        # Extract service type from filename
        service_type = self._extract_cdr_service_type(filename)
        
        # Generate table name
        table_name = f"cdr_{year}Q{quarter}_{service_type}"
        
        logger.debug(f"Generated CDR table name '{table_name}' from filename '{filename}'")
        return table_name
    
    def _generate_ep_table_name(self, file_path: Path, pattern: str) -> str:
        """Generate EP table name following pattern: ep_{cell_type}_{year}_cw{week}
        
        Examples:
        - LTECELL_CW14.xlsx → ep_lte_2023_cw14
        - GSMCELL_CW33.xlsx → ep_gsm_2023_cw33
        - NRCELL_CW07.xlsx → ep_nr_2025_cw07
        - TEF_SITE_CW10.xlsx → ep_site_2025_cw10
        - TEF_Sites_&_Locations_CW26.xlsx → ep_site_2023_cw26
        """

        filename = file_path.stem
        full_path_str = str(file_path)

        # Extract cell type from filename
        cell_type = self._extract_ep_cell_type(filename)

        # Extract year and week from path and filename
        year, week = self._extract_year_week_from_ep_path(full_path_str, filename)

        # Generate table name
        table_name = f"ep_{cell_type}_{year}_cw{week:02d}"

        logger.debug(f"Generated EP table name '{table_name}' from filename '{filename}'")
        return table_name
    
    def _generate_nlg_table_name(self, file_path: Path, pattern: str) -> str:
        """Generate NLG table name following pattern: nlg_cube_aktuell_{date}
        
        Supports multiple date formats and converts to YYYY-MM-DD:
        - NLG_CUBE_aktuell_2023-12-13.xlsb → nlg_cube_aktuell_2023-12-13
        - NLG_CUBE_aktuell_2023_12_13.xlsb → nlg_cube_aktuell_2023-12-13
        - NLG_CUBE_aktuell_2025 05 06.xlsb → nlg_cube_aktuell_2025-05-06
        - NLG_CUBE_aktuell_20250506.xlsb → nlg_cube_aktuell_2025-05-06
        - NLG_CUBE_aktuell-20250506.xlsb → nlg_cube_aktuell_2025-05-06
        """
        
        filename = file_path.stem
        
        # Extract date from filename and convert to YYYY-MM-DD format
        date_str = self._extract_date_for_nlg(filename)
        
        # Apply pattern - use fixed pattern for NLG
        table_name = f"nlg_cube_aktuell_{date_str}"
        
        logger.debug(f"Generated NLG table name '{table_name}' from filename '{filename}'")
        return table_name
    
    def _generate_kpi_table_name(self, file_path: Path, pattern: str) -> str:
        """Generate KPI table name following pattern: kpi_{metric_type}_{year}
        
        Examples:
        - LTE_Access_KPIs (20190204-20190410).xlsx → kpi_lte_access_2019
        - NR_Mobility_KPIs (20250504-20250510).xlsx → kpi_nr_mobility_2025
        """
        
        filename = file_path.stem
        
        # Extract year from filename or path
        year = self._extract_year_from_filename_or_path(filename)
        
        # Extract metric type from filename
        metric_type = self._extract_kpi_metric_type(filename)
        
        # Generate table name
        table_name = f"kpi_{metric_type}_{year}"
        
        logger.debug(f"Generated KPI table name '{table_name}' from filename '{filename}'")
        return table_name
    
    def _generate_score_table_name(self, file_path: Path, pattern: str) -> str:
        """Generate SCORE table name following pattern: score_{year}Q{quarter}
        
        Examples:
        - 20230207_shared_benchmark_ch_2023Q1_ch_campaign_01-FlatTable.xlsx → score_2023Q1
        - 20240607_shared_benchmark_ch_2024Q2_ch_campaign_01-FlatTable.xlsx → score_2024Q2
        """
        
        filename = file_path.stem
        
        # Extract year and quarter from filename
        year_quarter = self._extract_year_quarter_from_score_filename(filename)
        
        # Generate table name
        table_name = f"score_{year_quarter}"
        
        logger.debug(f"Generated SCORE table name '{table_name}' from filename '{filename}'")
        return table_name
    
    def _generate_cfg_table_name(self, file_path: Path, pattern: str) -> str:
        """Generate CFG table name following pattern: cfg_{year}_cw{week}
        
        Examples:
        - D:\\connect\\data\\input\\cfg\\2025\\CW05\\CFG_05_South\\file.tar.gz → cfg_2025_cw05
        - D:\\connect\\data\\input\\cfg\\2025\\CW20\\CW20_West\\file.tar.gz → cfg_2025_cw20
        """
        
        # Extract year and week from path
        year, week = self._extract_year_week_from_cfg_path(file_path)
        
        # Generate table name
        table_name = f"cfg_{year}_cw{week:02d}"
        
        logger.debug(f"Generated CFG table name '{table_name}' from path '{file_path}'")
        return table_name
    
    def _generate_generic_table_name(self, file_path: Path, pattern: str, data_type: str) -> str:
        """Generate generic table name."""
        
        filename = file_path.stem
        clean_filename = re.sub(r'[^\w]', '_', filename.lower())
        clean_filename = re.sub(r'_+', '_', clean_filename).strip('_')
        
        # Apply pattern
        table_name = pattern.format(filename=clean_filename)
        
        return self._clean_table_name(table_name)
    
    def _extract_year_quarter(self, filename: str) -> Tuple[str, str]:
        """Extract year and quarter from filename with enhanced pattern matching.

        Handles CDR file patterns like:
        - Shared_Benchmark_Q2_DE_2024_HTTP_Browsing_2024-05-31_13-35-42.xlsx
        - CDR_2024Q1_Voice_Data.xlsx
        - Q1_2024_Benchmark.xlsx
        """

        # Enhanced quarter patterns (order matters - most specific first)
        quarter_patterns = [
            r'(\d{4})Q(\d)',           # 2024Q1, 2024Q2
            r'Q(\d).*?(\d{4})',        # Q2_DE_2024 (non-greedy match)
            r'(\d{4}).*?Q(\d)',        # 2024_Q1 (non-greedy match)
            r'(\d{4}).*?quarter(\d)',   # 2024_quarter1
            r'quarter(\d).*?(\d{4})',   # quarter1_2024
        ]

        for pattern in quarter_patterns:
            match = re.search(pattern, filename, re.IGNORECASE)
            if match:
                groups = match.groups()
                if pattern.startswith(r'Q(\d)') or pattern.startswith(r'quarter(\d)'):
                    # Quarter comes first
                    quarter, year = groups
                else:
                    # Year comes first
                    year, quarter = groups

                # Validate year range
                year_int = int(year)
                if 2020 <= year_int <= 2030:
                    return year, quarter

        # Enhanced fallback: try to extract year from path and infer quarter from date
        year_match = re.search(r'\b(20\d{2})\b', filename)
        if year_match:
            year = year_match.group(1)

            # Try to extract month from date patterns like 2024-05-31
            date_match = re.search(r'(\d{4})-(\d{2})-(\d{2})', filename)
            if date_match:
                _, month_str, _ = date_match.groups()
                month = int(month_str)
                quarter = str((month - 1) // 3 + 1)
                return year, quarter

        # Final fallback
        current_year = str(datetime.now().year)
        return current_year, "1"
    
    def _extract_service_type(self, filename: str, sheet_name: Optional[str] = None) -> str:
        """Extract service type from filename or sheet name with enhanced pattern matching.
        
        Handles complex service type patterns like:
        - Voice_M2M_Calls → voice_m2m_calls
        - Data_Sessions → data_sessions
        - SMS_Messages → sms_messages
        
        Strategy:
        1. First try to extract from filename (contains most detailed info)
        2. If no compound service type found, try sheet_name
        3. Fallback to 'general'
        """
        
        # Enhanced service type patterns with compound types
        service_patterns = {
            # Compound service types (most specific first)
            'voice_m2m_calls': r'voice.*m2m.*calls?|m2m.*voice.*calls?|voice.*machine.*machine.*calls?',
            'data_m2m_sessions': r'data.*m2m.*sessions?|m2m.*data.*sessions?',
            'sms_m2m_messages': r'sms.*m2m.*messages?|m2m.*sms.*messages?',
            'data_m2m': r'data.*m2m|m2m.*data',
            'sms_m2m': r'sms.*m2m|m2m.*sms',
            'voice_calls': r'voice.*calls?|calls?.*voice',
            'data_sessions': r'data.*sessions?|sessions?.*data',
            'sms_messages': r'sms.*messages?|messages?.*sms',
            
            # Basic service types
            'voice': r'voice|call|telefon',
            'data': r'data|http|internet|web',
            'sms': r'sms|text|message',
            'video': r'video|stream|youtube',
            'file': r'file|download|upload|dl|ul',
            'browsing': r'browsing|browse|surf',
            'ping': r'ping|icmp',
            'dns': r'dns',
            'app': r'app|application',
            'benchmark': r'benchmark|test',
            'm2m': r'm2m|machine.*machine'
        }
        
        # Compound service types (for priority checking)
        compound_types = {
            'voice_m2m_calls', 'data_m2m_sessions', 'sms_m2m_messages',
            'data_m2m', 'sms_m2m', 'voice_calls', 'data_sessions', 'sms_messages'
        }
        
        # First, try to extract from filename (most detailed info)
        for service_type, pattern in service_patterns.items():
            if re.search(pattern, filename, re.IGNORECASE):
                # If we found a compound service type in filename, use it
                if service_type in compound_types:
                    return service_type
                # Store basic type as fallback
                filename_result = service_type
                break
        else:
            filename_result = None
        
        # If no compound type found in filename, try sheet_name
        if sheet_name:
            for service_type, pattern in service_patterns.items():
                if re.search(pattern, sheet_name, re.IGNORECASE):
                    # If we found a compound type in sheet, use it
                    if service_type in compound_types:
                        return service_type
                    # If we found a basic type in sheet and no filename result, use it
                    if not filename_result:
                        return service_type
        
        # Return filename result if we have one, otherwise 'general'
        return filename_result or 'general'
    
    def _extract_cell_type(self, filename: str) -> str:
        """Extract cell type from filename with enhanced pattern matching.

        Handles specific EP file naming patterns:
        - GSMCELL_CW03.xlsx → gsm
        - LTECELL_CW10.xlsx → lte
        - NRCELL_CW26.xlsx → nr
        - TEF_SITE_CW48.xlsx → site
        - TEF_Sites_&_Locations_CW26.xlsx → site
        """

        # Enhanced cell type patterns with specific matching
        # Order matters - most specific patterns first to avoid false matches
        cell_patterns = [
            # Site patterns first (most specific)
            ('site', [
                r'TEF_SITE',         # TEF_SITE_CW48.xlsx
                r'TEF_Sites',        # TEF_Sites_&_Locations_CW26.xlsx
                r'tef_site',         # lowercase variants
                r'site'              # generic site files
            ]),
            # Cell type patterns (remove word boundaries that prevent matching)
            ('gsm', [
                r'GSMCELL',          # GSMCELL_CW03.xlsx
                r'gsm',              # gsm files
                r'2g'                # 2G files
            ]),
            ('lte', [
                r'LTECELL',          # LTECELL_CW10.xlsx
                r'lte',              # lte files
                r'4g'                # 4G files
            ]),
            ('nr', [
                r'NRCELL',           # NRCELL_CW26.xlsx
                r'nr',               # nr files
                r'5g'                # 5G files
            ]),
            ('umts', [
                r'UMTSCELL',         # UMTSCELL files
                r'umts',             # umts files
                r'3g'                # 3G files
            ])
        ]

        # Check each cell type pattern (order matters - most specific first)
        for cell_type, patterns in cell_patterns:
            for pattern in patterns:
                if re.search(pattern, filename, re.IGNORECASE):
                    logger.debug(f"Matched cell type '{cell_type}' for filename '{filename}' with pattern '{pattern}'")
                    return cell_type

        # Fallback to 'general' without 'mixed'
        return 'general'
    
    def _extract_year_week(self, filename: str) -> Tuple[str, str]:
        """Extract year and calendar week from filename with enhanced pattern matching.

        Handles specific EP file naming patterns:
        - GSMCELL_CW03.xlsx → year from path/context, week 03
        - TEF_SITE_CW48.xlsx → year from path/context, week 48
        - TEF_Sites_&_Locations_CW26.xlsx → year from path/context, week 26
        """

        # Enhanced week patterns (order matters - most specific first)
        week_patterns = [
            r'\bCW(\d{1,2})\b',     # CW03, CW48 (word boundary for exact match)
            r'\bcw(\d{1,2})\b',     # cw03, cw48 (lowercase)
            r'week(\d{1,2})',       # week03
            r'w(\d{1,2})',          # w03
            r'wk(\d{1,2})',         # wk03
        ]

        week = "01"  # default
        for pattern in week_patterns:
            match = re.search(pattern, filename, re.IGNORECASE)
            if match:
                week = match.group(1).zfill(2)
                break

        # Enhanced year extraction
        year = self._extract_year_from_filename_or_path(filename)

        return year, week

    def _extract_year_week_from_path(self, full_path: str, filename: str) -> Tuple[str, str]:
        """Extract year and week from full path and filename.

        Handles path patterns like:
        - D:/connect/data/input/ep/2025/CW03/GSMCELL_CW03.xlsx
        - D:/connect/data/input/ep/2024/CW48/TEF_SITE_CW48.xlsx
        """

        # Extract week from filename first
        week_patterns = [
            r'CW(\d{1,2})',         # CW03, CW48 (most common pattern)
            r'_CW(\d{1,2})',        # _CW03, _CW48 (with underscore)
            r'cw(\d{1,2})',         # cw03, cw48 (lowercase)
            r'week(\d{1,2})',       # week03
            r'w(\d{1,2})',          # w03
            r'wk(\d{1,2})',         # wk03
        ]

        week = "01"  # default
        for pattern in week_patterns:
            match = re.search(pattern, filename, re.IGNORECASE)
            if match:
                week = match.group(1).zfill(2)
                break

        # Extract year from path first (prioritize path over filename)
        year = self._extract_year_from_path(full_path)
        if not year:
            # Fallback to filename extraction
            year = self._extract_year_from_filename_or_path(filename)

        # Ensure we have valid year and week
        if not year:
            year = str(datetime.now().year)  # Ultimate fallback
        if not week:
            week = "01"  # Default week if not found

        logger.debug(f"Extracted year='{year}', week='{week}' from path='{full_path}', filename='{filename}'")
        return year, week

    def _extract_year_from_path(self, full_path: str) -> str:
        """Extract year from directory path.

        Handles patterns like:
        - D:/connect/data/input/ep/2025/CW03/
        - D:/connect/data/input/ep/2024/CW48/
        """

        # Look for year in path segments
        path_segments = full_path.replace('\\', '/').split('/')

        for segment in path_segments:
            # Check if segment is a 4-digit year
            if re.match(r'^(20\d{2})$', segment):
                year = int(segment)
                # Validate year range
                if 2020 <= year <= 2030:
                    return str(year)

        return ""  # Return empty string if no year found in path

    def _extract_year_from_filename_or_path(self, filename_or_path) -> str:
        """Extract year from filename or path with enhanced logic.

        Tries multiple approaches to find the most relevant year:
        1. Four-digit year in filename or path
        2. Current year as fallback
        """
        
        # Convert to string if Path object
        search_str = str(filename_or_path)

        # Look for 4-digit year in filename or path
        year_patterns = [
            r'\b(20\d{2})\b',  # 2020-2099 with word boundaries
            r'(\d{4})',        # Any 4-digit number
        ]

        for pattern in year_patterns:
            matches = re.findall(pattern, search_str)
            if matches:
                # Take the first valid year found
                for year_str in matches:
                    year = int(year_str)
                    # Validate year range (reasonable for telecommunications data)
                    if 2020 <= year <= 2030:
                        return str(year)

        # Fallback to current year only if no valid year found
        current_year = datetime.now().year
        if 2020 <= current_year <= 2030:
            return str(current_year)
        return '2024'  # Default fallback year
    
    def _extract_date_for_nlg(self, filename: str) -> str:
        """Extract date from NLG filename and convert to YYYY-MM-DD format.

        Handles specific NLG file naming patterns:
        - NLG_CUBE_aktuell_2023-12-13.xlsb → 2023-12-13
        - NLG_CUBE_aktuell_2023_12_13.xlsb → 2023-12-13
        - NLG_CUBE_aktuell_2025 05 06.xlsb → 2025-05-06
        - NLG_CUBE_aktuell_20250506.xlsb → 2025-05-06
        - NLG_CUBE_aktuell-20250506.xlsb → 2025-05-06

        Returns date in YYYY-MM-DD format for table names.
        """
        import re
        from datetime import datetime

        logger.debug(f"Extracting date from NLG filename: {filename}")

        # Pattern 1: YYYY-MM-DD format (already correct)
        pattern1 = r'(\d{4}-\d{2}-\d{2})'
        match1 = re.search(pattern1, filename)
        if match1:
            date_str = match1.group(1)
            logger.debug(f"Found YYYY-MM-DD format date: {date_str}")
            return date_str

        # Pattern 2: YYYY_MM_DD format (underscores)
        pattern2 = r'(\d{4})_(\d{2})_(\d{2})'
        match2 = re.search(pattern2, filename)
        if match2:
            year, month, day = match2.groups()
            date_str = f"{year}-{month}-{day}"
            logger.debug(f"Found YYYY_MM_DD format, converted to: {date_str}")
            return date_str

        # Pattern 3: YYYY MM DD format (spaces)
        pattern3 = r'(\d{4})\s+(\d{2})\s+(\d{2})'
        match3 = re.search(pattern3, filename)
        if match3:
            year, month, day = match3.groups()
            date_str = f"{year}-{month}-{day}"
            logger.debug(f"Found YYYY MM DD format, converted to: {date_str}")
            return date_str

        # Pattern 4: YYYYMMDD format (compact)
        pattern4 = r'(\d{4})(\d{2})(\d{2})'
        match4 = re.search(pattern4, filename)
        if match4:
            year, month, day = match4.groups()
            date_str = f"{year}-{month}-{day}"
            logger.debug(f"Found YYYYMMDD format, converted to: {date_str}")
            return date_str

        # Pattern 5: Handle hyphen-separated YYYYMMDD (e.g., -20250506)
        pattern5 = r'-(\d{4})(\d{2})(\d{2})'
        match5 = re.search(pattern5, filename)
        if match5:
            year, month, day = match5.groups()
            date_str = f"{year}-{month}-{day}"
            logger.debug(f"Found -YYYYMMDD format, converted to: {date_str}")
            return date_str

        # Fallback: use current date
        current_date = datetime.now().strftime('%Y-%m-%d')
        logger.warning(f"No date pattern found in filename '{filename}', using current date: {current_date}")
        return current_date

        # Enhanced date patterns for NLG files (order matters - most specific first)
        date_patterns = [
            # ISO date format with separators: 2024-03-28, 2024_03_28
            r'(\d{4})[-_](\d{2})[-_](\d{2})',
            # Date with spaces: 2025 05 06
            r'(\d{4})\s+(\d{2})\s+(\d{2})',
            # Date with dots: 2024.03.28
            r'(\d{4})\.(\d{2})\.(\d{2})',
            # Date with slashes: 2024/03/28
            r'(\d{4})/(\d{2})/(\d{2})',
            # Compact date format: 20240328 (8 consecutive digits)
            r'\b(\d{8})\b',
            # Compact with dash prefix: -20250506
            r'-(\d{8})\b',
            # Alternative formats: 2024-11-20 (variable digit months/days)
            r'(\d{4})-(\d{1,2})-(\d{1,2})',
            # Partial date patterns: 2024-03, 2024_03
            r'(\d{4})[-_](\d{2})',
        ]

        for pattern in date_patterns:
            match = re.search(pattern, filename)
            if match:
                groups = match.groups()

                if len(groups) == 1:
                    # Compact format: 20240328 or -20250506
                    date_str = groups[0]
                    if len(date_str) == 8:
                        # Convert YYYYMMDD to YYYY-MM-DD
                        year = date_str[:4]
                        month = date_str[4:6]
                        day = date_str[6:8]
                        return f"{year}-{month}-{day}"
                elif len(groups) == 2:
                    # Year-month format: 2024-03
                    year, month = groups
                    return f"{year}-{month.zfill(2)}-01"  # Default to first day of month
                elif len(groups) == 3:
                    # Full date format: 2024-03-28 or 2025 05 06
                    year, month, day = groups
                    # Ensure month and day are zero-padded
                    month = month.zfill(2)
                    day = day.zfill(2)
                    return f"{year}-{month}-{day}"

        # Fallback: try to extract just year and use current month/day
        year_match = re.search(r'(\d{4})', filename)
        if year_match:
            year = year_match.group(1)
            current_date = datetime.now()
            return f"{year}-{current_date.month:02d}-{current_date.day:02d}"

        # Final fallback to current date in YYYY-MM-DD format
        return datetime.now().strftime('%Y-%m-%d')
    
    def _extract_kpi_metric_type(self, filename: str) -> str:
        """Extract metric type from KPI filename.
        
        Examples:
        - LTE_Access_KPIs (20190204-20190410).xlsx → lte_access
        - NR_Mobility_KPIs (20250504-20250510).xlsx → nr_mobility
        - UMTS_Handover_KPIs.xlsx → umts_handover
        """
        
        # Remove common suffixes and date patterns
        clean_name = re.sub(r'\s*\(\d{8}-\d{8}\)', '', filename)  # Remove date ranges
        clean_name = re.sub(r'_KPIs?$', '', clean_name, flags=re.IGNORECASE)  # Remove _KPI suffix
        clean_name = re.sub(r'\s*KPIs?\s*', '', clean_name, flags=re.IGNORECASE)  # Remove KPI word
        
        # Convert to lowercase and replace spaces/special chars with underscores
        metric_type = re.sub(r'[^\w]', '_', clean_name.lower())
        metric_type = re.sub(r'_+', '_', metric_type).strip('_')
        
        # Default if empty
        if not metric_type:
            metric_type = 'general'
            
        return metric_type
    
    def _extract_year_quarter_from_score_filename(self, filename: str) -> str:
        """Extract year and quarter from SCORE filename.
        
        Examples:
        - 20230207_shared_benchmark_ch_2023Q1_ch_campaign_01-FlatTable.xlsx → 2023Q1
        - 20240607_shared_benchmark_ch_2024Q2_ch_campaign_01-FlatTable.xlsx → 2024Q2
        """
        
        # Look for year-quarter pattern
        quarter_match = re.search(r'(\d{4})Q([1-4])', filename, re.IGNORECASE)
        if quarter_match:
            year, quarter = quarter_match.groups()
            return f"{year}Q{quarter}"
        
        # Fallback: extract year and default to Q1
        year_match = re.search(r'\b(20\d{2})\b', filename)
        if year_match:
            year = year_match.group(1)
            return f"{year}Q1"
        
        # Final fallback
        current_year = datetime.now().year
        return f"{current_year}Q1"
    
    def _extract_year_week_from_cfg_path(self, file_path: Path) -> Tuple[str, int]:
        """Extract year and week from CFG file path.
        
        Examples:
        - D:\\connect\\data\\input\\cfg\\2025\\CW05\\CFG_05_South\\file.tar.gz → (2025, 5)
        - D:\\connect\\data\\input\\cfg\\2025\\CW20\\CW20_West\\file.tar.gz → (2025, 20)
        """
        
        path_str = str(file_path)
        
        # Extract year from path
        year_match = re.search(r'\b(20\d{2})\b', path_str)
        year = year_match.group(1) if year_match else str(datetime.now().year)
        
        # Extract week from path (CW05, CW20, etc.)
        week_match = re.search(r'CW(\d{1,2})', path_str, re.IGNORECASE)
        week = int(week_match.group(1)) if week_match else 1
        
        return year, week
     
    def _extract_year_quarter_from_cdr_path(self, path_str: str, filename: str) -> Tuple[str, str]:
        """Extract year and quarter from CDR file path and filename.
        
        Examples:
        - D:\\connect\\data\\input\\cdr\\2022\\Q1\\Voice\\file.xlsx → (2022, 1)
        - D:\\connect\\data\\input\\cdr\\2024\\Q2\\SP\\file.xlsx → (2024, 2)
        """
        
        # First try to extract from path structure
        path_match = re.search(r'\\(20\d{2})\\Q([1-4])\\', path_str)
        if path_match:
            year, quarter = path_match.groups()
            return year, quarter
        
        # Fallback: try to extract from filename
        filename_match = re.search(r'Q([1-4]).*?(20\d{2})', filename)
        if filename_match:
            quarter, year = filename_match.groups()
            return year, quarter
        
        # Another pattern: year first, then quarter
        year_quarter_match = re.search(r'(20\d{2}).*?Q([1-4])', filename)
        if year_quarter_match:
            year, quarter = year_quarter_match.groups()
            return year, quarter
        
        # Final fallback
        year = self._extract_year_from_filename_or_path(path_str)
        return year, "1"  # Default to Q1
     
    def _extract_cdr_service_type(self, filename: str) -> str:
        """Extract service type from CDR filename.
        
        Examples:
        - Shared_Benchmark_Q1_DE_Voice_M2M_Calls_2022-04-01_14-00-54.xlsx → voice_m2m_calls
        - Shared_Benchmark_Q2_DE_2024_Conversational_App_SQ_Samples_2024-05-31_13-35-42.xlsx → conversational_app_sq_samples
        """
        
        # Remove common prefixes and suffixes
        clean_name = filename
        
        # Remove timestamp suffix (e.g., _2022-04-01_14-00-54)
        clean_name = re.sub(r'_\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}$', '', clean_name)
        
        # Remove shared benchmark prefix
        clean_name = re.sub(r'^Shared_Benchmark_', '', clean_name, flags=re.IGNORECASE)
        
        # Remove quarter and country patterns
        clean_name = re.sub(r'Q[1-4]_', '', clean_name, flags=re.IGNORECASE)
        clean_name = re.sub(r'_DE_', '_', clean_name, flags=re.IGNORECASE)
        clean_name = re.sub(r'^DE_', '', clean_name, flags=re.IGNORECASE)
        
        # Remove year patterns
        clean_name = re.sub(r'_?20\d{2}_?', '_', clean_name)
        
        # Convert to lowercase and clean up
        service_type = clean_name.lower()
        service_type = re.sub(r'[^\w]', '_', service_type)
        service_type = re.sub(r'_+', '_', service_type).strip('_')
        
        # Default if empty
        if not service_type:
            service_type = 'general'
            
        return service_type
      
    def _extract_ep_cell_type(self, filename: str) -> str:
        """Extract cell type from EP filename.
        
        Examples:
        - LTECELL_CW14.xlsx → lte
        - GSMCELL_CW33.xlsx → gsm
        - NRCELL_CW07.xlsx → nr
        - TEF_SITE_CW10.xlsx → site
        - TEF_Sites_&_Locations_CW26.xlsx → site
        - UMTSCELL_CW15.xlsx → umts
        """
        
        filename_upper = filename.upper()
        
        # Define cell type patterns
        cell_patterns = [
            (r'LTECELL', 'lte'),
            (r'GSMCELL', 'gsm'),
            (r'NRCELL', 'nr'),
            (r'UMTSCELL', 'umts'),
            (r'TEF_SITE', 'site'),
            (r'TEF_SITES', 'site'),
            (r'SITE', 'site'),
        ]
        
        for pattern, cell_type in cell_patterns:
            if re.search(pattern, filename_upper):
                return cell_type
        
        # Default fallback
        return 'general'
      
    def _extract_year_week_from_ep_path(self, path_str: str, filename: str) -> Tuple[str, int]:
        """Extract year and week from EP file path and filename.
        
        Examples:
        - D:\\connect\\data\\input\\ep\\2023\\CW14\\LTECELL_CW14.xlsx → (2023, 14)
        - D:\\connect\\data\\input\\ep\\2025\\CW07\\NRCELL_CW07.xlsx → (2025, 7)
        """
        
        # First try to extract from path structure
        path_match = re.search(r'\\(20\d{2})\\CW(\d{1,2})\\', path_str)
        if path_match:
            year, week = path_match.groups()
            return year, int(week)
        
        # Fallback: try to extract from filename
        filename_match = re.search(r'CW(\d{1,2})', filename, re.IGNORECASE)
        if filename_match:
            week = int(filename_match.group(1))
            # Extract year from path or filename
            year = self._extract_year_from_filename_or_path(path_str)
            return year, week
        
        # Final fallback
        year = self._extract_year_from_filename_or_path(path_str)
        return year, 1  # Default to week 1
      
    def _extract_date(self, filename: str) -> str:
        """Legacy method for backward compatibility - extract date in YYYYMMDD format.

        This method is kept for backward compatibility with existing code.
        For NLG data, use _extract_date_for_nlg instead.
        """
        # Use the new NLG method and convert back to YYYYMMDD
        date_str = self._extract_date_for_nlg(filename)
        # Convert YYYY-MM-DD back to YYYYMMDD
        return date_str.replace('-', '')
    
    def _clean_table_name(self, table_name: str) -> str:
        """Clean and standardize table name with enhanced special character handling.

        Handles special characters like & in TEF_Sites_&_Locations_CW26.xlsx
        and ensures PostgreSQL-compatible identifiers.
        Preserves quarter format (Q1, Q2, Q3, Q4) in CDR table names.
        Preserves date format (YYYY-MM-DD) in NLG table names.
        """

        # Preserve special formats before converting to lowercase
        # Match patterns like Q1, Q2, Q3, Q4 and temporarily replace them
        quarter_matches = re.findall(r'Q[1-4]', table_name)
        temp_table_name = table_name
        for i, quarter in enumerate(quarter_matches):
            temp_table_name = temp_table_name.replace(quarter, f'__QUARTER_{i}__', 1)
        
        # Preserve date format YYYY-MM-DD in NLG table names
        date_matches = re.findall(r'\d{4}-\d{2}-\d{2}', temp_table_name)
        for i, date in enumerate(date_matches):
            temp_table_name = temp_table_name.replace(date, f'__DATE_{i}__', 1)
        
        # Convert to lowercase
        table_name = temp_table_name.lower()
        
        # Restore quarter format
        for i, quarter in enumerate(quarter_matches):
            table_name = table_name.replace(f'__quarter_{i}__', quarter)
        
        # Restore date format
        for i, date in enumerate(date_matches):
            table_name = table_name.replace(f'__date_{i}__', date)

        # Handle specific special characters before general replacement
        special_char_replacements = {
            '&': 'and',      # TEF_Sites_&_Locations → TEF_Sites_and_Locations
            '+': 'plus',     # Handle plus signs
            '-': '_',        # Hyphens to underscores
            ' ': '_',        # Spaces to underscores
            '.': '_',        # Dots to underscores
        }

        for char, replacement in special_char_replacements.items():
            table_name = table_name.replace(char, replacement)

        # Replace any remaining special characters with underscores
        table_name = re.sub(r'[^\w]', '_', table_name)

        # Remove multiple consecutive underscores
        table_name = re.sub(r'_+', '_', table_name)

        # Remove leading/trailing underscores
        table_name = table_name.strip('_')

        # Ensure it doesn't start with a number
        if table_name and table_name[0].isdigit():
            table_name = f'tbl_{table_name}'

        # Ensure minimum length
        if len(table_name) < 3:
            table_name = f'tbl_{table_name}'

        # Limit length (PostgreSQL identifier limit is 63 characters)
        if len(table_name) > 60:
            # Try to preserve meaningful parts
            parts = table_name.split('_')
            if len(parts) > 1:
                # Keep first and last parts, truncate middle if needed
                first_part = parts[0]
                last_part = parts[-1]
                remaining_length = 60 - len(first_part) - len(last_part) - 2  # 2 underscores

                if remaining_length > 0 and len(parts) > 2:
                    middle_parts = '_'.join(parts[1:-1])
                    if len(middle_parts) > remaining_length:
                        middle_parts = middle_parts[:remaining_length]
                    table_name = f"{first_part}_{middle_parts}_{last_part}"
                else:
                    table_name = f"{first_part}_{last_part}"
            else:
                table_name = table_name[:60]

        return table_name


class OperatorDetector:
    """Detects operator information from Excel files and sheet names for CDR data routing."""
    
    def __init__(self, config: Dict):
        """Initialize with database configuration.
        
        Args:
            config: Database configuration containing operator mappings
        """
        self.config = config
        # Handle both dict and object config types
        if hasattr(config, 'get'):
            self.telecom_config = config.get('telecom_data_sources', {})
        elif hasattr(config, '__dict__'):
            config_dict = config.__dict__
            self.telecom_config = config_dict.get('telecom_data_sources', {})
        else:
            self.telecom_config = {}
            
        # Enhanced operator mappings for CDR data
        self.operator_mappings = {
            'telefonica': ['telefonica', 'tef', 'movistar', 'to2'],
            'vodafone': ['vodafone', 'vdf', 'vod'],
            'telekom': ['telekom', 'tdg', 'deutsche telekom', 'dt', 'magenta']
        }
        
        # Update with config if available
        config_mappings = self.telecom_config.get('operator_mappings', {})
        if config_mappings:
            self.operator_mappings.update(config_mappings)
    
    def detect_operator_from_sheet(self, sheet_name: str) -> Optional[str]:
        """Detect operator from Excel sheet name for CDR data routing.
        
        Schema mapping rules:
        - Contains "Telefonica" → cdr_to2 schema
        - Contains "Vodafone" → cdr_vdf schema  
        - Contains "Telekom" → cdr_tdg schema
        
        Args:
            sheet_name: Name of the Excel sheet
            
        Returns:
            Detected operator name or None
        """
        if not sheet_name:
            return None
            
        sheet_lower = sheet_name.lower()
        
        for operator, keywords in self.operator_mappings.items():
            for keyword in keywords:
                if keyword.lower() in sheet_lower:
                    logger.debug(f"Detected operator '{operator}' from sheet name '{sheet_name}' for CDR routing")
                    return operator
                    
        logger.debug(f"No operator detected from sheet name '{sheet_name}', will use default schema")
        return None
    
    def get_schema_for_operator(self, operator: str, data_type: str = 'cdr') -> str:
        """Get database schema name for operator and data type.
        
        CDR Schema mapping:
        - telefonica → cdr_to2
        - vodafone → cdr_vdf
        - telekom → cdr_tdg
        - default → cdr_to2
        
        Other data types use fixed schemas:
        - ep → ep_to2
        - nlg → nlg_to2
        - kpi → kpi_to2
        - score → score_to2
        - cfg → cfg_to2
        
        Args:
            operator: Operator name
            data_type: Type of data (default: 'cdr')
            
        Returns:
            Schema name
        """
        if data_type == 'cdr':
            # CDR data supports multiple schemas based on operator
            if not operator:
                return "cdr_to2"  # Default CDR schema
                
            schema_mappings = {
                'telefonica': "cdr_to2",
                'vodafone': "cdr_vdf", 
                'telekom': "cdr_tdg"
            }
            
            schema = schema_mappings.get(operator.lower(), "cdr_to2")
            logger.debug(f"Mapped CDR operator '{operator}' to schema '{schema}'")
            return schema
        else:
            # Other data types use fixed schemas
            schema = f"{data_type}_to2"
            logger.debug(f"Using fixed schema '{schema}' for data type '{data_type}'")
            return schema
