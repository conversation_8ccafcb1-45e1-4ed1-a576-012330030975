#!/usr/bin/env python3
"""
CFG (Configuration) data importer.

This module provides functionality for importing and processing CFG data files.
CFG files typically contain configuration data for telecommunications equipment and systems.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""

import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

import pandas as pd
from datetime import datetime

from .base import AbstractImporter, TelecomImportError, ImportResult

# Configure logging
logger = logging.getLogger(__name__)


class CFGImporter(AbstractImporter):
    """Configuration data importer."""

    def __init__(
        self,
        config: Optional[Dict[str, Any]] = None,
        db_session=None,
        performance_logger=None,
        **kwargs,
    ):
        """Initialize CFG importer.

        Args:
            config: Configuration dictionary
            db_session: Database session for direct database operations
            performance_logger: Logger for performance metrics
            **kwargs: Additional configuration options
        """
        self.name = "CFGImporter"
        self.supported_formats = ["csv", "xlsx", "xls"]
        self.config = config or {}
        self.db_session = db_session
        self.performance_logger = performance_logger

        # Initialize database components if session provided
        if db_session:
            from src.database.operations import BulkOperations
            from src.database.schema import SchemaManager

            self.schema_manager = SchemaManager(db_session)
            self.bulk_operations = BulkOperations(db_session)

        # Prepare config for AbstractImporter
        importer_config = {
            'name': self.name,
            'data_type': 'cfg',
            'supported_formats': self.supported_formats,
            'batch_size': self.config.get('batch_size', 2000),
            **kwargs
        }
        super().__init__(config=importer_config)

    def validate_file(self, file_path: Union[str, Path]) -> bool:
        """Validate CFG data file.

        Args:
            file_path: Path to the file

        Returns:
            bool: True if file is valid

        Raises:
            ImportError: If file is invalid
        """
        path = Path(file_path)

        if not path.exists():
            raise ImportError(f"File does not exist: {path}")

        if path.suffix.lower() not in [".csv", ".xlsx", ".xls"]:
            raise ImportError(f"Unsupported file format: {path.suffix}")

        return True

    def validate_data_structure(self, data: pd.DataFrame) -> Tuple[bool, List[str]]:
        """Validate CFG data structure.

        Args:
            data: DataFrame to validate

        Returns:
            Tuple[bool, List[str]]: Validation result and error messages
        """
        errors = []

        # Accept any column structure - no required columns validation

        # Check for empty DataFrame
        if data.empty:
            errors.append("Data file is empty")

        return len(errors) == 0, errors

    def validate_data_values(self, data: pd.DataFrame) -> Tuple[bool, List[str]]:
        """Validate CFG data values.

        Args:
            data: DataFrame to validate

        Returns:
            Tuple[bool, List[str]]: Validation result and error messages
        """
        errors = []

        # Skip required columns validation - accept any data structure

        # Validate CONFIG_ID uniqueness
        if "CONFIG_ID" in data.columns:
            duplicate_count = data["CONFIG_ID"].duplicated().sum()
            if duplicate_count > 0:
                errors.append(f"CONFIG_ID column has {duplicate_count} duplicate values")

        # Validate CONFIG_TYPE values
        if "CONFIG_TYPE" in data.columns:
            valid_types = ["STRING", "INTEGER", "FLOAT", "BOOLEAN", "JSON", "XML"]
            invalid_types = data[~data["CONFIG_TYPE"].isin(valid_types)]
            if not invalid_types.empty:
                errors.append(f"CONFIG_TYPE column has invalid values. Valid types: {valid_types}")

        return len(errors) == 0, errors

    def preprocess_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Preprocess CFG data before import.

        Args:
            data: Raw DataFrame

        Returns:
            pd.DataFrame: Preprocessed DataFrame
        """
        processed_data = data.copy()

        # Normalize column names
        processed_data.columns = processed_data.columns.str.upper().str.strip()

        # Normalize CONFIG_TYPE values
        if "CONFIG_TYPE" in processed_data.columns:
            processed_data["CONFIG_TYPE"] = processed_data["CONFIG_TYPE"].str.upper().str.strip()

        # Add metadata columns
        processed_data["IMPORT_TIMESTAMP"] = datetime.now()
        processed_data["SOURCE_FILE"] = self.source_path.name
        processed_data["LAST_UPDATED"] = datetime.now()

        # Remove rows with critical null values (only for columns that exist)
        required_columns = ["CONFIG_ID", "CONFIG_NAME", "CONFIG_VALUE", "CONFIG_TYPE"]
        existing_required_columns = [col for col in required_columns if col in processed_data.columns]

        if existing_required_columns:
            processed_data = processed_data.dropna(subset=existing_required_columns)
        else:
            # If none of the expected columns exist, try to work with whatever columns are available
            # Remove rows where all values are null
            processed_data = processed_data.dropna(how='all')

        # Remove duplicate CONFIG_IDs (keep first occurrence)
        processed_data = processed_data.drop_duplicates(subset=["CONFIG_ID"], keep="first")

        return processed_data

    def get_table_name(self, filename: str) -> str:
        """Generate table name for CFG data using enhanced naming conventions.

        Args:
            filename: Source filename or full path

        Returns:
            str: Table name following pattern cfg_{year}_cw{week}
        """
        try:
            from src.database.utils.table_naming import TableNamingManager

            # Get configuration
            config = getattr(self, 'config', {})

            naming_manager = TableNamingManager(config)

            # Convert filename to Path object (handle both filename and full path)
            file_path = Path(filename)

            # Generate table name using standardized logic
            # Pattern: cfg_{year}_cw{week}
            table_name = naming_manager.generate_table_name(
                data_type="cfg",
                file_path=file_path
            )

            self.logger.info(f"Generated CFG table name '{table_name}' for file '{filename}'")
            return table_name

        except Exception as e:
            self.logger.warning(f"Failed to generate table name using pattern, falling back to simple naming: {e}")
            return self._generate_cfg_table_name_fallback(filename)

    def _generate_cfg_table_name_fallback(self, filename: str) -> str:
        """Fallback CFG table name generation with enhanced logic."""
        import re
        from pathlib import Path
        from datetime import datetime

        file_path = Path(filename)
        path_str = str(file_path)

        # Extract year from path
        year_match = re.search(r'\b(20\d{2})\b', path_str)
        year = year_match.group(1) if year_match else str(datetime.now().year)

        # Extract week from path (CW05, CW20, etc.)
        week_match = re.search(r'CW(\d{1,2})', path_str, re.IGNORECASE)
        week = int(week_match.group(1)) if week_match else 1

        table_name = f"cfg_{year}_cw{week:02d}"
        self.logger.info(f"Using fallback CFG table name: '{table_name}'")
        return table_name

    def _get_legacy_table_name(self, filename: str) -> str:
        """Legacy table name generation for backward compatibility."""
        # Use pattern from config or default
        pattern = getattr(self.config, 'table_name_pattern', 'cfg_{filename}')

        # Clean filename (remove extension and special characters)
        clean_filename = Path(filename).stem.lower()
        clean_filename = "".join(c if c.isalnum() or c == "_" else "_" for c in clean_filename)

        raw_table_name = pattern.format(filename=clean_filename)

        # Validate and fix table name to meet PostgreSQL requirements
        from src.database.utils.validators import InputValidator
        return InputValidator.validate_and_fix_table_name(raw_table_name)

    def get_schema_name(self) -> str:
        """Get schema name for CFG data.

        Returns:
            str: Schema name
        """
        return getattr(self.config, 'schema_name', 'cfg_to2')

    async def import_data(self, source_path: Union[str, Path] = None, **kwargs) -> ImportResult:
        """Import CFG data from source file.

        Args:
            source_path: Path to the source file
            **kwargs: Additional import options

        Returns:
            ImportResult: Result of the import operation

        Raises:
            ImportError: If import fails
        """
        # Set source_path from parameter or use existing
        if source_path:
            from pathlib import Path
            self.source_path = Path(source_path)
        elif not hasattr(self, 'source_path') or not self.source_path:
            raise TelecomImportError("No source path provided")

        start_time = datetime.now()

        try:
            logger.info(f"Starting CFG import from {self.source_path}")

            # Validate source file
            self.validate_file(self.source_path)

            # Read data based on file format with configuration-driven approach
            if self.source_path.suffix.lower() == ".csv":
                # Use configuration-driven CSV structure detection
                structure = self._detect_csv_structure_with_config(self.source_path, 'cfg')
                self.logger.info(f"Using CFG CSV structure: {structure}")

                # Enhanced CSV reading with configuration-based structure detection
                import csv
                data = pd.read_csv(
                    self.source_path,
                    encoding=structure['encoding'],
                    delimiter=structure['delimiter'],
                    skiprows=structure['skip_rows'],
                    header=structure['header_row'],  # Use configured header row
                    engine='python',  # More flexible parser
                    on_bad_lines='skip',  # Skip problematic lines
                    quoting=csv.QUOTE_MINIMAL,
                    skipinitialspace=True,
                    comment='#'  # Skip lines starting with #
                )
            else:
                data = pd.read_excel(self.source_path)

            logger.info(f"Read {len(data)} records from {self.source_path}")

            # Validate data structure
            is_valid, structure_errors = self.validate_data_structure(data)
            if not is_valid:
                raise ImportError(f"Data structure validation failed: {structure_errors}")

            # Validate data values
            is_valid, value_errors = self.validate_data_values(data)
            if not is_valid:
                logger.warning(f"Data value validation warnings: {value_errors}")

            # Preprocess data
            processed_data = self.preprocess_data(data)
            logger.info(f"Preprocessed data: {len(processed_data)} valid records")

            if processed_data.empty:
                raise ImportError("No valid records after preprocessing")

            # Import to database if session available
            records_imported = 0
            if self.db_session and self.bulk_operations:
                table_name = self.get_table_name(self.source_path.name)
                schema_name = self.get_schema_name()

                # Convert DataFrame to records
                records = processed_data.to_dict("records")

                # Bulk insert
                result = await self.bulk_operations.bulk_insert(
                    table_name=table_name,
                    data=records,
                    schema=schema_name,
                    batch_size=kwargs.get("batch_size", 1000)
                )

                records_imported = result.successful
                logger.info(f"Imported {records_imported} CFG records to {schema_name}.{table_name}")
            else:
                records_imported = len(processed_data)
                logger.info("Database session not available, skipping database import")

            # Create result using new ImportResult format
            from .base import ImportStatus, ImportMetrics

            processing_time = (datetime.now() - start_time).total_seconds()
            metrics = ImportMetrics(
                start_time=start_time,
                end_time=datetime.now(),
                records_processed=len(processed_data),
                processing_time_seconds=processing_time
            )

            import_result = ImportResult(
                status=ImportStatus.COMPLETED,
                metrics=metrics,
                source_info={
                    "path": str(self.source_path),
                    "size_bytes": self.source_path.stat().st_size,
                    "data_type": "CFG"
                },
                validation_results={
                    "original_records": len(data),
                    "processed_records": len(processed_data),
                    "table_name": self.get_table_name(self.source_path.name) if self.db_session else None,
                    "schema_name": self.get_schema_name() if self.db_session else None,
                }
            )

            logger.info(f"CFG import completed successfully in {import_result.metadata['processing_time_seconds']:.2f} seconds")
            return import_result

        except Exception as e:
            error_msg = f"CFG import failed: {str(e)}"
            logger.error(error_msg)

            # Create error result using new ImportResult format
            from .base import ImportStatus, ImportMetrics

            processing_time = (datetime.now() - start_time).total_seconds()
            metrics = ImportMetrics(
                start_time=start_time,
                end_time=datetime.now(),
                records_processed=0,
                processing_time_seconds=processing_time,
                errors=[error_msg]
            )

            return ImportResult(
                status=ImportStatus.FAILED,
                error_message=error_msg,
                metrics=metrics,
                source_info={
                    "path": str(self.source_path),
                    "size_bytes": self.source_path.stat().st_size if self.source_path.exists() else 0,
                    "data_type": "CFG"
                }
            )

    def get_sample_data(self, num_rows: int = 5) -> Optional[pd.DataFrame]:
        """Get sample data from the source file.

        Args:
            num_rows: Number of rows to sample

        Returns:
            Optional[pd.DataFrame]: Sample data or None if error
        """
        try:
            if self.source_path.suffix.lower() == ".csv":
                return pd.read_csv(self.source_path, nrows=num_rows)
            else:
                return pd.read_excel(self.source_path, nrows=num_rows)
        except Exception as e:
            logger.error(f"Failed to get sample data: {e}")
            return None

    def get_file_info(self) -> Dict[str, Any]:
        """Get information about the source file.

        Returns:
            Dict[str, Any]: File information
        """
        try:
            stat = self.source_path.stat()
            return {
                "filename": self.source_path.name,
                "file_size_bytes": stat.st_size,
                "file_size_mb": round(stat.st_size / (1024 * 1024), 2),
                "modified_time": datetime.fromtimestamp(stat.st_mtime),
                "file_format": self.source_path.suffix.lower(),
                "supported": self.source_path.suffix.lower() in [".csv", ".xlsx", ".xls"],
            }
        except Exception as e:
            logger.error(f"Failed to get file info: {e}")
            return {"error": str(e)}

    def get_source_info(self) -> Dict[str, Any]:
        """Get source information for the importer."""
        return {
            'source_path': str(self.source_path) if self.source_path else None,
            'data_type': 'cfg',
            'supported_formats': self.supported_formats
        }

    async def validate_source(self, source_path: str = None) -> bool:
        """Validate the source file."""
        try:
            from pathlib import Path

            path = Path(source_path or self.source_path)

            # Check if file exists
            if not path.exists():
                return False

            # Check file extension
            if path.suffix.lower() not in ['.xlsx', '.xls']:
                return False

            return True

        except Exception:
            return False